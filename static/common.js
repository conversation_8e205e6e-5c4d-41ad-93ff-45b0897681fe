/**
 * 发卡商城 - 全局通用工具库
 * 统一数据处理、UI组件和API调用
 */

// 全局配置
const CONFIG = {
    API_BASE: 'http://localhost:5000/api',
    FRONTEND_BASE: 'http://localhost:8080/frontend',
    CURRENCY_SYMBOL: '¥',
    DEFAULT_AVATAR: 'https://picsum.photos/100/100?random=avatar'
};

// 数据格式化工具
const DataFormatter = {
    // 格式化价格显示
    formatPrice(price) {
        if (typeof price !== 'number') {
            price = parseFloat(price) || 0;
        }
        return `${CONFIG.CURRENCY_SYMBOL}${price.toFixed(2)}`;
    },
    
    // 格式化日期
    formatDate(dateString) {
        if (!dateString) return '未知时间';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN');
    },
    
    // 格式化库存状态
    formatStock(stock) {
        const num = parseInt(stock) || 0;
        if (num <= 0) return '缺货';
        if (num <= 10) return `库存紧张 (${num})`;
        return `库存充足 (${num})`;
    },
    
    // 格式化订单状态
    formatOrderStatus(status) {
        const statusMap = {
            'pending': '待处理',
            'processing': '处理中',
            'shipped': '已发货',
            'delivered': '已完成',
            'cancelled': '已取消',
            'refunded': '已退款'
        };
        return statusMap[status] || status;
    }
};

// API调用工具
const API = {
    // 通用请求方法
    async request(endpoint, options = {}) {
        const url = `${CONFIG.API_BASE}${endpoint}`;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'include' // 包含cookies
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        
        try {
            const response = await fetch(url, finalOptions);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error || `HTTP ${response.status}`);
            }
            
            return data;
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    },
    
    // 获取产品列表
    async getProducts(params = {}) {
        const query = new URLSearchParams(params).toString();
        const endpoint = query ? `/products?${query}` : '/products';
        return await this.request(endpoint);
    },
    
    // 获取单个产品
    async getProduct(id) {
        return await this.request(`/products/${id}`);
    },
    
    // 创建产品
    async createProduct(productData) {
        return await this.request('/products', {
            method: 'POST',
            body: JSON.stringify(productData)
        });
    },
    
    // 更新产品
    async updateProduct(id, productData) {
        return await this.request(`/products/${id}`, {
            method: 'PUT',
            body: JSON.stringify(productData)
        });
    },
    
    // 删除产品
    async deleteProduct(id) {
        return await this.request(`/products/${id}`, {
            method: 'DELETE'
        });
    },
    
    // 用户登录
    async login(credentials) {
        return await this.request('/auth/login', {
            method: 'POST',
            body: JSON.stringify(credentials)
        });
    },
    
    // 用户注册
    async register(userData) {
        return await this.request('/auth/register', {
            method: 'POST',
            body: JSON.stringify(userData)
        });
    },
    
    // 获取订单列表
    async getOrders(params = {}) {
        const query = new URLSearchParams(params).toString();
        const endpoint = query ? `/orders?${query}` : '/orders';
        return await this.request(endpoint);
    },
    
    // 创建订单
    async createOrder(orderData) {
        return await this.request('/orders', {
            method: 'POST',
            body: JSON.stringify(orderData)
        });
    },

    // 获取当前用户信息
    async getCurrentUser() {
        return await this.request('/auth/me');
    },

    // 用户登出
    async logout() {
        return await this.request('/auth/logout', {
            method: 'POST'
        });
    }
};

// 购物车管理
const Cart = {
    // 获取购物车数据
    get() {
        try {
            return JSON.parse(localStorage.getItem('cart') || '[]');
        } catch {
            return [];
        }
    },
    
    // 保存购物车数据
    save(cartData) {
        localStorage.setItem('cart', JSON.stringify(cartData));
        this.updateCartCount();
    },
    
    // 添加商品到购物车
    add(product, quantity = 1) {
        const cart = this.get();
        const existingItem = cart.find(item => item.product_id === product.id);
        
        if (existingItem) {
            existingItem.quantity += quantity;
        } else {
            cart.push({
                product_id: product.id,
                name: product.name,
                price: product.price,
                quantity: quantity,
                image: `https://picsum.photos/100/100?random=${product.id}`
            });
        }
        
        this.save(cart);
        this.showAddToCartToast(product.name);
    },
    
    // 移除商品
    remove(productId) {
        const cart = this.get();
        const newCart = cart.filter(item => item.product_id !== productId);
        this.save(newCart);
    },
    
    // 更新商品数量
    updateQuantity(productId, quantity) {
        const cart = this.get();
        const item = cart.find(item => item.product_id === productId);
        if (item) {
            if (quantity <= 0) {
                this.remove(productId);
            } else {
                item.quantity = quantity;
                this.save(cart);
            }
        }
    },
    
    // 清空购物车
    clear() {
        this.save([]);
    },
    
    // 获取购物车总价
    getTotal() {
        const cart = this.get();
        return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
    },
    
    // 获取购物车商品总数
    getCount() {
        const cart = this.get();
        return cart.reduce((count, item) => count + item.quantity, 0);
    },
    
    // 更新页面上的购物车数量显示
    updateCartCount() {
        const countElements = document.querySelectorAll('.cart-count');
        const count = this.getCount();
        countElements.forEach(el => {
            el.textContent = count;
            el.style.display = count > 0 ? 'inline' : 'none';
        });
    },
    
    // 显示添加到购物车的提示
    showAddToCartToast(productName) {
        UI.showToast(`已添加到购物车: ${productName}`, 'success');
    }
};

// UI工具
const UI = {
    // 显示Toast通知
    showToast(message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <span class="toast-icon">${this.getToastIcon(type)}</span>
                <span class="toast-message">${message}</span>
            </div>
        `;
        
        document.body.appendChild(toast);
        
        // 触发动画
        setTimeout(() => toast.classList.add('show'), 10);
        
        // 自动移除
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, duration);
    },
    
    // 获取Toast图标
    getToastIcon(type) {
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };
        return icons[type] || icons.info;
    },
    
    // 显示加载状态
    showLoading(container, message = '加载中...') {
        if (typeof container === 'string') {
            container = document.querySelector(container);
        }
        if (container) {
            container.innerHTML = `
                <div class="loading-state">
                    <div class="spinner"></div>
                    <span>${message}</span>
                </div>
            `;
        }
    },
    
    // 显示错误状态
    showError(container, message = '加载失败', retryCallback = null) {
        if (typeof container === 'string') {
            container = document.querySelector(container);
        }
        if (container) {
            container.innerHTML = `
                <div class="error-state">
                    <div class="error-icon">❌</div>
                    <p>${message}</p>
                    ${retryCallback ? '<button class="btn btn-primary" onclick="' + retryCallback + '">重试</button>' : ''}
                </div>
            `;
        }
    },
    
    // 显示空状态
    showEmpty(container, message = '暂无数据') {
        if (typeof container === 'string') {
            container = document.querySelector(container);
        }
        if (container) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📭</div>
                    <p>${message}</p>
                </div>
            `;
        }
    }
};

// 页面初始化
document.addEventListener('DOMContentLoaded', () => {
    // 更新购物车数量显示
    Cart.updateCartCount();
    
    // 添加全局样式
    if (!document.querySelector('#global-toast-styles')) {
        const style = document.createElement('style');
        style.id = 'global-toast-styles';
        style.textContent = `
            .toast {
                position: fixed;
                bottom: 20px;
                right: 20px;
                padding: 12px 16px;
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1000;
                transform: translateY(100px);
                opacity: 0;
                transition: all 0.3s ease;
                max-width: 300px;
            }
            .toast.show {
                transform: translateY(0);
                opacity: 1;
            }
            .toast-success { background: #10b981; color: white; }
            .toast-error { background: #ef4444; color: white; }
            .toast-warning { background: #f59e0b; color: white; }
            .toast-info { background: #3b82f6; color: white; }
            .toast-content {
                display: flex;
                align-items: center;
                gap: 8px;
            }
            .loading-state, .error-state, .empty-state {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 2rem;
                text-align: center;
                color: #6b7280;
            }
            .spinner {
                width: 24px;
                height: 24px;
                border: 2px solid #e5e7eb;
                border-top: 2px solid #3b82f6;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin-bottom: 8px;
            }
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            .error-icon, .empty-icon {
                font-size: 2rem;
                margin-bottom: 8px;
            }
        `;
        document.head.appendChild(style);
    }
});

// 导出全局对象
window.FakaMall = {
    CONFIG,
    DataFormatter,
    API,
    Cart,
    UI
};
