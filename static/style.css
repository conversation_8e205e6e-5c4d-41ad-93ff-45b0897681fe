/* static/style.css */

:root {
  --primary: #4f46e5;
  --primary-hover: #4338ca;
  --secondary: #6b7280;
  --success: #10b981;
  --danger: #ef4444;
  --warning: #f59e0b;
  --light: #f9fafb;
  --dark: #111827;
  
  /* 新增变量 */
  --primary-light: rgba(79, 70, 229, 0.1);
  --success-light: rgba(16, 185, 129, 0.1);
  --danger-light: rgba(239, 68, 68, 0.1);
  --warning-light: rgba(245, 158, 11, 0.1);
  
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  --transition-fast: 0.15s ease;
  --transition: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  --border-radius-sm: 0.25rem;
  --border-radius: 0.375rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
    Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

body {
  background-color: #f3f4f6;
  color: var(--dark);
  line-height: 1.5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

header {
  background-color: white;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 1rem;
}

.logo {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.nav-links {
  display: flex;
  gap: 1.5rem;
}

.nav-link {
  color: var(--dark);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s;
  position: relative;
}

.nav-link:hover {
  color: var(--primary);
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--primary);
}

.container {
  flex: 1;
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 1rem;
  width: 100%;
}

.page-title {
  font-size: 1.75rem;
  margin-bottom: 1.5rem;
  color: var(--dark);
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.product-card {
  background-color: white;
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: var(--shadow);
  transition: all var(--transition);
  display: flex;
  flex-direction: column;
  position: relative;
  border: 1px solid rgba(0,0,0,0.05);
}

.product-card:hover {
  transform: translateY(-6px);
  box-shadow: var(--shadow-lg);
}

.product-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: var(--border-radius-md);
  box-shadow: 0 0 0 2px var(--primary-light);
  opacity: 0;
  transition: opacity var(--transition);
  pointer-events: none;
}

.product-card:hover::after {
  opacity: 1;
}

.product-image {
  width: 100%;
  height: 180px;
  object-fit: cover;
}

.product-content {
  padding: 1.25rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--dark);
}

.product-desc {
  font-size: 0.875rem;
  color: var(--secondary);
  margin-bottom: 1rem;
  flex: 1;
}

.product-price {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--danger);
  margin-bottom: 1rem;
}

.product-actions {
  display: flex;
  gap: 0.5rem;
}

.btn {
  padding: 0.625rem 1.25rem;
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition);
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
  font-size: 0.95rem;
  letter-spacing: 0.025em;
  box-shadow: var(--shadow-sm);
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateX(-100%);
  transition: transform var(--transition-fast);
}

.btn:hover::before {
  transform: translateX(0);
}

.btn:active {
  transform: translateY(1px);
}

.btn-primary {
  background-color: var(--primary);
  color: white;
  box-shadow: 0 1px 3px rgba(79, 70, 229, 0.4);
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  box-shadow: 0 4px 6px rgba(79, 70, 229, 0.5);
}

.btn-secondary {
  background-color: var(--secondary);
  color: white;
  box-shadow: 0 1px 3px rgba(107, 114, 128, 0.4);
}

.btn-secondary:hover {
  background-color: #4b5563;
  box-shadow: 0 4px 6px rgba(107, 114, 128, 0.5);
}

.btn-success {
  background-color: var(--success);
  color: white;
  box-shadow: 0 1px 3px rgba(16, 185, 129, 0.4);
}

.btn-success:hover {
  background-color: #059669;
  box-shadow: 0 4px 6px rgba(16, 185, 129, 0.5);
}

.btn-danger {
  background-color: var(--danger);
  color: white;
  box-shadow: 0 1px 3px rgba(239, 68, 68, 0.4);
}

.btn-danger:hover {
  background-color: #dc2626;
  box-shadow: 0 4px 6px rgba(239, 68, 68, 0.5);
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--primary);
  color: var(--primary);
  box-shadow: none;
}

.btn-outline:hover {
  background-color: var(--primary-light);
  border-color: var(--primary-hover);
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1.125rem;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

.spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid rgba(79, 70, 229, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

footer {
  background-color: var(--dark);
  color: white;
  padding: 1.5rem;
  text-align: center;
  margin-top: auto;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
}

/* Responsive design */
@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    gap: 1rem;
  }
  
  .nav-links {
    gap: 1rem;
  }
  
  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }
}

@media (max-width: 480px) {
  .product-grid {
    grid-template-columns: 1fr;
  }
  
  .product-actions {
    flex-direction: column;
  }
}

.sidebar {
  width: 220px;
  background-color: #2f3542;
  color: #fff;
  padding: 20px;
  height: 100vh;
  position: fixed;
}

.sidebar h2 {
  font-size: 18px;
  margin-bottom: 30px;
  text-align: center;
}

.sidebar ul {
  list-style: none;
}

.sidebar ul li {
  margin: 15px 0;
}

.sidebar ul li a {
  color: #dcdde1;
  text-decoration: none;
  display: block;
  padding: 10px;
  border-radius: 4px;
}

.sidebar ul li a:hover {
  background-color: #57606f;
}

.main {
  margin-left: 220px;
  padding: 20px;
  width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h1 {
  font-size: 20px;
  color: #333;
}

.user-info {
  font-size: 14px;
  color: #666;
}

.card {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
  margin-bottom: 20px;
}

table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

table th, table td {
  padding: 12px;
  border-bottom: 1px solid #ddd;
  text-align: left;
}

table th {
  background-color: #f1f2f6;
}

.status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #fff;
}

.status-success {
  background-color: #4cd137;
}

.status-pending {
  background-color: #f1c40f;
}