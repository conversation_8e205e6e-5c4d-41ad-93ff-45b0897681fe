/* static/style.css */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Microsoft YaHei", sans-serif;
}

body {
  background-color: #f8f9fa;
  color: #333;
  line-height: 1.6;
}

header {
  background-color: #ffffff;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

header h1 {
  font-size: 24px;
  color: #333;
}

nav {
  margin-top: 10px;
}

nav a {
  margin: 0 15px;
  text-decoration: none;
  color: #007bff;
  font-weight: bold;
}

.container {
  max-width: 1200px;
  margin: 30px auto;
  padding: 0 20px;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.product-card {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  transition: transform 0.2s;
}

.product-card:hover {
  transform: translateY(-5px);
}

.product-title {
  font-size: 18px;
  margin-bottom: 10px;
  color: #333;
}

.product-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

.product-price {
  font-size: 16px;
  color: #e63946;
  font-weight: bold;
}

button {
  padding: 8px 16px;
  background-color: #007bff;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background-color: #0056b3;
}

footer {
  margin-top: 50px;
  text-align: center;
  padding: 20px;
  background-color: #f1f1f1;
  color: #999;
  font-size: 13px;
}

.sidebar {
  width: 220px;
  background-color: #2f3542;
  color: #fff;
  padding: 20px;
  height: 100vh;
  position: fixed;
}

.sidebar h2 {
  font-size: 18px;
  margin-bottom: 30px;
  text-align: center;
}

.sidebar ul {
  list-style: none;
}

.sidebar ul li {
  margin: 15px 0;
}

.sidebar ul li a {
  color: #dcdde1;
  text-decoration: none;
  display: block;
  padding: 10px;
  border-radius: 4px;
}

.sidebar ul li a:hover {
  background-color: #57606f;
}

.main {
  margin-left: 220px;
  padding: 20px;
  width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h1 {
  font-size: 20px;
  color: #333;
}

.user-info {
  font-size: 14px;
  color: #666;
}

.card {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
  margin-bottom: 20px;
}

table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

table th, table td {
  padding: 12px;
  border-bottom: 1px solid #ddd;
  text-align: left;
}

table th {
  background-color: #f1f2f6;
}

.status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #fff;
}

.status-success {
  background-color: #4cd137;
}

.status-pending {
  background-color: #f1c40f;
}