/**
 * 统一的Header组件
 */

function createHeader(options = {}) {
    const {
        title = '发卡商城',
        showCart = true,
        showAdmin = false,
        currentPage = 'home'
    } = options;

    return `
        <header class="site-header">
            <div class="header-container">
                <a href="/frontend/index.html" class="logo">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                        <path d="M3 9h18M9 21V9"></path>
                    </svg>
                    <span>${title}</span>
                </a>
                
                <nav class="nav-links">
                    <a href="/frontend/index.html" class="nav-link ${currentPage === 'home' ? 'active' : ''}">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                            <polyline points="9,22 9,12 15,12 15,22"></polyline>
                        </svg>
                        首页
                    </a>
                    
                    ${showCart ? `
                        <a href="/frontend/cart.html" class="nav-link ${currentPage === 'cart' ? 'active' : ''}" id="cart-link">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="9" cy="21" r="1"></circle>
                                <circle cx="20" cy="21" r="1"></circle>
                                <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                            </svg>
                            购物车
                            <span class="cart-count">0</span>
                        </a>
                    ` : ''}
                    
                    ${showAdmin ? `
                        <a href="/frontend/admin/login.html" class="nav-link admin-link">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                            </svg>
                            管理后台
                        </a>
                    ` : ''}
                </nav>
            </div>
        </header>
    `;
}

// 初始化Header
function initHeader(options = {}) {
    const headerHTML = createHeader(options);
    
    // 如果页面已有header，替换它
    const existingHeader = document.querySelector('header');
    if (existingHeader) {
        existingHeader.outerHTML = headerHTML;
    } else {
        // 否则插入到body开头
        document.body.insertAdjacentHTML('afterbegin', headerHTML);
    }
    
    // 更新购物车数量
    if (window.FakaMall && window.FakaMall.Cart) {
        window.FakaMall.Cart.updateCartCount();
    }
}

// 导出
window.HeaderComponent = {
    create: createHeader,
    init: initHeader
};
