#!/bin/bash

# 青云小铺自动部署脚本 - Rocky Linux
# 作者: 青云小铺开发团队
# 版本: 1.0.0
# 适用系统: Rocky Linux 8/9

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 检查系统版本
check_system() {
    log_step "检查系统版本..."
    
    if [[ -f /etc/rocky-release ]]; then
        ROCKY_VERSION=$(cat /etc/rocky-release | grep -oE '[0-9]+\.[0-9]+')
        log_info "检测到 Rocky Linux $ROCKY_VERSION"
    else
        log_error "此脚本仅支持 Rocky Linux 系统"
        exit 1
    fi
}

# 更新系统
update_system() {
    log_step "更新系统包..."
    dnf update -y
    dnf install -y epel-release
    dnf groupinstall -y "Development Tools"
}

# 安装Python 3.9+
install_python() {
    log_step "安装Python 3.9+..."
    
    # 检查Python版本
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
        if [[ $(echo "$PYTHON_VERSION >= 3.9" | bc -l) -eq 1 ]]; then
            log_info "Python $PYTHON_VERSION 已安装"
            return
        fi
    fi
    
    dnf install -y python39 python39-pip python39-devel
    alternatives --install /usr/bin/python3 python3 /usr/bin/python3.9 1
    alternatives --install /usr/bin/pip3 pip3 /usr/bin/pip3.9 1
}

# 安装Node.js
install_nodejs() {
    log_step "安装Node.js..."
    
    if command -v node &> /dev/null; then
        log_info "Node.js 已安装: $(node --version)"
        return
    fi
    
    # 安装Node.js 18 LTS
    curl -fsSL https://rpm.nodesource.com/setup_18.x | bash -
    dnf install -y nodejs
}

# 安装Nginx
install_nginx() {
    log_step "安装Nginx..."
    
    dnf install -y nginx
    systemctl enable nginx
}

# 安装系统监控工具
install_monitoring() {
    log_step "安装系统监控工具..."
    
    dnf install -y htop iotop nethogs
    pip3 install psutil
}

# 创建应用用户
create_app_user() {
    log_step "创建应用用户..."
    
    if id "qingyun" &>/dev/null; then
        log_info "用户 qingyun 已存在"
    else
        useradd -m -s /bin/bash qingyun
        log_info "已创建用户 qingyun"
    fi
}

# 部署应用
deploy_app() {
    log_step "部署青云小铺应用..."
    
    APP_DIR="/opt/qingyun-shop"
    
    # 创建应用目录
    mkdir -p $APP_DIR
    
    # 复制应用文件
    if [[ -d "./backend" && -d "./frontend" ]]; then
        cp -r ./backend $APP_DIR/
        cp -r ./frontend $APP_DIR/
        cp -r ./icon $APP_DIR/
        cp ./demo.html $APP_DIR/
        cp ./status_optimized.html $APP_DIR/
        
        # 设置权限
        chown -R qingyun:qingyun $APP_DIR
        chmod +x $APP_DIR/backend/start.sh
        
        log_info "应用文件已复制到 $APP_DIR"
    else
        log_error "未找到应用文件，请确保在项目根目录运行此脚本"
        exit 1
    fi
}

# 安装Python依赖
install_python_deps() {
    log_step "安装Python依赖..."
    
    cd /opt/qingyun-shop/backend
    
    # 创建虚拟环境
    sudo -u qingyun python3 -m venv venv
    sudo -u qingyun ./venv/bin/pip install --upgrade pip
    sudo -u qingyun ./venv/bin/pip install -r requirements.txt
    
    log_info "Python依赖安装完成"
}

# 配置Nginx
configure_nginx() {
    log_step "配置Nginx..."
    
    cat > /etc/nginx/conf.d/qingyun-shop.conf << 'EOF'
server {
    listen 80;
    server_name _;
    
    # 前端静态文件
    location / {
        root /opt/qingyun-shop;
        index demo.html;
        try_files $uri $uri/ =404;
    }
    
    # 后端API代理
    location /api/ {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        root /opt/qingyun-shop;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF
    
    # 测试Nginx配置
    nginx -t
    systemctl restart nginx
    
    log_info "Nginx配置完成"
}

# 创建systemd服务
create_systemd_service() {
    log_step "创建systemd服务..."
    
    cat > /etc/systemd/system/qingyun-shop.service << 'EOF'
[Unit]
Description=青云小铺 - 虚拟商品交易平台
After=network.target

[Service]
Type=simple
User=qingyun
Group=qingyun
WorkingDirectory=/opt/qingyun-shop/backend
Environment=PATH=/opt/qingyun-shop/backend/venv/bin
ExecStart=/opt/qingyun-shop/backend/venv/bin/python app.py
Restart=always
RestartSec=3
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    # 重新加载systemd配置
    systemctl daemon-reload
    systemctl enable qingyun-shop
    
    log_info "systemd服务已创建"
}

# 配置防火墙
configure_firewall() {
    log_step "配置防火墙..."
    
    if systemctl is-active --quiet firewalld; then
        firewall-cmd --permanent --add-service=http
        firewall-cmd --permanent --add-service=https
        firewall-cmd --permanent --add-port=5000/tcp
        firewall-cmd --reload
        log_info "防火墙规则已配置"
    else
        log_warn "firewalld未运行，跳过防火墙配置"
    fi
}

# 初始化数据库
init_database() {
    log_step "初始化数据库..."
    
    cd /opt/qingyun-shop/backend
    sudo -u qingyun ./venv/bin/python -c "
from models import init_db
init_db()
print('数据库初始化完成')
"
    
    log_info "数据库初始化完成"
}

# 启动服务
start_services() {
    log_step "启动服务..."
    
    systemctl start qingyun-shop
    systemctl start nginx
    
    # 检查服务状态
    if systemctl is-active --quiet qingyun-shop; then
        log_info "青云小铺后端服务启动成功"
    else
        log_error "青云小铺后端服务启动失败"
        systemctl status qingyun-shop
    fi
    
    if systemctl is-active --quiet nginx; then
        log_info "Nginx服务启动成功"
    else
        log_error "Nginx服务启动失败"
        systemctl status nginx
    fi
}

# 显示部署信息
show_deployment_info() {
    log_step "部署完成！"
    
    SERVER_IP=$(hostname -I | awk '{print $1}')
    
    echo ""
    echo "=========================================="
    echo "🎉 青云小铺部署成功！"
    echo "=========================================="
    echo ""
    echo "📱 访问地址:"
    echo "   商城首页: http://$SERVER_IP/"
    echo "   管理后台: http://$SERVER_IP/frontend/admin/login_fixed.html"
    echo "   系统状态: http://$SERVER_IP/status_optimized.html"
    echo ""
    echo "🔐 默认管理员账户:"
    echo "   用户名: admin"
    echo "   密码: admin123"
    echo ""
    echo "📁 应用目录: /opt/qingyun-shop"
    echo "📋 日志查看: journalctl -u qingyun-shop -f"
    echo ""
    echo "🔧 服务管理命令:"
    echo "   启动: systemctl start qingyun-shop"
    echo "   停止: systemctl stop qingyun-shop"
    echo "   重启: systemctl restart qingyun-shop"
    echo "   状态: systemctl status qingyun-shop"
    echo ""
    echo "🌐 Nginx管理:"
    echo "   重启: systemctl restart nginx"
    echo "   状态: systemctl status nginx"
    echo "   配置: /etc/nginx/conf.d/qingyun-shop.conf"
    echo ""
    echo "=========================================="
}

# 主函数
main() {
    echo "=========================================="
    echo "🛒 青云小铺自动部署脚本"
    echo "=========================================="
    echo ""
    
    check_root
    check_system
    
    log_info "开始部署青云小铺..."
    
    update_system
    install_python
    install_nodejs
    install_nginx
    install_monitoring
    create_app_user
    deploy_app
    install_python_deps
    configure_nginx
    create_systemd_service
    configure_firewall
    init_database
    start_services
    
    show_deployment_info
}

# 运行主函数
main "$@"
