<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>青云小铺 - 虚拟商品交易平台</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: #333;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem 1rem;
    }
    
    .header {
      text-align: center;
      color: white;
      margin-bottom: 3rem;
    }
    
    .logo {
      width: 80px;
      height: 80px;
      border-radius: 16px;
      margin: 0 auto 1rem;
      background: white;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .logo img {
      width: 64px;
      height: 64px;
      border-radius: 12px;
    }
    
    .title {
      font-size: 3rem;
      font-weight: 700;
      margin-bottom: 1rem;
    }
    
    .subtitle {
      font-size: 1.25rem;
      opacity: 0.9;
      margin-bottom: 2rem;
    }
    
    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 2rem;
      margin-bottom: 3rem;
    }
    
    .feature-card {
      background: white;
      border-radius: 20px;
      padding: 2rem;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      transition: transform 0.3s ease;
    }
    
    .feature-card:hover {
      transform: translateY(-5px);
    }
    
    .feature-icon {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      margin-bottom: 1.5rem;
    }
    
    .feature-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: #1f2937;
    }
    
    .feature-desc {
      color: #6b7280;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }
    
    .feature-links {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;
    }
    
    .btn {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 8px;
      font-weight: 500;
      text-decoration: none;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 0.9rem;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }
    
    .btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    
    .btn-primary:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
    
    .btn-outline {
      background: transparent;
      color: #667eea;
      border: 1px solid #667eea;
    }
    
    .btn-outline:hover {
      background: #667eea;
      color: white;
    }
    
    .quick-access {
      background: rgba(255,255,255,0.1);
      border-radius: 20px;
      padding: 2rem;
      text-align: center;
      color: white;
    }
    
    .quick-access h3 {
      font-size: 1.5rem;
      margin-bottom: 1rem;
    }
    
    .quick-links {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-top: 2rem;
    }
    
    .quick-link {
      background: rgba(255,255,255,0.1);
      border: 1px solid rgba(255,255,255,0.2);
      border-radius: 12px;
      padding: 1rem;
      color: white;
      text-decoration: none;
      transition: all 0.3s ease;
    }
    
    .quick-link:hover {
      background: rgba(255,255,255,0.2);
      transform: translateY(-2px);
    }
    
    .footer {
      text-align: center;
      color: rgba(255,255,255,0.8);
      margin-top: 3rem;
      padding-top: 2rem;
      border-top: 1px solid rgba(255,255,255,0.1);
    }
    
    .icp {
      margin-top: 1rem;
      font-size: 0.875rem;
      opacity: 0.7;
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
      .title {
        font-size: 2rem;
      }
      
      .features-grid {
        grid-template-columns: 1fr;
      }
      
      .quick-links {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo">
        <img src="icon/8304CEF178EEF908F89E309C65C1E521.jpg" alt="青云小铺">
      </div>
      <h1 class="title">青云小铺</h1>
      <p class="subtitle">专业的虚拟商品交易平台</p>
    </div>

    <div class="features-grid">
      <!-- 用户端功能 -->
      <div class="feature-card">
        <div class="feature-icon" style="background: #dbeafe; color: #2563eb;">🛒</div>
        <h3 class="feature-title">用户购物体验</h3>
        <p class="feature-desc">
          现代化的购物界面，支持商品浏览、购物车管理、在线支付等完整购物流程。
          集成PayPal和加密货币支付，提供安全便捷的支付体验。
        </p>
        <div class="feature-links">
          <a href="frontend/index_standalone.html" class="btn btn-primary">🏠 商城首页</a>
          <a href="frontend/cart_optimized.html" class="btn btn-outline">🛒 购物车</a>
          <a href="frontend/checkout_optimized.html" class="btn btn-outline">💳 结算支付</a>
        </div>
      </div>

      <!-- 管理后台 -->
      <div class="feature-card">
        <div class="feature-icon" style="background: #f3e8ff; color: #9333ea;">⚙️</div>
        <h3 class="feature-title">管理后台系统</h3>
        <p class="feature-desc">
          功能完整的管理后台，包括商品管理、订单管理、数据统计、系统设置等模块。
          支持实时数据监控、图表分析、系统配置等高级功能。
        </p>
        <div class="feature-links">
          <a href="frontend/admin/login_fixed.html" class="btn btn-primary">🔐 管理员登录</a>
          <a href="frontend/admin/admin_optimized.html" class="btn btn-outline">📦 商品管理</a>
          <a href="frontend/admin/orders_optimized.html" class="btn btn-outline">📋 订单管理</a>
        </div>
      </div>

      <!-- 数据统计 -->
      <div class="feature-card">
        <div class="feature-icon" style="background: #dcfce7; color: #16a34a;">📊</div>
        <h3 class="feature-title">数据统计分析</h3>
        <p class="feature-desc">
          专业的数据统计面板，提供销售趋势图表、订单状态分析、热销商品排行等功能。
          支持实时数据更新和数据导出，帮助商家做出明智决策。
        </p>
        <div class="feature-links">
          <a href="frontend/admin/analytics.html" class="btn btn-primary">📈 数据统计</a>
          <a href="status_optimized.html" class="btn btn-outline">🔍 系统状态</a>
        </div>
      </div>

      <!-- 系统设置 -->
      <div class="feature-card">
        <div class="feature-icon" style="background: #fef3c7; color: #d97706;">🔧</div>
        <h3 class="feature-title">系统配置管理</h3>
        <p class="feature-desc">
          全面的系统设置功能，包括商店基本信息、支付配置、邮件设置、安全设置等。
          支持PayPal配置、SMTP设置、维护模式等高级功能。
        </p>
        <div class="feature-links">
          <a href="frontend/admin/settings.html" class="btn btn-primary">⚙️ 系统设置</a>
        </div>
      </div>

      <!-- 支付功能 -->
      <div class="feature-card">
        <div class="feature-icon" style="background: #fef2f2; color: #dc2626;">💳</div>
        <h3 class="feature-title">多元化支付</h3>
        <p class="feature-desc">
          集成多种支付方式，包括PayPal国际支付和加密货币支付。
          支持自动发货、订单跟踪、支付确认等完整的支付流程。
        </p>
        <div class="feature-links">
          <a href="frontend/order_success.html?order_id=demo123" class="btn btn-primary">✅ 支付成功页</a>
        </div>
      </div>

      <!-- 技术特性 -->
      <div class="feature-card">
        <div class="feature-icon" style="background: #f0f9ff; color: #0284c7;">⚡</div>
        <h3 class="feature-title">技术特性</h3>
        <p class="feature-desc">
          采用现代化技术栈，响应式设计，支持移动端访问。
          使用Flask后端、SQLite数据库、现代化前端框架，确保系统稳定高效。
        </p>
        <div class="feature-links">
          <a href="login_test.html" class="btn btn-primary">🔧 功能测试</a>
        </div>
      </div>
    </div>

    <div class="quick-access">
      <h3>🚀 快速访问</h3>
      <p>选择您要访问的功能模块</p>
      
      <div class="quick-links">
        <a href="frontend/index_standalone.html" class="quick-link">
          <div>🏠 商城首页</div>
          <small>浏览商品和购物</small>
        </a>
        <a href="frontend/admin/login_fixed.html" class="quick-link">
          <div>🔐 管理后台</div>
          <small>admin / admin123</small>
        </a>
        <a href="frontend/admin/analytics.html" class="quick-link">
          <div>📊 数据统计</div>
          <small>销售数据分析</small>
        </a>
        <a href="frontend/admin/settings.html" class="quick-link">
          <div>⚙️ 系统设置</div>
          <small>配置管理</small>
        </a>
        <a href="frontend/checkout_optimized.html" class="quick-link">
          <div>💳 支付演示</div>
          <small>PayPal集成</small>
        </a>
        <a href="status_optimized.html" class="quick-link">
          <div>🔍 系统状态</div>
          <small>实时监控</small>
        </a>
      </div>
    </div>

    <div class="footer">
      <p>&copy; 2025 青云小铺. 保留所有权利.</p>
      <p>专业的虚拟商品交易平台 | 安全可靠 | 自动发货</p>
      <div class="icp">粤ICP备2023114300号-1</div>
    </div>
  </div>

  <script>
    // 页面加载完成后的初始化
    document.addEventListener('DOMContentLoaded', () => {
      console.log('青云小铺演示页面加载完成');
      
      // 添加点击统计
      document.querySelectorAll('a').forEach(link => {
        link.addEventListener('click', (e) => {
          const href = e.target.closest('a').href;
          console.log('访问链接:', href);
        });
      });
    });
  </script>
</body>
</html>
