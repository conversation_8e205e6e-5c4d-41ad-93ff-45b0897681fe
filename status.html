<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发卡商城 - 系统状态</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 100%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo {
            font-size: 2.5rem;
            font-weight: bold;
            color: #4f46e5;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: #6b7280;
            font-size: 1.1rem;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: #f8fafc;
            border-radius: 12px;
            padding: 20px;
            border-left: 4px solid #10b981;
        }
        
        .status-card.error {
            border-left-color: #ef4444;
        }
        
        .status-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }
        
        .status-value {
            font-size: 1.2rem;
            color: #10b981;
            font-weight: bold;
        }
        
        .status-value.error {
            color: #ef4444;
        }
        
        .links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }
        
        .link-btn {
            display: block;
            text-align: center;
            padding: 12px 20px;
            background: #4f46e5;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: background 0.3s ease;
        }
        
        .link-btn:hover {
            background: #4338ca;
        }
        
        .link-btn.secondary {
            background: #6b7280;
        }
        
        .link-btn.secondary:hover {
            background: #4b5563;
        }
        
        .info {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .info-title {
            font-weight: 600;
            color: #1e40af;
            margin-bottom: 8px;
        }
        
        .info-text {
            color: #1e40af;
            font-size: 0.9rem;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🛒 发卡商城</div>
            <div class="subtitle">虚拟商品自动发货平台</div>
        </div>
        
        <div class="status-grid">
            <div class="status-card">
                <div class="status-title">前端服务</div>
                <div class="status-value" id="frontend-status">检查中...</div>
            </div>
            
            <div class="status-card">
                <div class="status-title">后端API</div>
                <div class="status-value" id="backend-status">检查中...</div>
            </div>
            
            <div class="status-card">
                <div class="status-title">产品数量</div>
                <div class="status-value" id="product-count">检查中...</div>
            </div>
            
            <div class="status-card">
                <div class="status-title">数据库</div>
                <div class="status-value" id="database-status">检查中...</div>
            </div>
        </div>
        
        <div class="links">
            <a href="http://localhost:8080/frontend/index.html" class="link-btn" target="_blank">
                🏠 访问商城首页
            </a>
            <a href="http://localhost:8080/frontend/admin/login.html" class="link-btn secondary" target="_blank">
                ⚙️ 管理员后台
            </a>
            <a href="http://localhost:5000/api/health" class="link-btn secondary" target="_blank">
                🔍 API健康检查
            </a>
        </div>
        
        <div class="info">
            <div class="info-title">📋 系统信息</div>
            <div class="info-text">
                • 前端服务运行在: <strong>http://localhost:8080</strong><br>
                • 后端API运行在: <strong>http://localhost:5000</strong><br>
                • 管理员账号: admin / admin123<br>
                • 支持功能: 产品管理、订单管理、用户认证、支付系统
            </div>
        </div>
    </div>
    
    <script>
        // 检查前端服务状态
        function checkFrontendStatus() {
            const statusEl = document.getElementById('frontend-status');
            statusEl.textContent = '✅ 运行中';
            statusEl.parentElement.classList.remove('error');
        }
        
        // 检查后端API状态
        async function checkBackendStatus() {
            const statusEl = document.getElementById('backend-status');
            try {
                const response = await fetch('http://localhost:5000/api/health');
                if (response.ok) {
                    statusEl.textContent = '✅ 运行中';
                    statusEl.parentElement.classList.remove('error');
                } else {
                    throw new Error('API响应异常');
                }
            } catch (error) {
                statusEl.textContent = '❌ 离线';
                statusEl.classList.add('error');
                statusEl.parentElement.classList.add('error');
            }
        }
        
        // 检查产品数量
        async function checkProductCount() {
            const statusEl = document.getElementById('product-count');
            try {
                const response = await fetch('http://localhost:5000/api/products');
                if (response.ok) {
                    const data = await response.json();
                    statusEl.textContent = `${data.total || 0} 个产品`;
                    statusEl.parentElement.classList.remove('error');
                } else {
                    throw new Error('无法获取产品数据');
                }
            } catch (error) {
                statusEl.textContent = '❌ 无法获取';
                statusEl.classList.add('error');
                statusEl.parentElement.classList.add('error');
            }
        }
        
        // 检查数据库状态
        async function checkDatabaseStatus() {
            const statusEl = document.getElementById('database-status');
            try {
                const response = await fetch('http://localhost:5000/api/health');
                if (response.ok) {
                    statusEl.textContent = '✅ 连接正常';
                    statusEl.parentElement.classList.remove('error');
                } else {
                    throw new Error('数据库连接异常');
                }
            } catch (error) {
                statusEl.textContent = '❌ 连接失败';
                statusEl.classList.add('error');
                statusEl.parentElement.classList.add('error');
            }
        }
        
        // 页面加载时执行检查
        document.addEventListener('DOMContentLoaded', () => {
            checkFrontendStatus();
            checkBackendStatus();
            checkProductCount();
            checkDatabaseStatus();
            
            // 每30秒自动刷新状态
            setInterval(() => {
                checkBackendStatus();
                checkProductCount();
                checkDatabaseStatus();
            }, 30000);
        });
    </script>
</body>
</html>
