#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API测试脚本
用于测试发卡商城后端API的基本功能
"""

import requests
import json
import time

BASE_URL = 'http://127.0.0.1:5000'

def test_api():
    """测试API功能"""
    print("=== 发卡商城API测试 ===\n")
    
    # 测试产品API
    print("1. 测试产品API")
    
    # 获取产品列表
    print("   - 获取产品列表")
    response = requests.get(f"{BASE_URL}/api/products")
    print(f"     状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"     产品数量: {data.get('total', 0)}")
    
    # 测试用户注册
    print("\n2. 测试用户注册")
    user_data = {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "123456"
    }
    response = requests.post(f"{BASE_URL}/api/auth/register", json=user_data)
    print(f"   状态码: {response.status_code}")
    if response.status_code == 201:
        print("   注册成功")
    else:
        print(f"   注册失败: {response.json()}")
    
    # 测试用户登录
    print("\n3. 测试用户登录")
    login_data = {
        "username": "testuser",
        "password": "123456"
    }
    session = requests.Session()
    response = session.post(f"{BASE_URL}/api/auth/login", json=login_data)
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        print("   登录成功")
        user_info = response.json()
        print(f"   用户信息: {user_info.get('user', {}).get('username')}")
    else:
        print(f"   登录失败: {response.json()}")
    
    # 测试创建管理员用户
    print("\n4. 测试创建管理员用户")
    admin_data = {
        "username": "admin",
        "email": "<EMAIL>",
        "password": "admin123",
        "is_admin": True
    }
    response = requests.post(f"{BASE_URL}/api/auth/register", json=admin_data)
    print(f"   状态码: {response.status_code}")
    
    # 管理员登录
    admin_session = requests.Session()
    admin_login = {
        "username": "admin",
        "password": "admin123"
    }
    response = admin_session.post(f"{BASE_URL}/api/auth/login", json=admin_login)
    print(f"   管理员登录状态码: {response.status_code}")
    
    # 测试创建产品（需要管理员权限）
    print("\n5. 测试创建产品")
    product_data = {
        "name": "测试产品",
        "price": 99.99,
        "stock": 100,
        "description": "这是一个测试产品"
    }
    response = admin_session.post(f"{BASE_URL}/api/products", json=product_data)
    print(f"   状态码: {response.status_code}")
    if response.status_code == 201:
        product = response.json().get('product', {})
        product_id = product.get('id')
        print(f"   产品创建成功，ID: {product_id}")
        
        # 测试创建订单
        print("\n6. 测试创建订单")
        order_data = {
            "product_id": product_id,
            "quantity": 2
        }
        response = session.post(f"{BASE_URL}/api/orders", json=order_data)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 201:
            order = response.json().get('order', {})
            order_id = order.get('id')
            print(f"   订单创建成功，ID: {order_id}")
            
            # 测试支付
            print("\n7. 测试支付功能")
            payment_data = {
                "order_id": order_id,
                "provider": "mock"
            }
            response = session.post(f"{BASE_URL}/api/payments/create", json=payment_data)
            print(f"   创建支付状态码: {response.status_code}")
            if response.status_code == 201:
                payment = response.json().get('payment', {})
                payment_id = payment.get('payment_id')
                print(f"   支付订单创建成功，ID: {payment_id}")
                
                # 验证支付
                verify_data = {"provider": "mock"}
                response = session.post(f"{BASE_URL}/api/payments/{payment_id}/verify", json=verify_data)
                print(f"   验证支付状态码: {response.status_code}")
                if response.status_code == 200:
                    print("   支付验证成功")
    
    # 测试搜索功能
    print("\n8. 测试搜索功能")
    response = requests.get(f"{BASE_URL}/api/products/search?keyword=测试")
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"   搜索结果数量: {len(data.get('items', []))}")
    
    # 测试获取支付方式
    print("\n9. 测试获取支付方式")
    response = requests.get(f"{BASE_URL}/api/payments/methods")
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        methods = response.json().get('methods', [])
        print(f"   支付方式数量: {len(methods)}")
        for method in methods:
            print(f"     - {method.get('name')}: {'启用' if method.get('enabled') else '禁用'}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    try:
        test_api()
    except requests.exceptions.ConnectionError:
        print("错误: 无法连接到服务器，请确保应用正在运行")
        print("启动命令: cd backend && source venv/bin/activate && python app.py")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
