# backend/utils/db.py
import sqlite3
from contextlib import contextmanager
import os
from typing import Optional, List, Dict, Any

DB_PATH = 'faka_mall.db'

@contextmanager
def get_db_connection():
    """获取数据库连接上下文管理器"""
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    try:
        yield conn
    finally:
        conn.close()

def init_db():
    """初始化数据库表结构"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        
        # 创建产品表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            price REAL NOT NULL CHECK(price > 0),
            stock INTEGER NOT NULL CHECK(stock >= 0),
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # 创建订单表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            product_id INTEGER NOT NULL,
            quantity INTEGER NOT NULL CHECK(quantity > 0),
            total_price REAL NOT NULL CHECK(total_price > 0),
            status TEXT DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
        ''')
        
        conn.commit()

def get_product(product_id: int) -> Optional[Dict[str, Any]]:
    """获取单个产品"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM products WHERE id = ?', (product_id,))
        result = cursor.fetchone()
        return dict(result) if result else None

def get_products(page: int = 1, limit: int = 10) -> Dict[str, Any]:
    """获取分页产品列表"""
    offset = (page - 1) * limit
    with get_db_connection() as conn:
        cursor = conn.cursor()
        
        # 获取当前页产品
        cursor.execute('''
        SELECT * FROM products 
        ORDER BY id DESC
        LIMIT ? OFFSET ?
        ''', (limit, offset))
        items = [dict(row) for row in cursor.fetchall()]
        
        # 获取总数
        cursor.execute('SELECT COUNT(*) FROM products')
        total = cursor.fetchone()[0]
        
        return {
            'page': page,
            'items': items,
            'total': total,
            'has_next': (page * limit) < total,
            'has_prev': page > 1
        }

def get_all_products() -> List[Dict[str, Any]]:
    """获取所有产品"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM products ORDER BY id DESC')
        items = [dict(row) for row in cursor.fetchall()]
        return items

def search_products(keyword: str, page: int = 1, limit: int = 10) -> Dict[str, Any]:
    """根据关键词搜索产品（名称或描述）"""
    offset = (page - 1) * limit
    search_term = f'%{keyword}%'
    with get_db_connection() as conn:
        cursor = conn.cursor()
        
        # 获取当前页产品
        cursor.execute('''
        SELECT * FROM products 
        WHERE name LIKE ? OR description LIKE ?
        ORDER BY id DESC
        LIMIT ? OFFSET ?
        ''', (search_term, search_term, limit, offset))
        items = [dict(row) for row in cursor.fetchall()]
        
        # 获取总数
        cursor.execute('SELECT COUNT(*) FROM products WHERE name LIKE ? OR description LIKE ?', (search_term, search_term))
        total = cursor.fetchone()[0]
        
        return { 'page': page, 'items': items, 'total': total, 'has_next': (page * limit) < total, 'has_prev': page > 1 }

def get_low_stock_products(threshold: int) -> List[Dict[str, Any]]:
    """获取低于指定库存阈值的产品"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('''
            SELECT id, name, stock, price FROM products
            WHERE stock < ? ORDER BY stock ASC
        ''', (threshold,))
        items = [dict(row) for row in cursor.fetchall()]
        return items

def create_product(product_data: Dict[str, Any]) -> Dict[str, Any]:
    """创建新产品"""
    # 数据验证已移至模型层，这里假设数据是有效的
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('''
        INSERT INTO products (name, price, stock, description)
        VALUES (?, ?, ?, ?)
        ''', (
            product_data['name'],
            float(product_data['price']),
            int(product_data['stock']),
            product_data.get('description', '')
        ))
        conn.commit()
        
        # 获取刚插入的产品
        product_id = cursor.lastrowid
        return get_product(product_id)

def update_product(product_id: int, product_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """更新产品信息"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('''
        UPDATE products 
        SET 
            name = COALESCE(?, name),
            price = COALESCE(?, price),
            stock = COALESCE(?, stock),
            description = COALESCE(?, description),
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
        ''', (
            product_data.get('name'),
            float(product_data['price']) if 'price' in product_data else None,
            int(product_data['stock']) if 'stock' in product_data else None,
            product_data.get('description'),
            product_id
        ))
        conn.commit()
        
        if cursor.rowcount == 0:
            return None
        
        return get_product(product_id)

def delete_product(product_id: int) -> bool:
    """删除产品"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('DELETE FROM products WHERE id = ?', (product_id,))
        conn.commit()
        return cursor.rowcount > 0

def batch_delete_products(product_ids: List[int]) -> Dict[str, int]:
    """批量删除产品"""
    if not product_ids:
        return {'deleted_count': 0}
    
    placeholders = ','.join('?' for _ in product_ids)
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute(f'DELETE FROM products WHERE id IN ({placeholders})', product_ids)
        conn.commit()
        return {'deleted_count': cursor.rowcount}

# --- 订单数据库操作 ---

def create_order(order_data: Dict[str, Any], product_price: float) -> Optional[int]:
    """
    创建新订单并更新产品库存 (事务性操作).
    成功则返回新订单ID，失败(如库存不足)则返回None.
    """
    product_id = order_data['product_id']
    quantity = order_data['quantity']
    user_id = order_data.get('user_id') # 可以是匿名订单
    total_price = round(product_price * quantity, 2)

    with get_db_connection() as conn:
        cursor = conn.cursor()
        try:
            # 1. 原子性地更新库存
            cursor.execute('''
                UPDATE products
                SET stock = stock - ?
                WHERE id = ? AND stock >= ?
            ''', (quantity, product_id, quantity))

            # 如果没有行被更新，说明库存不足，事务失败
            if cursor.rowcount == 0:
                conn.rollback()
                return None

            # 2. 创建订单
            cursor.execute('''
                INSERT INTO orders (product_id, quantity, total_price, user_id)
                VALUES (?, ?, ?, ?)
            ''', (product_id, quantity, total_price, user_id))
            
            order_id = cursor.lastrowid
            conn.commit()
            return order_id

        except sqlite3.Error:
            conn.rollback()
            raise # 重新引发原始数据库错误

def get_order(order_id: int) -> Optional[Dict[str, Any]]:
    """获取单个订单，并附带产品信息"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('''
            SELECT o.*, p.name as product_name
            FROM orders o
            JOIN products p ON o.product_id = p.id
            WHERE o.id = ?
        ''', (order_id,))
        result = cursor.fetchone()
        return dict(result) if result else None

def get_orders(page: int = 1, limit: int = 10, status: Optional[str] = None) -> Dict[str, Any]:
    """获取分页订单列表，并附带产品信息"""
    offset = (page - 1) * limit
    
    base_query = '''
            FROM orders o JOIN products p ON o.product_id = p.id
    '''
    params = []
    
    if status:
        base_query += ' WHERE o.status = ?'
        params.append(status)

    with get_db_connection() as conn:
        cursor = conn.cursor()
        
        # 获取总数
        count_query = 'SELECT COUNT(*) ' + base_query
        cursor.execute(count_query, params)
        total = cursor.fetchone()[0]
        
        # 获取当前页数据
        select_query = 'SELECT o.id, o.user_id, o.quantity, o.total_price, o.status, o.created_at, p.id as product_id, p.name as product_name ' + base_query
        select_query += ' ORDER BY o.id DESC LIMIT ? OFFSET ?'
        params.extend([limit, offset])
        cursor.execute(select_query, params)
        
        items = [dict(row) for row in cursor.fetchall()]
        return { 'page': page, 'items': items, 'total': total, 'has_next': (page * limit) < total, 'has_prev': page > 1 }

def get_orders_by_user(user_id: int, page: int = 1, limit: int = 10) -> Dict[str, Any]:
    """根据用户ID获取分页订单列表"""
    offset = (page - 1) * limit
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('''
            SELECT o.id, o.quantity, o.total_price, o.status, o.created_at, p.id as product_id, p.name as product_name
            FROM orders o JOIN products p ON o.product_id = p.id
            WHERE o.user_id = ?
            ORDER BY o.id DESC LIMIT ? OFFSET ?
        ''', (user_id, limit, offset))
        items = [dict(row) for row in cursor.fetchall()]
        cursor.execute('SELECT COUNT(*) FROM orders WHERE user_id = ?', (user_id,))
        total = cursor.fetchone()[0]
        return { 'page': page, 'items': items, 'total': total, 'has_next': (page * limit) < total, 'has_prev': page > 1 }

def update_order(order_id: int, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """更新订单信息（目前只支持状态）"""
    if 'status' not in data:
        return get_order(order_id) # 没有可更新的字段

    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('UPDATE orders SET status = ? WHERE id = ?', (data['status'], order_id))
        conn.commit()
        if cursor.rowcount == 0:
            return None
        return get_order(order_id)

def delete_order(order_id: int) -> bool:
    """删除订单"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('DELETE FROM orders WHERE id = ?', (order_id,))
        conn.commit()
        return cursor.rowcount > 0

# --- Analytics & Statistics DB Operations ---

def get_dashboard_summary() -> Dict[str, Any]:
    """获取仪表板核心数据摘要"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        # Note: In a real high-traffic app, you might cache this or calculate it periodically.
        cursor.execute('''
            SELECT
                (SELECT COUNT(*) FROM products) as total_products,
                (SELECT COUNT(*) FROM orders) as total_orders,
                (SELECT SUM(total_price) FROM orders WHERE status = 'paid') as total_revenue,
                (SELECT COUNT(*) FROM users) as total_users
        ''')
        summary = cursor.fetchone()
        return dict(summary) if summary else {}

def get_product_statistics() -> Dict[str, Any]:
    """获取产品相关的统计数据"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('''
            SELECT
                COUNT(*) as total_products,
                SUM(stock) as total_stock,
                AVG(price) as average_price,
                (SELECT COUNT(*) FROM products WHERE stock = 0) as out_of_stock_count
        ''')
        stats = cursor.fetchone()
        return dict(stats) if stats else {}

def get_order_statistics() -> Dict[str, Any]:
    """获取订单相关的统计数据"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('''
            SELECT
                COUNT(*) as total_orders,
                SUM(total_price) as total_value,
                AVG(total_price) as average_order_value,
                (SELECT COUNT(*) FROM orders WHERE status = 'paid') as paid_orders_count
        ''')
        stats = cursor.fetchone()
        return dict(stats) if stats else {}

def get_top_selling_products(limit: int = 10) -> List[Dict[str, Any]]:
    """获取最畅销的产品列表"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('''
            SELECT
                p.id, p.name, p.price, SUM(o.quantity) as total_sold,
                SUM(o.total_price) as revenue
            FROM orders o
            JOIN products p ON o.product_id = p.id
            WHERE o.status = 'paid'
            GROUP BY p.id, p.name, p.price
            ORDER BY total_sold DESC
            LIMIT ?
        ''', (limit,))
        items = [dict(row) for row in cursor.fetchall()]
        return items

def get_monthly_revenue() -> List[Dict[str, Any]]:
    """获取月度收入统计"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('''
            SELECT
                strftime('%Y-%m', created_at) as month,
                SUM(total_price) as revenue
            FROM orders
            WHERE status = 'paid'
            GROUP BY month
            ORDER BY month ASC
        ''')
        items = [dict(row) for row in cursor.fetchall()]
        return items

# 初始化数据库
if not os.path.exists(DB_PATH):
    init_db()