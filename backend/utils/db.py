# backend/utils/db.py
import sqlite3
from contextlib import contextmanager
import os
from typing import Optional, List, Dict, Any

DB_PATH = 'faka_mall.db'

@contextmanager
def get_db_connection():
    """获取数据库连接上下文管理器"""
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    try:
        yield conn
    finally:
        conn.close()

def init_db():
    """初始化数据库表结构"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        
        # 创建产品表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            price REAL NOT NULL CHECK(price > 0),
            stock INTEGER NOT NULL CHECK(stock >= 0),
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # 创建订单表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            product_id INTEGER NOT NULL,
            quantity INTEGER NOT NULL CHECK(quantity > 0),
            total_price REAL NOT NULL CHECK(total_price > 0),
            status TEXT DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
        ''')
        
        conn.commit()

def get_product(product_id: int) -> Optional[Dict[str, Any]]:
    """获取单个产品"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM products WHERE id = ?', (product_id,))
        result = cursor.fetchone()
        return dict(result) if result else None

def get_products(page: int = 1, limit: int = 10) -> Dict[str, Any]:
    """获取分页产品列表"""
    offset = (page - 1) * limit
    with get_db_connection() as conn:
        cursor = conn.cursor()
        
        # 获取当前页产品
        cursor.execute('''
        SELECT * FROM products 
        ORDER BY id DESC
        LIMIT ? OFFSET ?
        ''', (limit, offset))
        items = [dict(row) for row in cursor.fetchall()]
        
        # 获取总数
        cursor.execute('SELECT COUNT(*) FROM products')
        total = cursor.fetchone()[0]
        
        return {
            'page': page,
            'items': items,
            'total': total,
            'has_next': (page * limit) < total,
            'has_prev': page > 1
        }

def get_all_products() -> List[Dict[str, Any]]:
    """获取所有产品"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM products ORDER BY id DESC')
        items = [dict(row) for row in cursor.fetchall()]
        return items

def search_products(keyword: str, page: int = 1, limit: int = 10) -> Dict[str, Any]:
    """根据关键词搜索产品（名称或描述）"""
    offset = (page - 1) * limit
    search_term = f'%{keyword}%'
    with get_db_connection() as conn:
        cursor = conn.cursor()
        
        # 获取当前页产品
        cursor.execute('''
        SELECT * FROM products 
        WHERE name LIKE ? OR description LIKE ?
        ORDER BY id DESC
        LIMIT ? OFFSET ?
        ''', (search_term, search_term, limit, offset))
        items = [dict(row) for row in cursor.fetchall()]
        
        # 获取总数
        cursor.execute('SELECT COUNT(*) FROM products WHERE name LIKE ? OR description LIKE ?', (search_term, search_term))
        total = cursor.fetchone()[0]
        
        return { 'page': page, 'items': items, 'total': total, 'has_next': (page * limit) < total, 'has_prev': page > 1 }

def create_product(product_data: Dict[str, Any]) -> Dict[str, Any]:
    """创建新产品"""
    # 数据验证已移至模型层，这里假设数据是有效的
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('''
        INSERT INTO products (name, price, stock, description)
        VALUES (?, ?, ?, ?)
        ''', (
            product_data['name'],
            float(product_data['price']),
            int(product_data['stock']),
            product_data.get('description', '')
        ))
        conn.commit()
        
        # 获取刚插入的产品
        product_id = cursor.lastrowid
        return get_product(product_id)

def update_product(product_id: int, product_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """更新产品信息"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('''
        UPDATE products 
        SET 
            name = COALESCE(?, name),
            price = COALESCE(?, price),
            stock = COALESCE(?, stock),
            description = COALESCE(?, description),
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
        ''', (
            product_data.get('name'),
            float(product_data['price']) if 'price' in product_data else None,
            int(product_data['stock']) if 'stock' in product_data else None,
            product_data.get('description'),
            product_id
        ))
        conn.commit()
        
        if cursor.rowcount == 0:
            return None
        
        return get_product(product_id)

def delete_product(product_id: int) -> bool:
    """删除产品"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('DELETE FROM products WHERE id = ?', (product_id,))
        conn.commit()
        return cursor.rowcount > 0

# --- 订单数据库操作 ---

def create_order(order_data: Dict[str, Any], product_price: float) -> Optional[int]:
    """
    创建新订单并更新产品库存 (事务性操作).
    成功则返回新订单ID，失败(如库存不足)则返回None.
    """
    product_id = order_data['product_id']
    quantity = order_data['quantity']
    total_price = round(product_price * quantity, 2)

    with get_db_connection() as conn:
        cursor = conn.cursor()
        try:
            # 1. 原子性地更新库存
            cursor.execute('''
                UPDATE products
                SET stock = stock - ?
                WHERE id = ? AND stock >= ?
            ''', (quantity, product_id, quantity))

            # 如果没有行被更新，说明库存不足，事务失败
            if cursor.rowcount == 0:
                conn.rollback()
                return None

            # 2. 创建订单
            cursor.execute('''
                INSERT INTO orders (product_id, quantity, total_price)
                VALUES (?, ?, ?)
            ''', (product_id, quantity, total_price))
            
            order_id = cursor.lastrowid
            conn.commit()
            return order_id

        except sqlite3.Error:
            conn.rollback()
            raise # 重新引发原始数据库错误

def get_order(order_id: int) -> Optional[Dict[str, Any]]:
    """获取单个订单，并附带产品信息"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('''
            SELECT o.*, p.name as product_name, p.price as product_price
            FROM orders o
            JOIN products p ON o.product_id = p.id
            WHERE o.id = ?
        ''', (order_id,))
        result = cursor.fetchone()
        return dict(result) if result else None

def get_orders(page: int = 1, limit: int = 10) -> Dict[str, Any]:
    """获取分页订单列表，并附带产品信息"""
    offset = (page - 1) * limit
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('''
            SELECT o.id, o.quantity, o.total_price, o.status, o.created_at, p.id as product_id, p.name as product_name
            FROM orders o JOIN products p ON o.product_id = p.id
            ORDER BY o.id DESC LIMIT ? OFFSET ?
        ''', (limit, offset))
        items = [dict(row) for row in cursor.fetchall()]
        cursor.execute('SELECT COUNT(*) FROM orders')
        total = cursor.fetchone()[0]
        return { 'page': page, 'items': items, 'total': total, 'has_next': (page * limit) < total, 'has_prev': page > 1 }

def update_order_status(order_id: int, status: str) -> Optional[Dict[str, Any]]:
    """更新订单状态"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute('UPDATE orders SET status = ? WHERE id = ?', (status, order_id))
        conn.commit()
        if cursor.rowcount == 0:
            return None
        return get_order(order_id)

# 初始化数据库
if not os.path.exists(DB_PATH):
    init_db()