# backend/utils/exceptions.py
"""
自定义异常类，用于更好的错误处理
"""

class BaseCustomException(Exception):
    """基础自定义异常类"""
    def __init__(self, message: str, code: int = 500):
        self.message = message
        self.code = code
        super().__init__(self.message)

class ValidationError(BaseCustomException):
    """数据验证错误"""
    def __init__(self, message: str):
        super().__init__(message, 400)

class NotFoundError(BaseCustomException):
    """资源未找到错误"""
    def __init__(self, message: str):
        super().__init__(message, 404)

class DatabaseError(BaseCustomException):
    """数据库操作错误"""
    def __init__(self, message: str):
        super().__init__(message, 500)

class AuthenticationError(BaseCustomException):
    """认证错误"""
    def __init__(self, message: str):
        super().__init__(message, 401)

class AuthorizationError(BaseCustomException):
    """授权错误"""
    def __init__(self, message: str):
        super().__init__(message, 403)

class BusinessLogicError(BaseCustomException):
    """业务逻辑错误"""
    def __init__(self, message: str):
        super().__init__(message, 422)

class PermissionDeniedError(BaseCustomException):
    """权限拒绝错误"""
    def __init__(self, message: str):
        super().__init__(message, 403)

class RateLimitError(BaseCustomException):
    """请求频率限制错误"""
    def __init__(self, message: str):
        super().__init__(message, 429)

class ConflictError(BaseCustomException):
    """资源冲突错误"""
    def __init__(self, message: str):
        super().__init__(message, 409)
