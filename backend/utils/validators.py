# backend/utils/validators.py
"""
数据验证工具
"""
import re
from typing import Dict, Any, Optional, List
from .exceptions import ValidationError

def validate_product_data(data: Dict[str, Any], partial: bool = False) -> None:
    """
    验证产品数据
    :param data: 产品数据字典
    :param partial: 是否为部分更新（允许缺少某些字段）
    """
    if not isinstance(data, dict):
        raise ValidationError("数据必须是字典格式")
    
    # 必需字段检查（仅在非部分更新时）
    if not partial:
        required_fields = ['name', 'price', 'stock']
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            raise ValidationError(f"缺少必需字段: {', '.join(missing_fields)}")
    
    # 产品名称验证
    if 'name' in data:
        name = data['name']
        if not isinstance(name, str) or not name.strip():
            raise ValidationError("产品名称不能为空")
        if len(name.strip()) > 100:
            raise ValidationError("产品名称长度不能超过100个字符")
        data['name'] = name.strip()
    
    # 价格验证
    if 'price' in data:
        try:
            price = float(data['price'])
            if price <= 0:
                raise ValidationError("产品价格必须大于0")
            if price > 999999.99:
                raise ValidationError("产品价格不能超过999999.99")
            data['price'] = round(price, 2)
        except (ValueError, TypeError):
            raise ValidationError("产品价格必须是有效的数字")
    
    # 库存验证
    if 'stock' in data:
        try:
            stock = int(data['stock'])
            if stock < 0:
                raise ValidationError("产品库存不能为负数")
            if stock > 999999:
                raise ValidationError("产品库存不能超过999999")
            data['stock'] = stock
        except (ValueError, TypeError):
            raise ValidationError("产品库存必须是有效的整数")
    
    # 描述验证
    if 'description' in data:
        description = data['description']
        if description is not None:
            if not isinstance(description, str):
                raise ValidationError("产品描述必须是字符串")
            if len(description) > 1000:
                raise ValidationError("产品描述长度不能超过1000个字符")
            data['description'] = description.strip()

def validate_order_data(data: Dict[str, Any], partial: bool = False) -> None:
    """
    验证订单数据
    :param data: 订单数据字典
    :param partial: 是否为部分更新
    """
    if not isinstance(data, dict):
        raise ValidationError("数据必须是字典格式")
    
    # 必需字段检查
    if not partial:
        required_fields = ['product_id', 'quantity']
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            raise ValidationError(f"缺少必需字段: {', '.join(missing_fields)}")
    
    # 产品ID验证
    if 'product_id' in data:
        try:
            product_id = int(data['product_id'])
            if product_id <= 0:
                raise ValidationError("产品ID必须是正整数")
            data['product_id'] = product_id
        except (ValueError, TypeError):
            raise ValidationError("产品ID必须是有效的整数")
    
    # 数量验证
    if 'quantity' in data:
        try:
            quantity = int(data['quantity'])
            if quantity <= 0:
                raise ValidationError("订单数量必须大于0")
            if quantity > 9999:
                raise ValidationError("订单数量不能超过9999")
            data['quantity'] = quantity
        except (ValueError, TypeError):
            raise ValidationError("订单数量必须是有效的整数")
    
    # 状态验证
    if 'status' in data:
        valid_statuses = ['pending', 'paid', 'shipped', 'delivered', 'cancelled']
        if data['status'] not in valid_statuses:
            raise ValidationError(f"订单状态必须是以下之一: {', '.join(valid_statuses)}")

    # 用户ID验证
    if 'user_id' in data and data['user_id'] is not None:
        try:
            user_id = int(data['user_id'])
            if user_id <= 0:
                raise ValidationError("用户ID必须是正整数")
        except (ValueError, TypeError):
            raise ValidationError("用户ID必须是有效的整数")

def validate_user_data(data: Dict[str, Any], partial: bool = False) -> None:
    """
    验证用户数据
    :param data: 用户数据字典
    :param partial: 是否为部分更新
    """
    if not isinstance(data, dict):
        raise ValidationError("数据必须是字典格式")
    
    # 必需字段检查
    if not partial:
        required_fields = ['username', 'email']
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            raise ValidationError(f"缺少必需字段: {', '.join(missing_fields)}")
    
    # 用户名验证
    if 'username' in data:
        username = data['username']
        if not isinstance(username, str) or not username.strip():
            raise ValidationError("用户名不能为空")
        if len(username.strip()) < 3 or len(username.strip()) > 50:
            raise ValidationError("用户名长度必须在3-50个字符之间")
        if not re.match(r'^[a-zA-Z0-9_]+$', username.strip()):
            raise ValidationError("用户名只能包含字母、数字和下划线")
        data['username'] = username.strip()
    
    # 邮箱验证
    if 'email' in data:
        email = data['email']
        if not isinstance(email, str) or not email.strip():
            raise ValidationError("邮箱不能为空")
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email.strip()):
            raise ValidationError("邮箱格式不正确")
        data['email'] = email.strip().lower()
    
    # 密码验证（仅在创建时）
    if 'password' in data:
        password = data['password']
        if not isinstance(password, str):
            raise ValidationError("密码必须是字符串")
        if len(password) < 6:
            raise ValidationError("密码长度不能少于6个字符")
        if len(password) > 128:
            raise ValidationError("密码长度不能超过128个字符")

def validate_pagination_params(page: int, limit: int) -> tuple:
    """
    验证分页参数
    :param page: 页码
    :param limit: 每页数量
    :return: 验证后的页码和每页数量
    """
    try:
        page = int(page)
        if page < 1:
            raise ValidationError("页码必须大于0")
    except (ValueError, TypeError):
        raise ValidationError("页码必须是有效的整数")
    
    try:
        limit = int(limit)
        if limit < 1:
            raise ValidationError("每页数量必须大于0")
        if limit > 100:
            raise ValidationError("每页数量不能超过100")
    except (ValueError, TypeError):
        raise ValidationError("每页数量必须是有效的整数")
    
    return page, limit

def validate_search_keyword(keyword: str) -> str:
    """
    验证搜索关键词
    :param keyword: 搜索关键词
    :return: 清理后的关键词
    """
    if not isinstance(keyword, str):
        raise ValidationError("搜索关键词必须是字符串")
    
    keyword = keyword.strip()
    if not keyword:
        raise ValidationError("搜索关键词不能为空")
    
    if len(keyword) > 100:
        raise ValidationError("搜索关键词长度不能超过100个字符")
    
    return keyword

def validate_id_list(ids: List[Any]) -> List[int]:
    """验证一个ID列表"""
    if not isinstance(ids, list):
        raise ValidationError("ID列表必须是一个数组")
    if not ids:
        raise ValidationError("ID列表不能为空")
    
    int_ids = []
    for i in ids:
        try:
            int_ids.append(int(i))
        except (ValueError, TypeError):
            raise ValidationError(f"ID '{i}' 不是一个有效的整数")
    return int_ids

def validate_date_range(start_date: Optional[str], end_date: Optional[str]) -> None:
    """
    验证日期范围.
    注意: 这是一个简单的验证。对于生产环境，建议使用更健壮的日期解析库。
    """
    date_format = r'^\d{4}-\d{2}-\d{2}$'
    if start_date and not re.match(date_format, start_date):
        raise ValidationError("start_date 格式不正确，应为 YYYY-MM-DD")
    if end_date and not re.match(date_format, end_date):
        raise ValidationError("end_date 格式不正确，应为 YYYY-MM-DD")
    if start_date and end_date and start_date > end_date:
        raise ValidationError("start_date 不能晚于 end_date")
