# backend/routes/delivery.py
"""
发货相关路由
处理虚拟商品的发货功能
"""

from flask import Blueprint, request, jsonify, session
from utils.db import get_db_connection
from delivery_service import delivery_service
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

delivery_bp = Blueprint('delivery', __name__, url_prefix='/api')

@delivery_bp.route('/delivery/process', methods=['POST'])
def process_delivery():
    """处理订单发货"""
    try:
        data = request.get_json()
        order_id = data.get('order_id')
        product_id = data.get('product_id')
        
        if not order_id or not product_id:
            return jsonify({'error': '缺少必要参数'}), 400
        
        # 获取订单信息
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, customer_email, total_price, status, created_at
            FROM orders 
            WHERE id = ?
        ''', (order_id,))
        
        order_row = cursor.fetchone()
        if not order_row:
            return jsonify({'error': '订单不存在'}), 404
        
        order = {
            'id': order_row[0],
            'customer_email': order_row[1],
            'total_price': order_row[2],
            'status': order_row[3],
            'created_at': order_row[4]
        }
        
        # 获取商品信息
        cursor.execute('''
            SELECT id, name, price, description, delivery_type, delivery_content,
                   email_subject, email_template, sender_email
            FROM products 
            WHERE id = ?
        ''', (product_id,))
        
        product_row = cursor.fetchone()
        if not product_row:
            return jsonify({'error': '商品不存在'}), 404
        
        product = {
            'id': product_row[0],
            'name': product_row[1],
            'price': product_row[2],
            'description': product_row[3],
            'delivery_type': product_row[4],
            'delivery_content': product_row[5],
            'email_subject': product_row[6],
            'email_template': product_row[7],
            'sender_email': product_row[8]
        }
        
        # 处理发货
        result = delivery_service.process_order_delivery(order, product)
        
        if result['success']:
            # 更新订单状态为已发货
            cursor.execute('''
                UPDATE orders 
                SET status = 'delivered', updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (order_id,))
            
            # 记录发货信息
            cursor.execute('''
                INSERT INTO delivery_records 
                (order_id, product_id, delivery_content, delivery_time, email_sent)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP, ?)
            ''', (order_id, product_id, result['delivery_content'], result['email_sent']))
            
            conn.commit()
        
        conn.close()
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"处理发货失败: {e}")
        return jsonify({'error': f'处理发货失败: {str(e)}'}), 500

@delivery_bp.route('/delivery/content/<int:order_id>/<int:product_id>', methods=['GET'])
def get_delivery_content(order_id, product_id):
    """获取发货内容"""
    try:
        # 验证订单和商品
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查发货记录
        cursor.execute('''
            SELECT delivery_content, delivery_time, email_sent
            FROM delivery_records 
            WHERE order_id = ? AND product_id = ?
        ''', (order_id, product_id))
        
        delivery_row = cursor.fetchone()
        if not delivery_row:
            return jsonify({'error': '发货记录不存在'}), 404
        
        # 获取订单信息
        cursor.execute('''
            SELECT customer_email, total_price, status
            FROM orders 
            WHERE id = ?
        ''', (order_id,))
        
        order_row = cursor.fetchone()
        if not order_row:
            return jsonify({'error': '订单不存在'}), 404
        
        # 获取商品信息
        cursor.execute('''
            SELECT name, price, description
            FROM products 
            WHERE id = ?
        ''', (product_id,))
        
        product_row = cursor.fetchone()
        if not product_row:
            return jsonify({'error': '商品不存在'}), 404
        
        conn.close()
        
        return jsonify({
            'order': {
                'id': order_id,
                'customer_email': order_row[0],
                'total_price': order_row[1],
                'status': order_row[2]
            },
            'product': {
                'id': product_id,
                'name': product_row[0],
                'price': product_row[1],
                'description': product_row[2]
            },
            'delivery': {
                'content': delivery_row[0],
                'delivery_time': delivery_row[1],
                'email_sent': delivery_row[2]
            }
        })
        
    except Exception as e:
        logger.error(f"获取发货内容失败: {e}")
        return jsonify({'error': f'获取发货内容失败: {str(e)}'}), 500

@delivery_bp.route('/delivery/send-email', methods=['POST'])
def send_delivery_email():
    """发送发货邮件"""
    try:
        data = request.get_json()
        
        required_fields = ['to_email', 'subject', 'content', 'order_id', 'product_name']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少必要字段: {field}'}), 400
        
        # 构建邮件数据
        email_data = {
            'to_email': data['to_email'],
            'subject': data['subject'],
            'content': data['content'],
            'sender_email': data.get('sender_email', '<EMAIL>')
        }
        
        # 发送邮件
        success = delivery_service._send_email(
            to_email=email_data['to_email'],
            subject=email_data['subject'],
            content=email_data['content'],
            sender_email=email_data['sender_email']
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': '邮件发送成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': '邮件发送失败'
            }), 500
            
    except Exception as e:
        logger.error(f"发送邮件失败: {e}")
        return jsonify({'error': f'发送邮件失败: {str(e)}'}), 500

@delivery_bp.route('/delivery/records', methods=['GET'])
def get_delivery_records():
    """获取发货记录"""
    try:
        # 检查管理员权限
        if not session.get('user') or not session.get('user', {}).get('is_admin'):
            return jsonify({'error': '需要管理员权限'}), 403
        
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取发货记录总数
        cursor.execute('SELECT COUNT(*) FROM delivery_records')
        total = cursor.fetchone()[0]
        
        # 获取发货记录
        offset = (page - 1) * per_page
        cursor.execute('''
            SELECT dr.id, dr.order_id, dr.product_id, dr.delivery_content,
                   dr.delivery_time, dr.email_sent,
                   p.name as product_name, o.customer_email
            FROM delivery_records dr
            LEFT JOIN products p ON dr.product_id = p.id
            LEFT JOIN orders o ON dr.order_id = o.id
            ORDER BY dr.delivery_time DESC
            LIMIT ? OFFSET ?
        ''', (per_page, offset))
        
        records = []
        for row in cursor.fetchall():
            records.append({
                'id': row[0],
                'order_id': row[1],
                'product_id': row[2],
                'delivery_content': row[3][:100] + '...' if len(row[3]) > 100 else row[3],
                'delivery_time': row[4],
                'email_sent': row[5],
                'product_name': row[6],
                'customer_email': row[7]
            })
        
        conn.close()
        
        return jsonify({
            'records': records,
            'total': total,
            'page': page,
            'per_page': per_page,
            'pages': (total + per_page - 1) // per_page
        })
        
    except Exception as e:
        logger.error(f"获取发货记录失败: {e}")
        return jsonify({'error': f'获取发货记录失败: {str(e)}'}), 500

@delivery_bp.route('/delivery/test-email', methods=['POST'])
def test_email():
    """测试邮件发送"""
    try:
        # 检查管理员权限
        if not session.get('user') or not session.get('user', {}).get('is_admin'):
            return jsonify({'error': '需要管理员权限'}), 403
        
        data = request.get_json()
        test_email = data.get('test_email', '<EMAIL>')
        
        # 发送测试邮件
        success = delivery_service._send_email(
            to_email=test_email,
            subject='【青云小铺】邮件服务测试',
            content='''这是一封测试邮件。

如果您收到这封邮件，说明邮件服务配置正确。

测试时间：{test_time}

青云小铺
<EMAIL>'''.format(test_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
            sender_email='<EMAIL>'
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': f'测试邮件已发送到 {test_email}'
            })
        else:
            return jsonify({
                'success': False,
                'message': '测试邮件发送失败'
            }), 500
            
    except Exception as e:
        logger.error(f"测试邮件发送失败: {e}")
        return jsonify({'error': f'测试邮件发送失败: {str(e)}'}), 500
