from flask import Blueprint, request, jsonify, session
from models import Order, Product
from utils.exceptions import ValidationError, NotFoundError, BusinessLogicError

order_bp = Blueprint('order', __name__)

@order_bp.route('/api/orders', methods=['GET'])
def get_orders():
    """获取订单列表"""
    try:
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))
        status = request.args.get('status')

        # 如果用户已登录，只显示自己的订单（除非是管理员）
        user_id = session.get('user_id')
        is_admin = session.get('is_admin', False)

        if user_id and not is_admin:
            result = Order.get_by_user(user_id, page, limit)
        else:
            result = Order.get_paginated(page, limit, status=status)

        return jsonify(result)

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'获取订单列表失败: {str(e)}'}), 500

@order_bp.route('/api/orders/<int:order_id>', methods=['GET'])
def get_order(order_id):
    """获取单个订单"""
    try:
        order = Order.get_by_id(order_id)
        if not order:
            return jsonify({'error': '订单不存在'}), 404

        # 检查权限：只有订单所有者或管理员可以查看
        user_id = session.get('user_id')
        is_admin = session.get('is_admin', False)

        if not is_admin and order.get('user_id') != user_id:
            return jsonify({'error': '权限不足'}), 403

        return jsonify(order)

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'获取订单失败: {str(e)}'}), 500

@order_bp.route('/api/orders', methods=['POST'])
def create_order():
    """创建新订单"""
    try:
        data = request.json
        if not data:
            return jsonify({'error': '请提供订单数据'}), 400

        # 设置用户ID（如果已登录）
        user_id = session.get('user_id')
        if user_id:
            data['user_id'] = user_id

        # 创建订单
        order = Order.create(data)

        return jsonify({
            'message': '订单创建成功',
            'order': order
        }), 201

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except NotFoundError as e:
        return jsonify({'error': e.message}), 404
    except BusinessLogicError as e:
        return jsonify({'error': e.message}), 422
    except Exception as e:
        return jsonify({'error': f'创建订单失败: {str(e)}'}), 500

@order_bp.route('/api/orders/<int:order_id>', methods=['PUT'])
def update_order(order_id):
    """更新订单信息"""
    try:
        data = request.json
        if not data:
            return jsonify({'error': '请提供更新数据'}), 400

        # 检查权限：只有管理员可以更新订单
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403

        order = Order.update(order_id, data)
        if order:
            return jsonify({
                'message': '订单更新成功',
                'order': order
            })
        else:
            return jsonify({'error': '订单不存在'}), 404

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'更新订单失败: {str(e)}'}), 500

@order_bp.route('/api/orders/<int:order_id>', methods=['DELETE'])
def delete_order(order_id):
    """删除订单"""
    try:
        # 检查权限：只有管理员可以删除订单
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403

        if Order.delete(order_id):
            return jsonify({'message': '订单删除成功'})
        else:
            return jsonify({'error': '订单不存在'}), 404

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'删除订单失败: {str(e)}'}), 500

@order_bp.route('/api/orders/<int:order_id>/status', methods=['PUT'])
def update_order_status(order_id):
    """更新订单状态"""
    try:
        data = request.json
        if not data or 'status' not in data:
            return jsonify({'error': '请提供订单状态'}), 400

        # 检查权限：只有管理员可以更新订单状态
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403

        order = Order.update(order_id, {'status': data['status']})
        if order:
            return jsonify({
                'message': '订单状态更新成功',
                'order': order
            })
        else:
            return jsonify({'error': '订单不存在'}), 404

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'更新订单状态失败: {str(e)}'}), 500

@order_bp.route('/api/orders/my', methods=['GET'])
def get_my_orders():
    """获取当前用户的订单列表"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'error': '未登录'}), 401

        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))

        result = Order.get_by_user(user_id, page, limit)
        return jsonify(result)

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'获取订单列表失败: {str(e)}'}), 500

@order_bp.route('/api/orders/status/<status>', methods=['GET'])
def get_orders_by_status(status):
    """根据状态获取订单列表"""
    try:
        # 检查权限：只有管理员可以查看所有状态的订单
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403

        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))

        result = Order.get_by_status(status, page, limit)
        return jsonify(result)

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'获取订单列表失败: {str(e)}'}), 500