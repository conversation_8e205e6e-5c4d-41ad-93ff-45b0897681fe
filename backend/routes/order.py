from flask import Blueprint, request, jsonify
from models import Order
from utils.payment import create_payment  # 预留支付接口 [[7]]

order_bp = Blueprint('order', __name__)

@order_bp.route('/api/order', methods=['POST'])
def create_order():
    data = request.json
    new_order = Order(
        product_id=data['product_id'],
        user_id=1,  # 假设用户已登录
        amount=data['amount'],
        status='pending'
    )
    db.session.add(new_order)
    db.session.commit()

    # 预留支付接口调用
    payment_url = create_payment(new_order.id, new_order.amount)  # [[7]]

    return jsonify({
        'order_id': new_order.id,
        'payment_url': payment_url
    })