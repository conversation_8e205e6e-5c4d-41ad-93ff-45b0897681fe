from flask import Blueprint, request, jsonify, session
from models import Order, Product, User
from utils.exceptions import (
    ValidationError, NotFoundError, BusinessLogicError,
    PermissionDeniedError, ConflictError, DatabaseError
)
from utils.validators import validate_pagination_params
from datetime import datetime, timedelta
import logging

order_bp = Blueprint('order', __name__)
logger = logging.getLogger(__name__)

def require_login():
    """检查用户是否登录"""
    if not session.get('user_id'):
        raise PermissionDeniedError('请先登录')

def require_admin():
    """检查是否是管理员"""
    if not session.get('is_admin', False):
        raise PermissionDeniedError('需要管理员权限')

def get_current_user_id() -> int:
    """获取当前用户ID"""
    user_id = session.get('user_id')
    if not user_id:
        raise PermissionDeniedError('请先登录')
    return user_id

def check_order_ownership(order: dict, user_id: int, is_admin: bool = False) -> bool:
    """检查订单所有权"""
    if is_admin:
        return True
    return order.get('user_id') == user_id

def log_order_operation(operation: str, order_id: int = None, user_id: int = None, **kwargs):
    """记录订单操作日志"""
    log_data = {
        'operation': operation,
        'order_id': order_id,
        'user_id': user_id,
        'timestamp': datetime.now().isoformat(),
        **kwargs
    }
    logger.info(f"Order operation: {log_data}")

@order_bp.route('/api/orders', methods=['GET'])
def get_orders():
    """获取订单列表 - 支持多种过滤条件"""
    try:
        # 解析查询参数
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))
        status = request.args.get('status')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        min_amount = request.args.get('min_amount')
        max_amount = request.args.get('max_amount')
        product_id = request.args.get('product_id')
        sort_by = request.args.get('sort_by', 'created_at')  # created_at, total_price, status
        sort_order = request.args.get('sort_order', 'desc')  # asc, desc

        # 验证分页参数
        if page < 1:
            raise ValidationError("页码必须大于0")
        if limit < 1 or limit > 100:
            raise ValidationError("每页数量必须在1-100之间")

        # 验证排序参数
        valid_sort_fields = ['created_at', 'total_price', 'status', 'id']
        if sort_by not in valid_sort_fields:
            raise ValidationError(f"排序字段必须是以下之一: {', '.join(valid_sort_fields)}")

        if sort_order not in ['asc', 'desc']:
            raise ValidationError("排序方向必须是 'asc' 或 'desc'")

        # 获取当前用户信息
        user_id = session.get('user_id')
        is_admin = session.get('is_admin', False)

        # 构建过滤条件
        filters = {
            'status': status,
            'start_date': start_date,
            'end_date': end_date,
            'min_amount': float(min_amount) if min_amount else None,
            'max_amount': float(max_amount) if max_amount else None,
            'product_id': int(product_id) if product_id else None,
            'sort_by': sort_by,
            'sort_order': sort_order
        }

        # 权限检查和数据获取
        if user_id and not is_admin:
            # 普通用户只能查看自己的订单
            result = Order.get_by_user_with_filters(user_id, page, limit, filters)
            log_order_operation("get_user_orders", user_id=user_id, filters=filters)
        else:
            # 管理员或未登录用户（公开订单）
            if not is_admin:
                # 未登录用户只能看到有限信息
                filters['public_only'] = True

            result = Order.get_paginated_with_filters(page, limit, filters)
            log_order_operation("get_all_orders", user_id=user_id, is_admin=is_admin, filters=filters)

        return jsonify(result)

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except PermissionDeniedError as e:
        return jsonify({'error': e.message}), 403
    except Exception as e:
        logger.error(f"获取订单列表失败: {str(e)}")
        return jsonify({'error': f'获取订单列表失败: {str(e)}'}), 500

@order_bp.route('/api/orders/<int:order_id>', methods=['GET'])
def get_order(order_id):
    """获取单个订单详情"""
    try:
        # 验证订单ID
        if order_id <= 0:
            raise ValidationError("订单ID必须是正整数")

        # 获取订单信息
        order = Order.get_by_id(order_id)
        if not order:
            raise NotFoundError('订单不存在')

        # 权限检查
        user_id = session.get('user_id')
        is_admin = session.get('is_admin', False)

        if not check_order_ownership(order, user_id, is_admin):
            raise PermissionDeniedError('权限不足，只能查看自己的订单')

        # 获取订单详细信息（包括产品详情、用户信息等）
        detailed_order = Order.get_detailed_by_id(order_id)
        if not detailed_order:
            detailed_order = order  # 降级到基础信息

        # 记录操作日志
        log_order_operation("get_order_detail", order_id=order_id, user_id=user_id)

        return jsonify({
            'order': detailed_order,
            'permissions': {
                'can_edit': is_admin,
                'can_cancel': is_admin or (order.get('status') == 'pending' and order.get('user_id') == user_id),
                'can_view_full_details': is_admin or order.get('user_id') == user_id
            }
        })

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except NotFoundError as e:
        return jsonify({'error': e.message}), 404
    except PermissionDeniedError as e:
        return jsonify({'error': e.message}), 403
    except Exception as e:
        logger.error(f"获取订单详情失败: order_id={order_id}, error={str(e)}")
        return jsonify({'error': f'获取订单失败: {str(e)}'}), 500

@order_bp.route('/api/orders', methods=['POST'])
def create_order():
    """创建新订单"""
    try:
        data = request.json
        if not data:
            raise ValidationError('请提供订单数据')

        # 验证必需字段
        required_fields = ['product_id', 'quantity']
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            raise ValidationError(f'缺少必需字段: {", ".join(missing_fields)}')

        # 获取当前用户信息
        user_id = session.get('user_id')
        is_admin = session.get('is_admin', False)

        # 设置用户ID
        if user_id:
            data['user_id'] = user_id
        elif not data.get('customer_info'):
            # 如果未登录且没有客户信息，要求提供客户信息
            raise ValidationError('未登录用户必须提供客户信息')

        # 验证产品ID和数量
        product_id = data['product_id']
        quantity = data['quantity']

        if not isinstance(product_id, int) or product_id <= 0:
            raise ValidationError('产品ID必须是正整数')

        if not isinstance(quantity, int) or quantity <= 0:
            raise ValidationError('订单数量必须是正整数')

        if quantity > 999:
            raise ValidationError('单次订单数量不能超过999')

        # 检查产品是否存在和可用
        product = Product.get_by_id(product_id)
        if not product:
            raise NotFoundError(f'产品ID {product_id} 不存在')

        # 检查库存
        if product['stock'] < quantity:
            raise BusinessLogicError(f'产品 "{product["name"]}" 库存不足，当前库存: {product["stock"]}')

        # 计算订单金额
        unit_price = product['price']
        total_price = unit_price * quantity
        data['unit_price'] = unit_price
        data['total_price'] = total_price

        # 添加订单备注（如果提供）
        if 'notes' in data and len(data['notes']) > 500:
            raise ValidationError('订单备注不能超过500个字符')

        # 创建订单
        order = Order.create(data)

        # 记录操作日志
        log_order_operation(
            "create_order",
            order_id=order['id'],
            user_id=user_id,
            product_id=product_id,
            quantity=quantity,
            total_price=total_price
        )

        # 返回创建结果
        return jsonify({
            'message': '订单创建成功',
            'order': order,
            'product_info': {
                'name': product['name'],
                'unit_price': unit_price,
                'remaining_stock': product['stock'] - quantity
            }
        }), 201

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except NotFoundError as e:
        return jsonify({'error': e.message}), 404
    except BusinessLogicError as e:
        return jsonify({'error': e.message}), 422
    except PermissionDeniedError as e:
        return jsonify({'error': e.message}), 403
    except Exception as e:
        logger.error(f"创建订单失败: data={data}, error={str(e)}")
        return jsonify({'error': f'创建订单失败: {str(e)}'}), 500

@order_bp.route('/api/orders/<int:order_id>', methods=['PUT'])
def update_order(order_id):
    """更新订单信息"""
    try:
        data = request.json
        if not data:
            return jsonify({'error': '请提供更新数据'}), 400

        # 检查权限：只有管理员可以更新订单
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403

        order = Order.update(order_id, data)
        if order:
            return jsonify({
                'message': '订单更新成功',
                'order': order
            })
        else:
            return jsonify({'error': '订单不存在'}), 404

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'更新订单失败: {str(e)}'}), 500

@order_bp.route('/api/orders/<int:order_id>', methods=['DELETE'])
def delete_order(order_id):
    """删除订单"""
    try:
        # 检查权限：只有管理员可以删除订单
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403

        if Order.delete(order_id):
            return jsonify({'message': '订单删除成功'})
        else:
            return jsonify({'error': '订单不存在'}), 404

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'删除订单失败: {str(e)}'}), 500

@order_bp.route('/api/orders/<int:order_id>/status', methods=['PUT'])
def update_order_status(order_id):
    """更新订单状态"""
    try:
        data = request.json
        if not data or 'status' not in data:
            return jsonify({'error': '请提供订单状态'}), 400

        # 检查权限：只有管理员可以更新订单状态
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403

        order = Order.update(order_id, {'status': data['status']})
        if order:
            return jsonify({
                'message': '订单状态更新成功',
                'order': order
            })
        else:
            return jsonify({'error': '订单不存在'}), 404

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'更新订单状态失败: {str(e)}'}), 500

@order_bp.route('/api/orders/my', methods=['GET'])
def get_my_orders():
    """获取当前用户的订单列表"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'error': '未登录'}), 401

        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))

        result = Order.get_by_user(user_id, page, limit)
        return jsonify(result)

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'获取订单列表失败: {str(e)}'}), 500

@order_bp.route('/api/orders/status/<status>', methods=['GET'])
def get_orders_by_status(status):
    """根据状态获取订单列表"""
    try:
        # 检查权限：只有管理员可以查看所有状态的订单
        require_admin()

        # 验证状态参数
        valid_statuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded']
        if status not in valid_statuses:
            raise ValidationError(f'无效的订单状态: {status}，有效状态: {", ".join(valid_statuses)}')

        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))

        result = Order.get_by_status(status, page, limit)

        log_order_operation("get_orders_by_status", user_id=session.get('user_id'), status=status)

        return jsonify(result)

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except PermissionDeniedError as e:
        return jsonify({'error': e.message}), 403
    except Exception as e:
        logger.error(f"根据状态获取订单失败: status={status}, error={str(e)}")
        return jsonify({'error': f'获取订单列表失败: {str(e)}'}), 500

@order_bp.route('/api/orders/<int:order_id>/cancel', methods=['POST'])
def cancel_order(order_id):
    """取消订单"""
    try:
        # 验证订单ID
        if order_id <= 0:
            raise ValidationError("订单ID必须是正整数")

        # 获取订单信息
        order = Order.get_by_id(order_id)
        if not order:
            raise NotFoundError('订单不存在')

        # 权限检查
        user_id = session.get('user_id')
        is_admin = session.get('is_admin', False)

        # 只有订单所有者或管理员可以取消订单
        if not check_order_ownership(order, user_id, is_admin):
            raise PermissionDeniedError('权限不足，只能取消自己的订单')

        # 检查订单状态
        if order['status'] != 'pending':
            raise BusinessLogicError(f'只能取消待处理状态的订单，当前状态: {order["status"]}')

        # 获取取消原因
        data = request.json or {}
        cancel_reason = data.get('reason', '用户取消')

        if len(cancel_reason) > 200:
            raise ValidationError('取消原因不能超过200个字符')

        # 执行取消操作
        cancelled_order = Order.cancel(order_id, cancel_reason)

        # 记录操作日志
        log_order_operation(
            "cancel_order",
            order_id=order_id,
            user_id=user_id,
            reason=cancel_reason,
            original_status=order['status']
        )

        return jsonify({
            'message': '订单取消成功',
            'order': cancelled_order,
            'cancel_info': {
                'reason': cancel_reason,
                'cancelled_at': datetime.now().isoformat(),
                'cancelled_by': 'admin' if is_admin else 'customer'
            }
        })

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except NotFoundError as e:
        return jsonify({'error': e.message}), 404
    except BusinessLogicError as e:
        return jsonify({'error': e.message}), 422
    except PermissionDeniedError as e:
        return jsonify({'error': e.message}), 403
    except Exception as e:
        logger.error(f"取消订单失败: order_id={order_id}, error={str(e)}")
        return jsonify({'error': f'取消订单失败: {str(e)}'}), 500

@order_bp.route('/api/orders/<int:order_id>/notes', methods=['PUT'])
def update_order_notes(order_id):
    """更新订单备注"""
    try:
        # 验证订单ID
        if order_id <= 0:
            raise ValidationError("订单ID必须是正整数")

        # 获取订单信息
        order = Order.get_by_id(order_id)
        if not order:
            raise NotFoundError('订单不存在')

        # 权限检查 - 只有管理员可以更新订单备注
        require_admin()

        # 获取新的备注内容
        data = request.json
        if not data or 'notes' not in data:
            raise ValidationError('请提供备注内容')

        notes = data['notes']
        if len(notes) > 1000:
            raise ValidationError('备注内容不能超过1000个字符')

        # 更新订单备注
        updated_order = Order.update_notes(order_id, notes)

        # 记录操作日志
        log_order_operation(
            "update_order_notes",
            order_id=order_id,
            user_id=session.get('user_id'),
            notes_length=len(notes)
        )

        return jsonify({
            'message': '订单备注更新成功',
            'order': updated_order
        })

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except NotFoundError as e:
        return jsonify({'error': e.message}), 404
    except PermissionDeniedError as e:
        return jsonify({'error': e.message}), 403
    except Exception as e:
        logger.error(f"更新订单备注失败: order_id={order_id}, error={str(e)}")
        return jsonify({'error': f'更新订单备注失败: {str(e)}'}), 500

@order_bp.route('/api/orders/statistics', methods=['GET'])
def get_order_statistics():
    """获取订单统计信息"""
    try:
        # 检查权限：只有管理员可以查看统计信息
        require_admin()

        # 获取统计数据
        stats = Order.get_statistics()

        # 获取状态分布
        from utils.db import get_order_status_distribution
        status_distribution = get_order_status_distribution()

        # 记录操作日志
        log_order_operation("get_order_statistics", user_id=session.get('user_id'))

        return jsonify({
            'statistics': stats,
            'status_distribution': status_distribution,
            'generated_at': datetime.now().isoformat()
        })

    except PermissionDeniedError as e:
        return jsonify({'error': e.message}), 403
    except Exception as e:
        logger.error(f"获取订单统计失败: error={str(e)}")
        return jsonify({'error': f'获取订单统计失败: {str(e)}'}), 500

@order_bp.route('/api/orders/export', methods=['GET'])
def export_orders():
    """导出订单数据"""
    try:
        # 检查权限：只有管理员可以导出数据
        require_admin()

        # 获取查询参数
        status = request.args.get('status')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        format_type = request.args.get('format', 'json')  # json, csv

        # 构建过滤条件
        filters = {
            'status': status,
            'start_date': start_date,
            'end_date': end_date,
            'sort_by': 'created_at',
            'sort_order': 'desc'
        }

        # 获取所有符合条件的订单（不分页）
        all_orders = Order.get_paginated_with_filters(1, 10000, filters)
        orders = all_orders['items']

        # 记录操作日志
        log_order_operation(
            "export_orders",
            user_id=session.get('user_id'),
            filters=filters,
            count=len(orders),
            format=format_type
        )

        if format_type == 'csv':
            # 返回CSV格式数据
            import csv
            import io

            output = io.StringIO()
            writer = csv.writer(output)

            # 写入表头
            headers = ['订单ID', '产品名称', '数量', '单价', '总价', '状态', '创建时间', '备注']
            writer.writerow(headers)

            # 写入数据
            for order in orders:
                writer.writerow([
                    order['id'],
                    order.get('product_name', ''),
                    order['quantity'],
                    order.get('unit_price', order['total_price'] / order['quantity']),
                    order['total_price'],
                    order['status'],
                    order.get('created_at', ''),
                    order.get('notes', '')
                ])

            csv_data = output.getvalue()
            output.close()

            return jsonify({
                'format': 'csv',
                'data': csv_data,
                'filename': f'orders_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv',
                'count': len(orders)
            })
        else:
            # 返回JSON格式数据
            return jsonify({
                'format': 'json',
                'data': orders,
                'filters': filters,
                'count': len(orders),
                'exported_at': datetime.now().isoformat()
            })

    except PermissionDeniedError as e:
        return jsonify({'error': e.message}), 403
    except Exception as e:
        logger.error(f"导出订单数据失败: error={str(e)}")
        return jsonify({'error': f'导出订单数据失败: {str(e)}'}), 500

@order_bp.route('/api/orders/batch/update-status', methods=['POST'])
def batch_update_order_status():
    """批量更新订单状态"""
    try:
        # 检查权限：只有管理员可以批量更新
        require_admin()

        data = request.json
        if not data:
            raise ValidationError('请提供更新数据')

        order_ids = data.get('order_ids', [])
        new_status = data.get('status')

        if not order_ids:
            raise ValidationError('请提供订单ID列表')

        if not new_status:
            raise ValidationError('请提供新的订单状态')

        # 验证状态
        valid_statuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded']
        if new_status not in valid_statuses:
            raise ValidationError(f'无效的订单状态: {new_status}')

        # 验证订单ID
        if not isinstance(order_ids, list) or len(order_ids) > 100:
            raise ValidationError('订单ID列表格式错误或数量超过100个')

        success_count = 0
        failed_orders = []

        # 批量更新
        for order_id in order_ids:
            try:
                if not isinstance(order_id, int) or order_id <= 0:
                    failed_orders.append({'id': order_id, 'error': '无效的订单ID'})
                    continue

                # 更新订单状态
                updated_order = Order.update(order_id, {'status': new_status})
                if updated_order:
                    success_count += 1
                else:
                    failed_orders.append({'id': order_id, 'error': '订单不存在'})

            except Exception as e:
                failed_orders.append({'id': order_id, 'error': str(e)})

        # 记录操作日志
        log_order_operation(
            "batch_update_status",
            user_id=session.get('user_id'),
            order_ids=order_ids,
            new_status=new_status,
            success_count=success_count,
            failed_count=len(failed_orders)
        )

        return jsonify({
            'message': '批量更新完成',
            'success_count': success_count,
            'failed_count': len(failed_orders),
            'failed_orders': failed_orders,
            'new_status': new_status
        })

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except PermissionDeniedError as e:
        return jsonify({'error': e.message}), 403
    except Exception as e:
        logger.error(f"批量更新订单状态失败: error={str(e)}")
        return jsonify({'error': f'批量更新订单状态失败: {str(e)}'}), 500