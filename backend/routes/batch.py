# backend/routes/batch.py
from flask import Blueprint, request, jsonify, session
from models import Product, Order
from utils.exceptions import ValidationError, NotFoundError, BusinessLogicError
from utils.db import batch_delete_products

batch_bp = Blueprint('batch', __name__)

@batch_bp.route('/api/batch/products/delete', methods=['POST'])
def batch_delete_products_route():
    """批量删除产品"""
    try:
        # 检查权限：只有管理员可以批量删除
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        data = request.json
        if not data or 'product_ids' not in data:
            return jsonify({'error': '请提供产品ID列表'}), 400
        
        product_ids = data['product_ids']
        if not isinstance(product_ids, list) or not product_ids:
            return jsonify({'error': '产品ID列表不能为空'}), 400
        
        # 验证所有ID都是整数
        for product_id in product_ids:
            if not isinstance(product_id, int) or product_id <= 0:
                return jsonify({'error': f'无效的产品ID: {product_id}'}), 400
        
        # 执行批量删除
        result = batch_delete_products(product_ids)
        
        return jsonify({
            'message': '批量删除完成',
            'result': result
        })
        
    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'批量删除失败: {str(e)}'}), 500

@batch_bp.route('/api/batch/products/update', methods=['POST'])
def batch_update_products():
    """批量更新产品"""
    try:
        # 检查权限：只有管理员可以批量更新
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        data = request.json
        if not data or 'updates' not in data:
            return jsonify({'error': '请提供更新数据'}), 400
        
        updates = data['updates']
        if not isinstance(updates, list) or not updates:
            return jsonify({'error': '更新数据不能为空'}), 400
        
        success_count = 0
        failed_updates = []
        
        for update in updates:
            try:
                product_id = update.get('id')
                update_data = update.get('data', {})
                
                if not product_id or not update_data:
                    failed_updates.append({
                        'id': product_id,
                        'error': '缺少产品ID或更新数据'
                    })
                    continue
                
                # 更新产品
                updated_product = Product.update(product_id, update_data)
                if updated_product:
                    success_count += 1
                else:
                    failed_updates.append({
                        'id': product_id,
                        'error': '产品不存在'
                    })
                    
            except Exception as e:
                failed_updates.append({
                    'id': update.get('id'),
                    'error': str(e)
                })
        
        return jsonify({
            'message': '批量更新完成',
            'result': {
                'success_count': success_count,
                'failed_count': len(failed_updates),
                'failed_updates': failed_updates
            }
        })
        
    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'批量更新失败: {str(e)}'}), 500

@batch_bp.route('/api/batch/orders/update-status', methods=['POST'])
def batch_update_order_status():
    """批量更新订单状态"""
    try:
        # 检查权限：只有管理员可以批量更新订单状态
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        data = request.json
        if not data or 'order_ids' not in data or 'status' not in data:
            return jsonify({'error': '请提供订单ID列表和新状态'}), 400
        
        order_ids = data['order_ids']
        new_status = data['status']
        
        if not isinstance(order_ids, list) or not order_ids:
            return jsonify({'error': '订单ID列表不能为空'}), 400
        
        # 验证状态
        valid_statuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled']
        if new_status not in valid_statuses:
            return jsonify({'error': f'无效的订单状态: {new_status}'}), 400
        
        success_count = 0
        failed_updates = []
        
        for order_id in order_ids:
            try:
                if not isinstance(order_id, int) or order_id <= 0:
                    failed_updates.append({
                        'id': order_id,
                        'error': '无效的订单ID'
                    })
                    continue
                
                # 更新订单状态
                updated_order = Order.update_status(order_id, new_status)
                if updated_order:
                    success_count += 1
                else:
                    failed_updates.append({
                        'id': order_id,
                        'error': '订单不存在'
                    })
                    
            except Exception as e:
                failed_updates.append({
                    'id': order_id,
                    'error': str(e)
                })
        
        return jsonify({
            'message': '批量更新订单状态完成',
            'result': {
                'success_count': success_count,
                'failed_count': len(failed_updates),
                'failed_updates': failed_updates,
                'new_status': new_status
            }
        })
        
    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'批量更新订单状态失败: {str(e)}'}), 500

@batch_bp.route('/api/batch/products/export', methods=['GET'])
def export_products():
    """导出产品数据"""
    try:
        # 检查权限：只有管理员可以导出数据
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        # 获取所有产品
        products = Product.get_all()
        
        # 格式化导出数据
        export_data = []
        for product in products:
            export_data.append({
                'ID': product['id'],
                '产品名称': product['name'],
                '价格': product['price'],
                '库存': product['stock'],
                '描述': product.get('description', ''),
                '创建时间': product.get('created_at', ''),
                '更新时间': product.get('updated_at', '')
            })
        
        return jsonify({
            'message': '产品数据导出成功',
            'data': export_data,
            'total_count': len(export_data),
            'export_time': Product._get_current_time() if hasattr(Product, '_get_current_time') else 'unknown'
        })
        
    except Exception as e:
        return jsonify({'error': f'导出产品数据失败: {str(e)}'}), 500

@batch_bp.route('/api/batch/orders/export', methods=['GET'])
def export_orders():
    """导出订单数据"""
    try:
        # 检查权限：只有管理员可以导出数据
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        # 获取查询参数
        status = request.args.get('status')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # 获取订单数据
        if status:
            orders_data = Order.get_by_status(status, page=1, limit=10000)
        else:
            orders_data = Order.get_paginated(page=1, limit=10000)
        
        orders = orders_data['items']
        
        # 格式化导出数据
        export_data = []
        for order in orders:
            export_data.append({
                'ID': order['id'],
                '产品ID': order['product_id'],
                '产品名称': order.get('product_name', ''),
                '数量': order['quantity'],
                '总价': order['total_price'],
                '状态': order['status'],
                '创建时间': order.get('created_at', '')
            })
        
        return jsonify({
            'message': '订单数据导出成功',
            'data': export_data,
            'total_count': len(export_data),
            'filters': {
                'status': status,
                'start_date': start_date,
                'end_date': end_date
            }
        })
        
    except Exception as e:
        return jsonify({'error': f'导出订单数据失败: {str(e)}'}), 500

@batch_bp.route('/api/batch/products/import', methods=['POST'])
def import_products():
    """批量导入产品"""
    try:
        # 检查权限：只有管理员可以导入数据
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        data = request.json
        if not data or 'products' not in data:
            return jsonify({'error': '请提供产品数据'}), 400
        
        products_data = data['products']
        if not isinstance(products_data, list) or not products_data:
            return jsonify({'error': '产品数据不能为空'}), 400
        
        success_count = 0
        failed_imports = []
        
        for i, product_data in enumerate(products_data):
            try:
                # 验证必需字段
                required_fields = ['name', 'price', 'stock']
                missing_fields = [field for field in required_fields if field not in product_data]
                
                if missing_fields:
                    failed_imports.append({
                        'index': i,
                        'data': product_data,
                        'error': f'缺少必需字段: {", ".join(missing_fields)}'
                    })
                    continue
                
                # 创建产品
                created_product = Product.create(product_data)
                if created_product:
                    success_count += 1
                    
            except Exception as e:
                failed_imports.append({
                    'index': i,
                    'data': product_data,
                    'error': str(e)
                })
        
        return jsonify({
            'message': '批量导入完成',
            'result': {
                'success_count': success_count,
                'failed_count': len(failed_imports),
                'failed_imports': failed_imports
            }
        })
        
    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'批量导入失败: {str(e)}'}), 500
