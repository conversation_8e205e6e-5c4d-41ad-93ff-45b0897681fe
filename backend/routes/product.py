# backend/routes/product.py
from flask import Blueprint, request, jsonify
from models import Product

product_bp = Blueprint('product', __name__)

@product_bp.route('/api/products', methods=['GET'])
def get_products():
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 10))
    result = Product.get_paginated(page, limit)
    return jsonify(result)

@product_bp.route('/api/products/<int:product_id>', methods=['GET'])
def get_product(product_id):
    product = Product.get_by_id(product_id)
    if product:
        return jsonify(product)
    return jsonify({'error': 'Product not found'}), 404

@product_bp.route('/api/products', methods=['POST'])
def create_product():
    data = request.json
    new_product = Product.create(data)
    return jsonify(new_product), 201

@product_bp.route('/api/products/<int:product_id>', methods=['PUT'])
def update_product(product_id):
    data = request.json
    updated = Product.update(product_id, data)
    if updated:
        return jsonify(updated)
    return jsonify({'error': 'Product not found'}), 404

@product_bp.route('/api/products/<int:product_id>', methods=['DELETE'])
def delete_product(product_id):
    if Product.delete(product_id):
        return jsonify({'message': 'Product deleted'})
    return jsonify({'error': 'Product not found'}), 404
