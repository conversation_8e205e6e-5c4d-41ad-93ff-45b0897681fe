# backend/routes/product.py
from flask import Blueprint, request, jsonify, session
from models import Product
from utils.exceptions import ValidationError, NotFoundError, BusinessLogicError

product_bp = Blueprint('product', __name__)

@product_bp.route('/api/products', methods=['GET'])
def get_products():
    """获取产品列表"""
    try:
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))
        search = request.args.get('search')

        result = Product.get_paginated(page, limit, search)
        return jsonify(result)

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'获取产品列表失败: {str(e)}'}), 500

@product_bp.route('/api/products/<int:product_id>', methods=['GET'])
def get_product(product_id):
    """获取单个产品"""
    try:
        product = Product.get_by_id(product_id)
        if product:
            return jsonify(product)
        return jsonify({'error': '产品不存在'}), 404

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'获取产品失败: {str(e)}'}), 500

@product_bp.route('/api/products', methods=['POST'])
def create_product():
    """创建新产品"""
    try:
        # 检查权限：只有管理员可以创建产品
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403

        data = request.json
        if not data:
            return jsonify({'error': '请提供产品数据'}), 400

        product = Product.create(data)
        return jsonify({
            'message': '产品创建成功',
            'product': product
        }), 201

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'创建产品失败: {str(e)}'}), 500

@product_bp.route('/api/products/<int:product_id>', methods=['PUT'])
def update_product(product_id):
    """更新产品信息"""
    try:
        # 检查权限：只有管理员可以更新产品
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403

        data = request.json
        if not data:
            return jsonify({'error': '请提供更新数据'}), 400

        product = Product.update(product_id, data)
        if product:
            return jsonify({
                'message': '产品更新成功',
                'product': product
            })
        return jsonify({'error': '产品不存在'}), 404

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except NotFoundError as e:
        return jsonify({'error': e.message}), 404
    except Exception as e:
        return jsonify({'error': f'更新产品失败: {str(e)}'}), 500

@product_bp.route('/api/products/<int:product_id>', methods=['DELETE'])
def delete_product(product_id):
    """删除产品"""
    try:
        # 检查权限：只有管理员可以删除产品
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403

        if Product.delete(product_id):
            return jsonify({'message': '产品删除成功'})
        return jsonify({'error': '产品不存在'}), 404

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except NotFoundError as e:
        return jsonify({'error': e.message}), 404
    except Exception as e:
        return jsonify({'error': f'删除产品失败: {str(e)}'}), 500

@product_bp.route('/api/products/search', methods=['GET'])
def search_products():
    """搜索产品"""
    try:
        keyword = request.args.get('keyword')
        if not keyword:
            return jsonify({'error': '请提供搜索关键词'}), 400

        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))

        result = Product.search(keyword, page, limit)
        return jsonify(result)

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'搜索产品失败: {str(e)}'}), 500

@product_bp.route('/api/products/low-stock', methods=['GET'])
def get_low_stock_products():
    """获取库存不足的产品"""
    try:
        # 检查权限：只有管理员可以查看库存不足的产品
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403

        threshold = int(request.args.get('threshold', 10))
        products = Product.get_low_stock(threshold)

        return jsonify({
            'threshold': threshold,
            'count': len(products),
            'products': products
        })

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'获取低库存产品失败: {str(e)}'}), 500

@product_bp.route('/api/products/batch', methods=['DELETE'])
def batch_delete_products():
    """批量删除产品"""
    try:
        # 检查权限：只有管理员可以批量删除产品
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403

        data = request.json
        if not data or 'product_ids' not in data:
            return jsonify({'error': '请提供产品ID列表'}), 400

        result = Product.batch_delete(data['product_ids'])
        return jsonify({
            'message': '批量删除完成',
            'result': result
        })

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'批量删除失败: {str(e)}'}), 500
