# backend/routes/payment.py
from flask import Blueprint, request, jsonify, session
from models import Order
from utils.payment import create_payment, verify_payment, refund_payment
from utils.exceptions import ValidationError, NotFoundError, BusinessLogicError

payment_bp = Blueprint('payment', __name__)

@payment_bp.route('/api/payments/create', methods=['POST'])
def create_payment_order():
    """创建支付订单"""
    try:
        data = request.json
        if not data:
            return jsonify({'error': '请提供支付数据'}), 400

        order_id = data.get('order_id')
        if not order_id:
            return jsonify({'error': '请提供订单ID'}), 400

        # 获取订单信息
        order = Order.get_by_id(order_id)
        if not order:
            return jsonify({'error': '订单不存在'}), 404

        # 检查权限：只有订单所有者或管理员可以创建支付
        user_id = session.get('user_id')
        is_admin = session.get('is_admin', False)

        if not is_admin and order.get('user_id') != user_id:
            return jsonify({'error': '权限不足'}), 403

        # 检查订单状态
        if order['status'] != 'pending':
            return jsonify({'error': '订单状态不允许支付'}), 400

        # 创建支付订单
        payment_provider = data.get('provider', 'mock')  # 默认使用模拟支付
        description = f"订单支付 - 订单号: {order_id}"

        payment_result = create_payment(
            order_id=order_id,
            amount=order['total_price'],
            description=description,
            provider=payment_provider
        )

        return jsonify({
            'message': '支付订单创建成功',
            'payment': payment_result
        }), 201

    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except NotFoundError as e:
        return jsonify({'error': e.message}), 404
    except Exception as e:
        return jsonify({'error': f'创建支付订单失败: {str(e)}'}), 500

@payment_bp.route('/api/payments/<payment_id>/verify', methods=['POST'])
def verify_payment_status(payment_id):
    """验证支付状态"""
    try:
        provider = request.json.get('provider', 'mock') if request.json else 'mock'

        # 验证支付状态
        result = verify_payment(payment_id, provider)

        if result.get('success'):
            # 如果支付成功，更新订单状态
            order_id = result.get('order_id')
            if order_id and result.get('status') == 'paid':
                Order.update(order_id, {'status': 'paid'})

            return jsonify({
                'message': '支付验证成功',
                'result': result
            })
        else:
            return jsonify({
                'message': '支付验证失败',
                'error': result.get('error', '未知错误')
            }), 400

    except Exception as e:
        return jsonify({'error': f'验证支付状态失败: {str(e)}'}), 500

@payment_bp.route('/api/payments/<payment_id>/refund', methods=['POST'])
def refund_payment_order(payment_id):
    """退款"""
    try:
        # 检查权限：只有管理员可以退款
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403

        data = request.json or {}
        provider = data.get('provider', 'mock')
        refund_amount = data.get('amount')  # 如果不提供金额，则全额退款

        # 执行退款
        result = refund_payment(payment_id, refund_amount, provider)

        if result.get('success'):
            return jsonify({
                'message': '退款成功',
                'result': result
            })
        else:
            return jsonify({
                'message': '退款失败',
                'error': result.get('error', '未知错误')
            }), 400

    except Exception as e:
        return jsonify({'error': f'退款失败: {str(e)}'}), 500

@payment_bp.route('/api/payments/methods', methods=['GET'])
def get_payment_methods():
    """获取可用的支付方式"""
    try:
        # 返回系统支持的支付方式
        methods = [
            {
                'id': 'mock',
                'name': '模拟支付',
                'description': '用于测试的模拟支付方式',
                'enabled': True
            },
            {
                'id': 'alipay',
                'name': '支付宝',
                'description': '支付宝在线支付',
                'enabled': False  # 需要配置后启用
            },
            {
                'id': 'wechat',
                'name': '微信支付',
                'description': '微信在线支付',
                'enabled': False  # 需要配置后启用
            }
        ]

        return jsonify({
            'methods': methods
        })

    except Exception as e:
        return jsonify({'error': f'获取支付方式失败: {str(e)}'}), 500