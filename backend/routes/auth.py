# backend/routes/auth.py
import hashlib
from flask import Blueprint, request, jsonify, session
from models import User
from utils.exceptions import ValidationError, NotFoundError, BusinessLogicError

auth_bp = Blueprint('auth', __name__)

def hash_password(password: str) -> str:
    """对密码进行哈希处理"""
    return hashlib.sha256(password.encode()).hexdigest()

@auth_bp.route('/api/auth/register', methods=['POST'])
def register():
    """用户注册"""
    try:
        data = request.json
        if not data:
            return jsonify({'error': '请提供注册数据'}), 400
        
        # 验证必需字段
        required_fields = ['username', 'email', 'password']
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            return jsonify({'error': f'缺少必需字段: {", ".join(missing_fields)}'}), 400
        
        # 对密码进行哈希处理
        data['password_hash'] = hash_password(data['password'])
        del data['password']  # 删除明文密码
        
        # 创建用户
        user = User.create(data)
        
        return jsonify({
            'message': '注册成功',
            'user': user
        }), 201
        
    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except BusinessLogicError as e:
        return jsonify({'error': e.message}), 422
    except Exception as e:
        return jsonify({'error': f'注册失败: {str(e)}'}), 500

@auth_bp.route('/api/auth/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.json
        if not data:
            return jsonify({'error': '请提供登录数据'}), 400
        
        username = data.get('username')
        password = data.get('password')
        
        if not username or not password:
            return jsonify({'error': '用户名和密码不能为空'}), 400
        
        # 对密码进行哈希处理
        password_hash = hash_password(password)
        
        # 用户认证
        user = User.authenticate(username, password_hash)
        if user:
            # 设置session
            session['user_id'] = user['id']
            session['username'] = user['username']
            session['is_admin'] = user.get('is_admin', False)
            
            return jsonify({
                'message': '登录成功',
                'user': user
            })
        else:
            return jsonify({'error': '用户名或密码错误'}), 401
            
    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'登录失败: {str(e)}'}), 500

@auth_bp.route('/api/auth/logout', methods=['POST'])
def logout():
    """用户登出"""
    try:
        session.clear()
        return jsonify({'message': '登出成功'})
    except Exception as e:
        return jsonify({'error': f'登出失败: {str(e)}'}), 500

@auth_bp.route('/api/auth/profile', methods=['GET'])
def get_profile():
    """获取当前用户信息"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'error': '未登录'}), 401
        
        user = User.get_by_id(user_id)
        if user:
            return jsonify({'user': user})
        else:
            return jsonify({'error': '用户不存在'}), 404
            
    except Exception as e:
        return jsonify({'error': f'获取用户信息失败: {str(e)}'}), 500

@auth_bp.route('/api/auth/profile', methods=['PUT'])
def update_profile():
    """更新当前用户信息"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'error': '未登录'}), 401
        
        data = request.json
        if not data:
            return jsonify({'error': '请提供更新数据'}), 400
        
        # 如果包含密码，进行哈希处理
        if 'password' in data:
            data['password_hash'] = hash_password(data['password'])
            del data['password']
        
        # 普通用户不能修改管理员权限
        if not session.get('is_admin', False):
            data.pop('is_admin', None)
        
        user = User.update(user_id, data)
        if user:
            # 更新session中的用户名
            if 'username' in data:
                session['username'] = data['username']
            
            return jsonify({
                'message': '更新成功',
                'user': user
            })
        else:
            return jsonify({'error': '用户不存在'}), 404
            
    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except BusinessLogicError as e:
        return jsonify({'error': e.message}), 422
    except Exception as e:
        return jsonify({'error': f'更新失败: {str(e)}'}), 500

@auth_bp.route('/api/auth/change-password', methods=['POST'])
def change_password():
    """修改密码"""
    try:
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'error': '未登录'}), 401
        
        data = request.json
        if not data:
            return jsonify({'error': '请提供密码数据'}), 400
        
        old_password = data.get('old_password')
        new_password = data.get('new_password')
        
        if not old_password or not new_password:
            return jsonify({'error': '旧密码和新密码不能为空'}), 400
        
        # 验证旧密码
        username = session.get('username')
        old_password_hash = hash_password(old_password)
        user = User.authenticate(username, old_password_hash)
        
        if not user:
            return jsonify({'error': '旧密码错误'}), 400
        
        # 更新密码
        new_password_hash = hash_password(new_password)
        User.update(user_id, {'password_hash': new_password_hash})
        
        return jsonify({'message': '密码修改成功'})
        
    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'密码修改失败: {str(e)}'}), 500

@auth_bp.route('/api/auth/check', methods=['GET'])
def check_auth():
    """检查登录状态"""
    try:
        user_id = session.get('user_id')
        if user_id:
            return jsonify({
                'authenticated': True,
                'user_id': user_id,
                'username': session.get('username'),
                'is_admin': session.get('is_admin', False)
            })
        else:
            return jsonify({'authenticated': False})
    except Exception as e:
        return jsonify({'error': f'检查登录状态失败: {str(e)}'}), 500

# 管理员相关路由
@auth_bp.route('/api/admin/users', methods=['GET'])
def admin_get_users():
    """管理员获取用户列表"""
    try:
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))
        
        result = User.get_paginated(page, limit)
        return jsonify(result)
        
    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'获取用户列表失败: {str(e)}'}), 500

@auth_bp.route('/api/admin/users/<int:user_id>', methods=['PUT'])
def admin_update_user(user_id):
    """管理员更新用户信息"""
    try:
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        data = request.json
        if not data:
            return jsonify({'error': '请提供更新数据'}), 400
        
        # 如果包含密码，进行哈希处理
        if 'password' in data:
            data['password_hash'] = hash_password(data['password'])
            del data['password']
        
        user = User.update(user_id, data)
        if user:
            return jsonify({
                'message': '更新成功',
                'user': user
            })
        else:
            return jsonify({'error': '用户不存在'}), 404
            
    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except BusinessLogicError as e:
        return jsonify({'error': e.message}), 422
    except Exception as e:
        return jsonify({'error': f'更新失败: {str(e)}'}), 500

@auth_bp.route('/api/admin/users/<int:user_id>', methods=['DELETE'])
def admin_delete_user(user_id):
    """管理员删除用户"""
    try:
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        # 不能删除自己
        if user_id == session.get('user_id'):
            return jsonify({'error': '不能删除自己的账户'}), 400
        
        if User.delete(user_id):
            return jsonify({'message': '用户删除成功'})
        else:
            return jsonify({'error': '用户不存在'}), 404
            
    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'删除失败: {str(e)}'}), 500
