# backend/routes/analytics.py
from flask import Blueprint, request, jsonify, session
from models import Product, Order, Analytics
from utils.exceptions import ValidationError, NotFoundError, BusinessLogicError

analytics_bp = Blueprint('analytics', __name__)

@analytics_bp.route('/api/analytics/dashboard', methods=['GET'])
def get_dashboard():
    """获取仪表板数据"""
    try:
        # 检查权限：只有管理员可以查看分析数据
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        dashboard_data = Analytics.get_dashboard_data()
        return jsonify(dashboard_data)
        
    except Exception as e:
        return jsonify({'error': f'获取仪表板数据失败: {str(e)}'}), 500

@analytics_bp.route('/api/analytics/products/statistics', methods=['GET'])
def get_product_statistics():
    """获取产品统计信息"""
    try:
        # 检查权限：只有管理员可以查看统计数据
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        stats = Product.get_statistics()
        return jsonify(stats)
        
    except Exception as e:
        return jsonify({'error': f'获取产品统计失败: {str(e)}'}), 500

@analytics_bp.route('/api/analytics/orders/statistics', methods=['GET'])
def get_order_statistics():
    """获取订单统计信息"""
    try:
        # 检查权限：只有管理员可以查看统计数据
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        stats = Order.get_statistics()
        return jsonify(stats)
        
    except Exception as e:
        return jsonify({'error': f'获取订单统计失败: {str(e)}'}), 500

@analytics_bp.route('/api/analytics/products/low-stock', methods=['GET'])
def get_low_stock_products():
    """获取低库存产品"""
    try:
        # 检查权限：只有管理员可以查看
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        threshold = int(request.args.get('threshold', 10))
        
        from utils.db import get_low_stock_products
        products = get_low_stock_products(threshold)
        
        return jsonify({
            'threshold': threshold,
            'count': len(products),
            'products': products
        })
        
    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'获取低库存产品失败: {str(e)}'}), 500

@analytics_bp.route('/api/analytics/sales/report', methods=['GET'])
def get_sales_report():
    """获取销售报告"""
    try:
        # 检查权限：只有管理员可以查看
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        report = Analytics.get_sales_report(start_date, end_date)
        return jsonify(report)
        
    except Exception as e:
        return jsonify({'error': f'获取销售报告失败: {str(e)}'}), 500

@analytics_bp.route('/api/analytics/orders/by-status', methods=['GET'])
def get_orders_by_status():
    """按状态统计订单"""
    try:
        # 检查权限：只有管理员可以查看
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        from utils.db import get_orders
        all_orders = get_orders(page=1, limit=10000)['items']  # 获取所有订单
        
        # 按状态分组统计
        status_counts = {}
        for order in all_orders:
            status = order['status']
            status_counts[status] = status_counts.get(status, 0) + 1
        
        return jsonify({
            'status_distribution': status_counts,
            'total_orders': len(all_orders)
        })
        
    except Exception as e:
        return jsonify({'error': f'获取订单状态统计失败: {str(e)}'}), 500

@analytics_bp.route('/api/analytics/products/top-selling', methods=['GET'])
def get_top_selling_products():
    """获取热销产品"""
    try:
        # 检查权限：只有管理员可以查看
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        limit = int(request.args.get('limit', 10))
        
        # 这里需要在数据库中实现复杂的统计查询
        # 目前先返回模拟数据
        from utils.db import get_orders, get_product
        all_orders = get_orders(page=1, limit=10000)['items']
        
        # 统计每个产品的销售数量
        product_sales = {}
        for order in all_orders:
            product_id = order['product_id']
            quantity = order['quantity']
            if product_id in product_sales:
                product_sales[product_id] += quantity
            else:
                product_sales[product_id] = quantity
        
        # 排序并获取前N个
        sorted_products = sorted(product_sales.items(), key=lambda x: x[1], reverse=True)[:limit]
        
        # 获取产品详细信息
        top_products = []
        for product_id, total_sold in sorted_products:
            product = get_product(product_id)
            if product:
                top_products.append({
                    'product': product,
                    'total_sold': total_sold,
                    'revenue': product['price'] * total_sold
                })
        
        return jsonify({
            'top_products': top_products,
            'limit': limit
        })
        
    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'获取热销产品失败: {str(e)}'}), 500

@analytics_bp.route('/api/analytics/revenue/monthly', methods=['GET'])
def get_monthly_revenue():
    """获取月度收入统计"""
    try:
        # 检查权限：只有管理员可以查看
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        # 这里需要在数据库中实现按月份统计的功能
        # 目前先返回模拟数据
        from utils.db import get_orders
        all_orders = get_orders(page=1, limit=10000)['items']
        
        # 按月份统计收入（简化实现）
        monthly_revenue = {}
        for order in all_orders:
            # 从created_at中提取月份（简化处理）
            created_at = order.get('created_at', '')
            month = created_at[:7] if len(created_at) >= 7 else 'unknown'  # YYYY-MM
            
            if month in monthly_revenue:
                monthly_revenue[month] += order['total_price']
            else:
                monthly_revenue[month] = order['total_price']
        
        # 转换为列表格式
        revenue_data = [
            {'month': month, 'revenue': round(revenue, 2)}
            for month, revenue in sorted(monthly_revenue.items())
        ]
        
        return jsonify({
            'monthly_revenue': revenue_data,
            'total_months': len(revenue_data)
        })
        
    except Exception as e:
        return jsonify({'error': f'获取月度收入统计失败: {str(e)}'}), 500

@analytics_bp.route('/api/analytics/inventory/value', methods=['GET'])
def get_inventory_value():
    """获取库存价值分析"""
    try:
        # 检查权限：只有管理员可以查看
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        from utils.db import get_all_products
        all_products = get_all_products()
        
        # 计算库存价值分析
        total_value = 0
        high_value_products = []
        low_stock_value = 0
        
        for product in all_products:
            product_value = product['price'] * product['stock']
            total_value += product_value
            
            # 高价值产品（单个产品库存价值超过1000）
            if product_value > 1000:
                high_value_products.append({
                    'product': product,
                    'inventory_value': round(product_value, 2)
                })
            
            # 低库存产品的价值
            if product['stock'] <= 10:
                low_stock_value += product_value
        
        # 按价值排序
        high_value_products.sort(key=lambda x: x['inventory_value'], reverse=True)
        
        return jsonify({
            'total_inventory_value': round(total_value, 2),
            'high_value_products': high_value_products[:10],  # 前10个高价值产品
            'low_stock_value': round(low_stock_value, 2),
            'average_product_value': round(total_value / len(all_products), 2) if all_products else 0
        })
        
    except Exception as e:
        return jsonify({'error': f'获取库存价值分析失败: {str(e)}'}), 500
