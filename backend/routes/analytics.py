# backend/routes/analytics.py
from flask import Blueprint, request, jsonify, session
from models import Product, Order, Analytics
from utils.exceptions import ValidationError, NotFoundError, BusinessLogicError

analytics_bp = Blueprint('analytics', __name__)

@analytics_bp.route('/api/analytics/dashboard', methods=['GET'])
def get_dashboard():
    """获取仪表板数据"""
    try:
        # 检查权限：只有管理员可以查看分析数据
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        dashboard_data = Analytics.get_dashboard_data()
        return jsonify(dashboard_data)
        
    except Exception as e:
        return jsonify({'error': f'获取仪表板数据失败: {str(e)}'}), 500

@analytics_bp.route('/api/analytics/products/statistics', methods=['GET'])
def get_product_statistics():
    """获取产品统计信息"""
    try:
        # 检查权限：只有管理员可以查看统计数据
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        stats = Product.get_statistics()
        return jsonify(stats)
        
    except Exception as e:
        return jsonify({'error': f'获取产品统计失败: {str(e)}'}), 500

@analytics_bp.route('/api/analytics/orders/statistics', methods=['GET'])
def get_order_statistics():
    """获取订单统计信息"""
    try:
        # 检查权限：只有管理员可以查看统计数据
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        stats = Order.get_statistics()
        return jsonify(stats)
        
    except Exception as e:
        return jsonify({'error': f'获取订单统计失败: {str(e)}'}), 500

@analytics_bp.route('/api/analytics/products/low-stock', methods=['GET'])
def get_low_stock_products():
    """获取低库存产品"""
    try:
        # 检查权限：只有管理员可以查看
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        threshold = int(request.args.get('threshold', 10))
        products = Product.get_low_stock(threshold)
        
        return jsonify({
            'threshold': threshold,
            'count': len(products),
            'products': products
        })
    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'获取低库存产品失败: {str(e)}'}), 500

@analytics_bp.route('/api/analytics/sales/report', methods=['GET'])
def get_sales_report():
    """获取销售报告"""
    try:
        # 检查权限：只有管理员可以查看
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        report = Analytics.get_sales_report(start_date, end_date)
        return jsonify(report)
    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'获取销售报告失败: {str(e)}'}), 500

@analytics_bp.route('/api/analytics/orders/by-status', methods=['GET'])
def get_orders_by_status():
    """按状态统计订单"""
    try:
        # 检查权限：只有管理员可以查看
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        status_distribution = Analytics.get_order_distribution()
        
        return jsonify({
            'status_distribution': status_distribution,
            'total_orders': sum(status_distribution.values())
        })
        
    except Exception as e:
        return jsonify({'error': f'获取订单状态统计失败: {str(e)}'}), 500

@analytics_bp.route('/api/analytics/products/top-selling', methods=['GET'])
def get_top_selling_products():
    """获取热销产品"""
    try:
        # 检查权限：只有管理员可以查看
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        limit = int(request.args.get('limit', 10))
        top_products = Analytics.get_top_selling(limit)
        
        return jsonify({
            'top_products': top_products,
            'limit': limit
        })
    except ValidationError as e:
        return jsonify({'error': e.message}), 400
    except Exception as e:
        return jsonify({'error': f'获取热销产品失败: {str(e)}'}), 500

@analytics_bp.route('/api/analytics/revenue/monthly', methods=['GET'])
def get_monthly_revenue():
    """获取月度收入统计"""
    try:
        # 检查权限：只有管理员可以查看
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        revenue_data = Analytics.get_revenue_by_month()
        
        return jsonify({
            'monthly_revenue': revenue_data,
            'total_months': len(revenue_data)
        })
    except Exception as e:
        return jsonify({'error': f'获取月度收入统计失败: {str(e)}'}), 500

@analytics_bp.route('/api/analytics/inventory/value', methods=['GET'])
def get_inventory_value():
    """获取库存价值分析"""
    try:
        # 检查权限：只有管理员可以查看
        if not session.get('is_admin', False):
            return jsonify({'error': '权限不足'}), 403
        
        inventory_stats = Analytics.get_inventory_value()
        
        return jsonify({
            'total_inventory_value': round(inventory_stats.get('total_value', 0), 2),
            'low_stock_value': round(inventory_stats.get('low_stock_value', 0), 2),
            'average_product_value': round(inventory_stats.get('average_product_value', 0), 2)
        })
        
    except Exception as e:
        return jsonify({'error': f'获取库存价值分析失败: {str(e)}'}), 500
