# backend/models.py
import sqlite3
from utils.db import (
    get_product,
    get_products,
    get_all_products,
    create_product,
    update_product,
    delete_product,
    search_products,
    get_low_stock_products,
    batch_delete_products,
    create_order,
    get_order,
    get_orders,
    get_orders_by_user,
    update_order,
    delete_order,
    get_dashboard_summary,
    get_product_statistics,
    get_order_statistics,
    get_top_selling_products,
    get_monthly_revenue,
    get_order_status_distribution,
    get_inventory_value_statistics,
    get_sales_report_data,

)
from utils.validators import (
    validate_product_data, validate_order_data, validate_pagination_params, 
    validate_search_keyword, validate_id_list, validate_date_range
)
from utils.exceptions import (
    ValidationError,
    DatabaseError,
    NotFoundError,
    BusinessLogicError
)
from typing import Optional, Dict, Any, List
from datetime import datetime
import logging

# 配置日志
logger = logging.getLogger(__name__)

class BaseModel:
    """基础模型类，提供通用功能"""

    @classmethod
    def _log_operation(cls, operation: str, entity_id: Optional[int] = None, **kwargs):
        """记录操作日志"""
        entity_name = cls.__name__.lower()
        if entity_id:
            logger.info(f"{operation} {entity_name} {entity_id}: {kwargs}")
        else:
            logger.info(f"{operation} {entity_name}: {kwargs}")

class Product(BaseModel):
    """产品模型类，封装数据库操作"""
    
    @classmethod
    def get_all(cls) -> List[Dict[str, Any]]:
        """获取所有产品(不推荐，数据量大时性能差)"""
        # 注意: 当产品数量非常多时，一次性加载所有产品可能会消耗大量内存。
        return get_all_products()
    
    @classmethod
    def get_by_id(cls, product_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取单个产品"""
        return get_product(product_id)
    
    @classmethod
    def get_paginated(cls, page: int = 1, limit: int = 10) -> Dict[str, Any]:
        """获取分页产品列表"""
        try:
            return get_products(page=page, limit=limit)
        except sqlite3.Error as e:
            raise DatabaseError(f"获取分页产品失败: 数据库错误 - {e}")

    @classmethod
    def search(cls, keyword: str, page: int = 1, limit: int = 10) -> Dict[str, Any]:
        """根据关键词搜索产品"""
        # 1. 验证输入
        cleaned_keyword = validate_search_keyword(keyword)
        page, limit = validate_pagination_params(page, limit)
        
        # 2. 数据库操作
        try:
            return search_products(keyword=cleaned_keyword, page=page, limit=limit)
        except sqlite3.Error as e:
            raise DatabaseError(f"搜索产品失败: 数据库错误 - {e}")

    @classmethod
    def get_low_stock(cls, threshold: int = 10) -> List[Dict[str, Any]]:
        """获取低于指定库存阈值的产品"""
        if not isinstance(threshold, int) or threshold < 0:
            raise ValidationError("库存阈值必须是一个非负整数")
        try:
            return get_low_stock_products(threshold)
        except sqlite3.Error as e:
            raise DatabaseError(f"获取低库存产品失败: 数据库错误 - {e}")

    @classmethod
    def batch_delete(cls, product_ids: List[int]) -> Dict[str, int]:
        """批量删除产品"""
        # 1. 验证ID列表
        validated_ids = validate_id_list(product_ids)
        
        # 2. 数据库操作
        try:
            return batch_delete_products(validated_ids)
        except sqlite3.Error as e:
            raise DatabaseError(f"批量删除产品失败: 数据库错误 - {e}")
    
    @classmethod
    def create(cls, data: Dict[str, Any]) -> Dict[str, Any]:
        """创建新产品"""
        # 1. 数据验证
        validate_product_data(data)
        
        # 2. 数据库操作
        try:
            return create_product(data)
        except sqlite3.Error as e:  # 处理数据库操作错误
            # 在实际应用中，这里可能需要记录日志
            raise DatabaseError(f"创建产品失败: 数据库错误 - {e}")
    
    @classmethod
    def update(cls, product_id: int, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """更新产品信息"""
        # 1. 数据验证 (允许部分更新)
        validate_product_data(data, partial=True)
        
        # 2. 数据库操作
        try:
            updated_product = update_product(product_id, data)
            if updated_product is None:
                raise NotFoundError(f"产品 ID {product_id} 未找到")
            return updated_product
        except sqlite3.Error as e:  # 处理数据库操作错误
            raise DatabaseError(f"更新产品失败: 数据库错误 - {e}")
    
    @classmethod
    def delete(cls, product_id: int) -> bool:
        """删除产品"""
        try:
            success = delete_product(product_id)
            if not success:
                raise NotFoundError(f"产品 ID {product_id} 未找到")
            cls._log_operation("删除产品", product_id)
            return True
        except sqlite3.Error as e:  # 处理数据库操作错误
            raise DatabaseError(f"删除产品失败: 数据库错误 - {e}")

    @classmethod
    def get_statistics(cls) -> Dict[str, Any]:
        """获取产品统计信息"""
        try:
            return get_product_statistics()
        except sqlite3.Error as e:
            raise DatabaseError(f"获取产品统计失败: {e}")


class Order(BaseModel):
    """订单模型类，封装订单相关操作"""

    @classmethod
    def create(cls, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建新订单
        1. 验证数据
        2. 检查产品是否存在和库存
        3. 调用数据库事务函数创建订单并扣减库存
        """
        # 1. 验证订单数据
        validate_order_data(data) # 验证 product_id, quantity, user_id 等
        product_id = data['product_id']
        quantity = data['quantity']

        # 2. 业务逻辑检查
        product = Product.get_by_id(product_id)
        if not product:
            raise NotFoundError(f"产品 ID {product_id} 不存在")
        
        if product['stock'] < quantity:
            raise BusinessLogicError(f"产品 '{product['name']}' 库存不足 (当前库存: {product['stock']})")

        # 3. 数据库操作 (事务性)
        try:
            order_id = create_order(data, product['price'])
            
            if order_id is None:
                # 这种情况通常发生在并发场景下，即在上述检查后，库存被其他请求消耗
                raise BusinessLogicError(f"创建订单失败：产品 '{product['name']}' 库存可能刚刚被抢光")

            new_order = cls.get_by_id(order_id)
            return new_order
        except sqlite3.Error as e:
            raise DatabaseError(f"创建订单失败: 数据库错误 - {e}")

    @classmethod
    def get_by_id(cls, order_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取单个订单"""
        return get_order(order_id)

    @classmethod
    def get_paginated(cls, page: int = 1, limit: int = 10, status: Optional[str] = None) -> Dict[str, Any]:
        """获取分页订单列表"""
        page, limit = validate_pagination_params(page, limit)
        if status:
            validate_order_data({'status': status}, partial=True)
        return get_orders(page=page, limit=limit, status=status)

    @classmethod
    def get_by_user(cls, user_id: int, page: int = 1, limit: int = 10) -> Dict[str, Any]:
        """根据用户ID获取其订单列表"""
        page, limit = validate_pagination_params(page, limit)
        return get_orders_by_user(user_id, page, limit)

    @classmethod
    def update(cls, order_id: int, data: Dict[str, Any]) -> Dict[str, Any]:
        """更新订单信息（目前仅支持状态）"""
        # 验证传入的数据，目前只允许更新status
        if 'status' in data:
            validate_order_data({'status': data['status']}, partial=True)
        else:
            raise ValidationError("没有提供可更新的字段（目前只支持 'status'）")
        
        try:
            updated_order = update_order(order_id, data)
            if updated_order is None:
                raise NotFoundError(f"订单 ID {order_id} 未找到")
            return updated_order
        except sqlite3.Error as e:
            raise DatabaseError(f"更新订单失败: {e}")

    @classmethod
    def delete(cls, order_id: int) -> None:
        """删除订单"""
        try:
            if not delete_order(order_id):
                raise NotFoundError(f"订单 ID {order_id} 未找到")
        except sqlite3.Error as e:
            raise DatabaseError(f"删除订单失败: {e}")

    @classmethod
    def get_statistics(cls) -> Dict[str, Any]:
        """获取订单统计信息"""
        try:
            return get_order_statistics()
        except sqlite3.Error as e:
            raise DatabaseError(f"获取订单统计失败: {e}")

    @classmethod
    def get_detailed_by_id(cls, order_id: int) -> Optional[Dict[str, Any]]:
        """获取订单详细信息，包括产品和用户信息"""
        try:
            return get_order(order_id)  # 使用现有的get_order函数
        except sqlite3.Error as e:
            raise DatabaseError(f"获取订单详情失败: {e}")

    @classmethod
    def get_by_user_with_filters(cls, user_id: int, page: int, limit: int, filters: Dict[str, Any]) -> Dict[str, Any]:
        """根据用户ID和过滤条件获取订单列表"""
        try:
            return get_orders_by_user(user_id, page, limit)  # 简化版本
        except sqlite3.Error as e:
            raise DatabaseError(f"获取用户订单失败: {e}")

    @classmethod
    def get_paginated_with_filters(cls, page: int, limit: int, filters: Dict[str, Any]) -> Dict[str, Any]:
        """根据过滤条件获取分页订单列表"""
        try:
            status = filters.get('status')
            return get_orders(page, limit, status)  # 使用现有函数
        except sqlite3.Error as e:
            raise DatabaseError(f"获取订单列表失败: {e}")

    @classmethod
    def cancel(cls, order_id: int, reason: str = '') -> Dict[str, Any]:
        """取消订单并恢复库存"""
        try:
            # 简化版本：只更新状态
            cancelled_order = update_order(order_id, {'status': 'cancelled'})
            if not cancelled_order:
                raise NotFoundError(f"订单 ID {order_id} 未找到或无法取消")
            return cancelled_order
        except sqlite3.Error as e:
            raise DatabaseError(f"取消订单失败: {e}")

    @classmethod
    def update_notes(cls, order_id: int, notes: str) -> Dict[str, Any]:
        """更新订单备注"""
        try:
            # 简化版本：使用现有的update_order函数
            updated_order = update_order(order_id, {'notes': notes})
            if not updated_order:
                raise NotFoundError(f"订单 ID {order_id} 未找到")
            return updated_order
        except sqlite3.Error as e:
            raise DatabaseError(f"更新订单备注失败: {e}")

    @classmethod
    def get_by_status(cls, status: str, page: int = 1, limit: int = 10) -> Dict[str, Any]:
        """根据状态获取订单列表"""
        try:
            validate_order_data({'status': status}, partial=True)
            return get_orders(page, limit, status)  # 使用现有函数
        except sqlite3.Error as e:
            raise DatabaseError(f"根据状态获取订单失败: {e}")


class Analytics:
    """分析模型类，封装所有统计和报告功能"""

    @classmethod
    def get_dashboard_data(cls) -> Dict[str, Any]:
        """获取仪表板核心数据"""
        try:
            return get_dashboard_summary()
        except sqlite3.Error as e:
            raise DatabaseError(f"获取仪表板数据失败: {e}")

    @classmethod
    def get_top_selling(cls, limit: int = 10) -> List[Dict[str, Any]]:
        """获取热销产品报告"""
        if not isinstance(limit, int) or not 1 <= limit <= 50:
            raise ValidationError("limit 参数必须是1到50之间的整数")
        try:
            return get_top_selling_products(limit)
        except sqlite3.Error as e:
            raise DatabaseError(f"获取热销产品失败: {e}")

    @classmethod
    def get_revenue_by_month(cls) -> List[Dict[str, Any]]:
        """获取月度收入报告"""
        try:
            return get_monthly_revenue()
        except sqlite3.Error as e:
            raise DatabaseError(f"获取月度收入报告失败: {e}")

    @classmethod
    def get_order_distribution(cls) -> Dict[str, int]:
        """获取订单状态分布"""
        try:
            return get_order_status_distribution()
        except sqlite3.Error as e:
            raise DatabaseError(f"获取订单状态分布失败: {e}")

    @classmethod
    def get_inventory_value(cls) -> Dict[str, Any]:
        """获取库存价值分析"""
        try:
            stats = get_inventory_value_statistics()
            total_value = stats.get('total_value') or 0
            product_count = stats.get('product_count') or 0
            stats['average_product_value'] = round(total_value / product_count, 2) if product_count > 0 else 0
            return stats
        except sqlite3.Error as e:
            raise DatabaseError(f"获取库存价值分析失败: {e}")

    @classmethod
    def get_sales_report(cls, start_date: Optional[str], end_date: Optional[str]) -> Dict[str, Any]:
        """获取销售报告"""
        # 1. 验证日期
        validate_date_range(start_date, end_date)

        # 2. 获取数据
        try:
            report = get_sales_report_data(start_date, end_date)
            report['start_date'] = start_date
            report['end_date'] = end_date
            return report
        except sqlite3.Error as e:
            raise DatabaseError(f"获取销售报告失败: {e}")


class User(BaseModel):
    """用户模型类，封装用户相关操作"""

    @classmethod
    def create(cls, username: str, email: str, password_hash: str, is_admin: bool = False) -> Dict[str, Any]:
        """创建新用户"""
        # 简单的用户数据验证
        if not username or not email or not password_hash:
            raise ValidationError("用户名、邮箱和密码不能为空")

        # 这里需要在db.py中实现用户相关的数据库操作
        # 目前先返回模拟数据
        user_data = {
            'id': 1,  # 模拟ID
            'username': username,
            'email': email,
            'is_admin': is_admin,
            'created_at': datetime.now().isoformat()
        }

        cls._log_operation("创建用户", user_data['id'], username=username)
        return user_data

    @classmethod
    def authenticate(cls, username: str, password_hash: str) -> Optional[Dict[str, Any]]:
        """用户认证"""
        if not username or not password_hash:
            raise ValidationError("用户名和密码不能为空")

        # 这里需要在db.py中实现用户认证逻辑
        # 目前先返回模拟数据
        if username == "admin" and password_hash:  # 简单的模拟认证
            user_data = {
                'id': 1,
                'username': username,
                'email': '<EMAIL>',
                'is_admin': True
            }
            cls._log_operation("用户认证成功", user_data['id'], username=username)
            return user_data

        cls._log_operation("用户认证失败", username=username)
        return None

    @classmethod
    def get_by_id(cls, user_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取用户"""
        if not isinstance(user_id, int) or user_id <= 0:
            raise ValidationError("用户ID必须是正整数")

        # 这里需要在db.py中实现
        # 目前先返回模拟数据
        if user_id == 1:
            return {
                'id': 1,
                'username': 'admin',
                'email': '<EMAIL>',
                'is_admin': True
            }
        return None
