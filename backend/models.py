# backend/models.py
import sqlite3
from utils.db import (
    get_product,
    get_products,
    get_all_products,
    create_product,
    update_product,
    delete_product,
    search_products,
    create_order,
    get_order,
    get_orders,
    update_order_status
)
from utils.validators import validate_product_data, validate_order_data, validate_pagination_params, validate_search_keyword
from utils.exceptions import (
    ValidationError,
    DatabaseError,
    NotFoundError,
    BusinessLogicError
)
from typing import Optional, Dict, Any, List

class Product:
    """产品模型类，封装数据库操作"""
    
    @classmethod
    def get_all(cls) -> List[Dict[str, Any]]:
        """获取所有产品(不推荐，数据量大时性能差)"""
        # 注意: 当产品数量非常多时，一次性加载所有产品可能会消耗大量内存。
        return get_all_products()
    
    @classmethod
    def get_by_id(cls, product_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取单个产品"""
        return get_product(product_id)
    
    @classmethod
    def get_paginated(cls, page: int = 1, limit: int = 10, search: Optional[str] = None) -> Dict[str, Any]:
        """获取分页产品列表"""
        return get_products(page=page, limit=limit)

    @classmethod
    def search(cls, keyword: str, page: int = 1, limit: int = 10) -> Dict[str, Any]:
        """根据关键词搜索产品"""
        # 1. 验证输入
        cleaned_keyword = validate_search_keyword(keyword)
        page, limit = validate_pagination_params(page, limit)
        
        # 2. 数据库操作
        try:
            return search_products(keyword=cleaned_keyword, page=page, limit=limit)
        except sqlite3.Error as e:
            raise DatabaseError(f"搜索产品失败: 数据库错误 - {e}")
    
    @classmethod
    def create(cls, data: Dict[str, Any]) -> Dict[str, Any]:
        """创建新产品"""
        # 1. 数据验证
        validate_product_data(data)
        
        # 2. 数据库操作
        try:
            return create_product(data)
        except sqlite3.Error as e:  # 处理数据库操作错误
            # 在实际应用中，这里可能需要记录日志
            raise DatabaseError(f"创建产品失败: 数据库错误 - {e}")
    
    @classmethod
    def update(cls, product_id: int, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """更新产品信息"""
        # 1. 数据验证 (允许部分更新)
        validate_product_data(data, partial=True)
        
        # 2. 数据库操作
        try:
            updated_product = update_product(product_id, data)
            if updated_product is None:
                raise NotFoundError(f"产品 ID {product_id} 未找到")
            return updated_product
        except sqlite3.Error as e:  # 处理数据库操作错误
            raise DatabaseError(f"更新产品失败: 数据库错误 - {e}")
    
    @classmethod
    def delete(cls, product_id: int) -> None:
        """删除产品"""
        try:
            success = delete_product(product_id)
            if not success:
                raise NotFoundError(f"产品 ID {product_id} 未找到")
        except sqlite3.Error as e:  # 处理数据库操作错误
            raise DatabaseError(f"删除产品失败: 数据库错误 - {e}")

class Order:
    """订单模型类，封装订单相关操作"""

    @classmethod
    def create(cls, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建新订单
        1. 验证数据
        2. 检查产品是否存在和库存
        3. 调用数据库事务函数创建订单并扣减库存
        """
        # 1. 验证订单数据
        validate_order_data(data)
        product_id = data['product_id']
        quantity = data['quantity']

        # 2. 业务逻辑检查
        product = Product.get_by_id(product_id)
        if not product:
            raise NotFoundError(f"产品 ID {product_id} 不存在")
        
        if product['stock'] < quantity:
            raise BusinessLogicError(f"产品 '{product['name']}' 库存不足 (当前库存: {product['stock']})")

        # 3. 数据库操作 (事务性)
        try:
            order_id = create_order(data, product['price'])
            
            if order_id is None:
                # 这种情况通常发生在并发场景下，即在上述检查后，库存被其他请求消耗
                raise BusinessLogicError(f"创建订单失败：产品 '{product['name']}' 库存可能刚刚被抢光")

            new_order = cls.get_by_id(order_id)
            return new_order
        except sqlite3.Error as e:
            raise DatabaseError(f"创建订单失败: 数据库错误 - {e}")

    @classmethod
    def get_by_id(cls, order_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取单个订单"""
        return get_order(order_id)

    @classmethod
    def get_paginated(cls, page: int = 1, limit: int = 10) -> Dict[str, Any]:
        """获取分页订单列表"""
        page, limit = validate_pagination_params(page, limit)
        return get_orders(page=page, limit=limit)

    @classmethod
    def update_status(cls, order_id: int, status: str) -> Dict[str, Any]:
        """更新订单状态"""
        validate_order_data({'status': status}, partial=True)
        try:
            updated_order = update_order_status(order_id, status)
            if updated_order is None:
                raise NotFoundError(f"订单 ID {order_id} 未找到")
            return updated_order
        except sqlite3.Error as e:
            raise DatabaseError(f"更新订单状态失败: {e}")
