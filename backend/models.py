# backend/models.py
from utils.db import (
    get_product,
    get_products,
    create_product,
    update_product,
    delete_product
)
from typing import Optional, Dict, Any

class Product:
    """产品模型类，封装数据库操作"""
    
    @classmethod
    def get_all(cls) -> list:
        """获取所有产品(不推荐，数据量大时性能差)"""
        result = get_products(page=1, limit=10000)  # 设置大limit获取全部
        return result['items']
    
    @classmethod
    def get_by_id(cls, product_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取单个产品"""
        return get_product(product_id)
    
    @classmethod
    def get_paginated(cls, page: int = 1, limit: int = 10) -> Dict[str, Any]:
        """获取分页产品列表"""
        return get_products(page=page, limit=limit)
    
    @classmethod
    def create(cls, data: Dict[str, Any]) -> Dict[str, Any]:
        """创建新产品"""
        try:
            return create_product(data)
        except ValueError as e:
            raise ValueError(f"创建产品失败: {str(e)}")
        except Exception as e:
            raise Exception(f"数据库错误: {str(e)}")
    
    @classmethod
    def update(cls, product_id: int, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """更新产品信息"""
        try:
            return update_product(product_id, data)
        except Exception as e:
            raise Exception(f"更新产品失败: {str(e)}")
    
    @classmethod
    def delete(cls, product_id: int) -> bool:
        """删除产品"""
        try:
            return delete_product(product_id)
        except Exception as e:
            raise Exception(f"删除产品失败: {str(e)}")
