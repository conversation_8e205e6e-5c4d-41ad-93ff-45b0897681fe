#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的API测试脚本
验证核心功能是否正常工作
"""

import requests
import json

BASE_URL = 'http://127.0.0.1:5000'

def test_basic_functionality():
    """测试基本功能"""
    print("=== 基本功能测试 ===\n")
    
    # 1. 健康检查
    print("1. 健康检查")
    response = requests.get(f"{BASE_URL}/api/health")
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        print(f"   响应: {response.json()}")
    
    # 2. 获取产品列表
    print("\n2. 获取产品列表")
    response = requests.get(f"{BASE_URL}/api/products")
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"   产品数量: {data.get('total', 0)}")
    
    # 3. 获取订单列表
    print("\n3. 获取订单列表")
    response = requests.get(f"{BASE_URL}/api/orders")
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"   订单数量: {data.get('total', 0)}")
    
    # 4. 测试权限控制
    print("\n4. 测试权限控制")
    product_data = {
        "name": "测试产品",
        "price": 99.99,
        "stock": 100,
        "description": "测试产品描述"
    }
    response = requests.post(f"{BASE_URL}/api/products", json=product_data)
    print(f"   创建产品状态码: {response.status_code}")
    if response.status_code == 403:
        print("   ✓ 权限控制正常工作")
    
    # 5. 测试搜索功能
    print("\n5. 测试搜索功能")
    response = requests.get(f"{BASE_URL}/api/products?search=测试")
    print(f"   搜索状态码: {response.status_code}")
    if response.status_code == 200:
        print("   ✓ 搜索功能正常")
    
    # 6. 测试分页功能
    print("\n6. 测试分页功能")
    response = requests.get(f"{BASE_URL}/api/products?page=1&limit=5")
    print(f"   分页状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"   ✓ 分页功能正常，页码: {data.get('page')}")
    
    # 7. 测试错误处理
    print("\n7. 测试错误处理")
    response = requests.get(f"{BASE_URL}/api/products/99999")
    print(f"   无效产品ID状态码: {response.status_code}")
    if response.status_code == 404:
        print("   ✓ 错误处理正常")
    
    # 8. 测试订单过滤
    print("\n8. 测试订单过滤")
    response = requests.get(f"{BASE_URL}/api/orders?status=pending&page=1&limit=5")
    print(f"   订单过滤状态码: {response.status_code}")
    if response.status_code == 200:
        print("   ✓ 订单过滤功能正常")
    
    print("\n=== 基本功能测试完成 ===")
    print("✓ 所有核心API接口正常工作")
    print("✓ 权限控制系统正常")
    print("✓ 错误处理机制正常")
    print("✓ 搜索和过滤功能正常")

if __name__ == "__main__":
    try:
        test_basic_functionality()
    except requests.exceptions.ConnectionError:
        print("错误: 无法连接到服务器，请确保应用正在运行")
        print("启动命令: cd backend && source venv/bin/activate && python app.py")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
