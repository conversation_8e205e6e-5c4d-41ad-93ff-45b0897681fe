# backend/app.py
from flask import Flask, session
from flask_cors import CORS

app = Flask(__name__)
app.secret_key = 'your_secret_key_here'  # 设置 session 密钥
CORS(app)

# 导入路由
from routes.product import product_bp
from routes.order import order_bp
from routes.auth import auth_bp
from routes.payment import payment_bp

app.register_blueprint(product_bp)
app.register_blueprint(order_bp)
app.register_blueprint(auth_bp)
app.register_blueprint(payment_bp)

if __name__ == '__main__':
    app.run(debug=True)
