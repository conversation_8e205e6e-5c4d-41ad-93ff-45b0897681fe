# backend/app.py
import os
import logging
from flask import Flask, session, jsonify
from flask_cors import CORS
from config import get_config

# 创建Flask应用
def create_app(config_name=None):
    """应用工厂函数"""
    app = Flask(__name__)

    # 加载配置
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'default')

    config_class = get_config()
    app.config.from_object(config_class)
    config_class.init_app(app)

    # 配置CORS - 更详细的配置
    CORS(app,
         origins=app.config['CORS_ORIGINS'],
         supports_credentials=True,
         allow_headers=['Content-Type', 'Authorization', 'Accept'],
         methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])

    # 配置日志
    if not app.debug and not app.testing:
        logging.basicConfig(
            level=getattr(logging, app.config['LOG_LEVEL']),
            format='%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        )

    return app

# 创建应用实例
app = create_app()

# 全局错误处理
@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'API endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500

@app.errorhandler(400)
def bad_request(error):
    return jsonify({'error': 'Bad request'}), 400

# 健康检查端点
@app.route('/api/health', methods=['GET'])
def health_check():
    return jsonify({
        'status': 'healthy',
        'version': app.config.get('API_VERSION', 'v1'),
        'environment': os.environ.get('FLASK_ENV', 'development')
    })

# 导入路由
from routes.product import product_bp
from routes.order import order_bp
from routes.auth import auth_bp
from routes.payment import payment_bp
from routes.analytics import analytics_bp
from routes.batch import batch_bp
from routes.delivery import delivery_bp

app.register_blueprint(product_bp)
app.register_blueprint(order_bp)
app.register_blueprint(auth_bp)
app.register_blueprint(payment_bp)
app.register_blueprint(analytics_bp)
app.register_blueprint(batch_bp)
app.register_blueprint(delivery_bp)

if __name__ == '__main__':
    app.run(debug=True)
