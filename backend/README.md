# 发卡商城后端系统

## 项目概述

这是一个基于Flask的发卡商城后端系统，提供了完整的商品管理、订单处理、用户认证和支付功能。

## 主要功能

### 1. 产品管理
- 产品的增删改查
- 分页查询
- 搜索功能
- 库存管理
- 低库存预警
- 批量操作

### 2. 订单管理
- 订单创建和管理
- 订单状态跟踪
- 用户订单查询
- 管理员订单管理

### 3. 用户认证
- 用户注册和登录
- 密码加密存储
- 会话管理
- 权限控制（普通用户/管理员）
- 用户信息管理

### 4. 支付系统
- 多支付方式支持
- 支付订单创建
- 支付状态验证
- 退款功能
- 支付回调处理

## 技术架构

### 后端框架
- **Flask**: 轻量级Web框架
- **SQLite**: 数据库存储
- **Flask-CORS**: 跨域支持

### 代码结构
```
backend/
├── app.py                 # 主应用入口
├── config.py             # 配置文件
├── models.py             # 数据模型层
├── routes/               # 路由层
│   ├── product.py        # 产品相关路由
│   ├── order.py          # 订单相关路由
│   ├── auth.py           # 认证相关路由
│   └── payment.py        # 支付相关路由
└── utils/                # 工具模块
    ├── db.py             # 数据库操作
    ├── validators.py     # 数据验证
    ├── exceptions.py     # 自定义异常
    └── payment.py        # 支付工具
```

## 数据库设计

### 产品表 (products)
- id: 主键
- name: 产品名称
- price: 价格
- stock: 库存
- description: 描述
- created_at: 创建时间
- updated_at: 更新时间

### 用户表 (users)
- id: 主键
- username: 用户名
- email: 邮箱
- password_hash: 密码哈希
- is_admin: 是否管理员
- created_at: 创建时间
- updated_at: 更新时间

### 订单表 (orders)
- id: 主键
- user_id: 用户ID
- product_id: 产品ID
- quantity: 数量
- unit_price: 单价
- total_price: 总价
- status: 状态
- customer_info: 客户信息
- created_at: 创建时间
- updated_at: 更新时间

### 购物车表 (cart_items)
- id: 主键
- user_id: 用户ID
- product_id: 产品ID
- quantity: 数量
- created_at: 创建时间

## API接口

### 产品接口
- `GET /api/products` - 获取产品列表
- `GET /api/products/{id}` - 获取单个产品
- `POST /api/products` - 创建产品（管理员）
- `PUT /api/products/{id}` - 更新产品（管理员）
- `DELETE /api/products/{id}` - 删除产品（管理员）
- `GET /api/products/search` - 搜索产品
- `GET /api/products/low-stock` - 获取低库存产品（管理员）
- `DELETE /api/products/batch` - 批量删除产品（管理员）

### 订单接口
- `GET /api/orders` - 获取订单列表
- `GET /api/orders/{id}` - 获取单个订单
- `POST /api/orders` - 创建订单
- `PUT /api/orders/{id}` - 更新订单（管理员）
- `DELETE /api/orders/{id}` - 删除订单（管理员）
- `PUT /api/orders/{id}/status` - 更新订单状态（管理员）
- `GET /api/orders/my` - 获取当前用户订单
- `GET /api/orders/status/{status}` - 按状态获取订单（管理员）

### 认证接口
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/profile` - 获取用户信息
- `PUT /api/auth/profile` - 更新用户信息
- `POST /api/auth/change-password` - 修改密码
- `GET /api/auth/check` - 检查登录状态
- `GET /api/admin/users` - 获取用户列表（管理员）
- `PUT /api/admin/users/{id}` - 更新用户信息（管理员）
- `DELETE /api/admin/users/{id}` - 删除用户（管理员）

### 支付接口
- `POST /api/payments/create` - 创建支付订单
- `POST /api/payments/{id}/verify` - 验证支付状态
- `POST /api/payments/{id}/refund` - 退款（管理员）
- `GET /api/payments/methods` - 获取支付方式

## 主要改进

### 1. 代码结构优化
- 采用MVC架构模式
- 分离业务逻辑和数据访问
- 模块化设计，便于维护

### 2. 数据验证增强
- 统一的数据验证机制
- 详细的错误信息
- 类型检查和范围验证

### 3. 错误处理完善
- 自定义异常类
- 统一的错误响应格式
- 详细的日志记录

### 4. 安全性提升
- 密码哈希存储
- 会话管理
- 权限控制
- 输入验证和过滤

### 5. 功能扩展
- 用户认证系统
- 支付集成框架
- 搜索功能
- 批量操作
- 库存管理

### 6. 性能优化
- 分页查询
- 数据库索引
- 连接池管理
- 缓存机制预留

## 使用说明

### 安装依赖
```bash
pip install flask flask-cors
```

### 运行应用
```bash
cd backend
python app.py
```

### 初始化数据
应用首次运行时会自动创建数据库表结构。

### 创建管理员账户
可以通过注册接口创建用户，然后手动在数据库中将 `is_admin` 字段设置为 `True`。

## 配置说明

### 数据库配置
- 默认使用SQLite数据库
- 数据库文件：`faka_mall.db`
- 可在 `utils/db.py` 中修改数据库路径

### 支付配置
- 默认使用模拟支付
- 可在 `utils/payment.py` 中添加真实支付服务商
- 支持支付宝、微信支付等扩展

### 会话配置
- 在 `app.py` 中设置 `secret_key`
- 生产环境请使用强密码

### 新增功能接口

#### 分析统计接口
- `GET /api/analytics/dashboard` - 获取仪表板数据（管理员）
- `GET /api/analytics/products/statistics` - 获取产品统计（管理员）
- `GET /api/analytics/orders/statistics` - 获取订单统计（管理员）
- `GET /api/analytics/products/low-stock` - 获取低库存产品（管理员）
- `GET /api/analytics/sales/report` - 获取销售报告（管理员）
- `GET /api/analytics/orders/by-status` - 按状态统计订单（管理员）
- `GET /api/analytics/products/top-selling` - 获取热销产品（管理员）
- `GET /api/analytics/revenue/monthly` - 获取月度收入统计（管理员）
- `GET /api/analytics/inventory/value` - 获取库存价值分析（管理员）

#### 批量操作接口
- `POST /api/batch/products/delete` - 批量删除产品（管理员）
- `POST /api/batch/products/update` - 批量更新产品（管理员）
- `POST /api/batch/orders/update-status` - 批量更新订单状态（管理员）
- `GET /api/batch/products/export` - 导出产品数据（管理员）
- `GET /api/batch/orders/export` - 导出订单数据（管理员）
- `POST /api/batch/products/import` - 批量导入产品（管理员）

#### 系统接口
- `GET /api/health` - 健康检查

## 配置管理

### 环境变量配置
```bash
# Flask配置
FLASK_ENV=development|production|testing
FLASK_DEBUG=true|false
SECRET_KEY=your-secret-key

# 数据库配置
DATABASE_URL=sqlite:///faka_mall.db
DB_PATH=faka_mall.db

# 会话配置
SESSION_COOKIE_SECURE=true|false

# 业务配置
LOW_STOCK_THRESHOLD=10
ORDER_TIMEOUT_MINUTES=30
PAYMENT_TIMEOUT_MINUTES=30

# 日志配置
LOG_LEVEL=DEBUG|INFO|WARNING|ERROR
LOG_FILE=logs/faka_mall.log

# CORS配置
CORS_ORIGINS=http://localhost:3000,https://yourdomain.com
```

### 配置类
- `DevelopmentConfig`: 开发环境配置
- `ProductionConfig`: 生产环境配置
- `TestingConfig`: 测试环境配置

## 高级功能

### 1. 数据分析
- 产品销售统计
- 订单状态分析
- 库存价值分析
- 月度收入报告
- 热销产品排行

### 2. 批量操作
- 批量产品管理
- 批量订单处理
- 数据导入导出
- 批量状态更新

### 3. 系统监控
- 健康检查接口
- 操作日志记录
- 错误统计分析
- 性能监控预留

### 4. 安全增强
- 会话管理
- 权限控制
- 数据验证
- 错误处理

## 扩展建议

1. **缓存系统**: 集成Redis缓存热点数据
2. **消息队列**: 使用Celery处理异步任务
3. **文件上传**: 支持产品图片上传和管理
4. **邮件通知**: 订单状态变更邮件通知
5. **API文档**: 集成Swagger自动生成API文档
6. **单元测试**: 添加完整的测试覆盖
7. **容器化**: Docker部署支持
8. **微服务**: 按业务模块拆分服务
9. **实时通知**: WebSocket支持
10. **数据备份**: 自动备份机制

## 部署指南

### 开发环境
```bash
# 1. 克隆项目
git clone <repository-url>
cd faka-mall/backend

# 2. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 3. 安装依赖
pip install -r requirements.txt

# 4. 设置环境变量
export FLASK_ENV=development
export FLASK_DEBUG=true

# 5. 启动应用
python app.py
```

### 生产环境
```bash
# 1. 设置环境变量
export FLASK_ENV=production
export SECRET_KEY=your-production-secret-key
export DATABASE_URL=your-production-database-url

# 2. 使用WSGI服务器
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

## 注意事项

1. **安全配置**
   - 生产环境必须修改默认的 `SECRET_KEY`
   - 启用HTTPS和安全头
   - 配置防火墙和访问控制

2. **数据库优化**
   - 生产环境建议使用PostgreSQL或MySQL
   - 添加适当的数据库索引
   - 定期备份数据

3. **性能优化**
   - 实施API限流
   - 添加缓存机制
   - 优化数据库查询

4. **监控和日志**
   - 配置日志轮转
   - 添加性能监控
   - 设置错误报警

5. **扩展性**
   - 考虑微服务架构
   - 使用负载均衡
   - 实施水平扩展
