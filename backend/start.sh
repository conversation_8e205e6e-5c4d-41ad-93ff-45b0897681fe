#!/bin/bash
# 发卡商城后端启动脚本

echo "=== 发卡商城后端启动脚本 ==="

# 检查虚拟环境是否存在
if [ ! -d "venv" ]; then
    echo "创建虚拟环境..."
    python -m venv venv
    
    echo "激活虚拟环境并安装依赖..."
    source venv/bin/activate
    pip install -r requirements.txt
else
    echo "激活虚拟环境..."
    source venv/bin/activate
fi

# 检查依赖是否安装
echo "检查依赖..."
python -c "import flask, flask_cors" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "安装依赖..."
    pip install -r requirements.txt
fi

echo "启动应用..."
echo "访问地址: http://127.0.0.1:5000"
echo "按 Ctrl+C 停止服务"
echo ""

python app.py
