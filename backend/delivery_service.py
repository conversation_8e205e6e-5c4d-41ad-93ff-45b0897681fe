# backend/delivery_service.py
"""
虚拟商品发货服务
处理虚拟商品的自动发货逻辑
"""

import json
import random
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

class DeliveryService:
    """虚拟商品发货服务"""
    
    def __init__(self, app=None):
        self.app = app
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化应用"""
        self.app = app
        
        # 邮件配置
        self.smtp_host = app.config.get('SMTP_HOST', 'localhost')
        self.smtp_port = app.config.get('SMTP_PORT', 587)
        self.smtp_username = app.config.get('SMTP_USERNAME', '<EMAIL>')
        self.smtp_password = app.config.get('SMTP_PASSWORD', '')
        self.smtp_use_tls = app.config.get('SMTP_USE_TLS', True)
        
        # 默认发件人
        self.default_sender = app.config.get('DEFAULT_SENDER', '<EMAIL>')
    
    def get_delivery_content(self, product: Dict, order: Dict) -> Tuple[str, bool]:
        """
        获取发货内容
        
        Args:
            product: 商品信息
            order: 订单信息
            
        Returns:
            Tuple[str, bool]: (发货内容, 是否成功)
        """
        try:
            delivery_type = product.get('delivery_type', 'text')
            delivery_content = product.get('delivery_content', '')
            
            if not delivery_content:
                return "商品暂无发货内容", False
            
            if delivery_type == 'list':
                # 列表类型：随机选择一个未使用的项目
                return self._get_list_content(product, order)
            else:
                # 文本类型：直接返回内容
                return delivery_content, True
                
        except Exception as e:
            logger.error(f"获取发货内容失败: {e}")
            return f"获取发货内容失败: {str(e)}", False
    
    def _get_list_content(self, product: Dict, order: Dict) -> Tuple[str, bool]:
        """
        从列表中获取发货内容
        
        Args:
            product: 商品信息
            order: 订单信息
            
        Returns:
            Tuple[str, bool]: (发货内容, 是否成功)
        """
        try:
            # 解析发货列表
            delivery_list = json.loads(product.get('delivery_content', '[]'))
            
            if not delivery_list:
                return "发货列表为空", False
            
            # 获取已使用的项目（这里简化处理，实际应该从数据库获取）
            used_items = self._get_used_items(product['id'])
            
            # 筛选未使用的项目
            available_items = [item for item in delivery_list if item not in used_items]
            
            if not available_items:
                return "所有发货项目已用完，请联系客服", False
            
            # 随机选择一个项目
            selected_item = random.choice(available_items)
            
            # 标记为已使用
            self._mark_item_used(product['id'], selected_item, order['id'])
            
            return selected_item, True
            
        except json.JSONDecodeError:
            return "发货列表格式错误", False
        except Exception as e:
            logger.error(f"从列表获取发货内容失败: {e}")
            return f"获取发货内容失败: {str(e)}", False
    
    def _get_used_items(self, product_id: int) -> List[str]:
        """
        获取已使用的发货项目
        
        Args:
            product_id: 商品ID
            
        Returns:
            List[str]: 已使用的项目列表
        """
        # 这里应该从数据库获取已使用的项目
        # 简化处理，返回空列表
        return []
    
    def _mark_item_used(self, product_id: int, item: str, order_id: int):
        """
        标记发货项目为已使用
        
        Args:
            product_id: 商品ID
            item: 发货项目
            order_id: 订单ID
        """
        # 这里应该将使用记录保存到数据库
        logger.info(f"商品 {product_id} 的发货项目 '{item}' 已用于订单 {order_id}")
    
    def send_delivery_email(self, order: Dict, product: Dict, delivery_content: str) -> bool:
        """
        发送发货邮件
        
        Args:
            order: 订单信息
            product: 商品信息
            delivery_content: 发货内容
            
        Returns:
            bool: 是否发送成功
        """
        try:
            # 获取收件人邮箱
            recipient_email = order.get('customer_email')
            if not recipient_email:
                logger.error("订单缺少客户邮箱")
                return False
            
            # 构建邮件内容
            subject = product.get('email_subject') or f"【青云小铺】您购买的商品已发货"
            email_content = self._build_email_content(order, product, delivery_content)
            
            # 发送邮件
            return self._send_email(
                to_email=recipient_email,
                subject=subject,
                content=email_content,
                sender_email=product.get('sender_email') or self.default_sender
            )
            
        except Exception as e:
            logger.error(f"发送发货邮件失败: {e}")
            return False
    
    def _build_email_content(self, order: Dict, product: Dict, delivery_content: str) -> str:
        """
        构建邮件内容
        
        Args:
            order: 订单信息
            product: 商品信息
            delivery_content: 发货内容
            
        Returns:
            str: 邮件内容
        """
        # 获取邮件模板
        template = product.get('email_template') or self._get_default_email_template()
        
        # 替换模板变量
        content = template.format(
            product_name=product.get('name', '未知商品'),
            order_id=order.get('id', '未知'),
            purchase_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            delivery_content=delivery_content,
            customer_email=order.get('customer_email', ''),
            store_name='青云小铺',
            store_email=self.default_sender
        )
        
        return content
    
    def _get_default_email_template(self) -> str:
        """获取默认邮件模板"""
        return """亲爱的用户，

感谢您在青云小铺的购买！

您购买的商品：{product_name}
订单号：{order_id}
购买时间：{purchase_time}

发货内容：
{delivery_content}

请妥善保存以上内容。如有任何问题，请联系我们的客服。

感谢您的信任与支持！

青云小铺
{store_email}
"""
    
    def _send_email(self, to_email: str, subject: str, content: str, sender_email: str = None) -> bool:
        """
        发送邮件
        
        Args:
            to_email: 收件人邮箱
            subject: 邮件主题
            content: 邮件内容
            sender_email: 发件人邮箱
            
        Returns:
            bool: 是否发送成功
        """
        try:
            sender_email = sender_email or self.default_sender
            
            # 创建邮件
            msg = MIMEMultipart()
            msg['From'] = sender_email
            msg['To'] = to_email
            msg['Subject'] = subject
            
            # 添加邮件内容
            msg.attach(MIMEText(content, 'plain', 'utf-8'))
            
            # 发送邮件
            if self.smtp_host == 'localhost':
                # 本地测试模式，只记录日志
                logger.info(f"模拟发送邮件到 {to_email}")
                logger.info(f"主题: {subject}")
                logger.info(f"内容: {content}")
                return True
            else:
                # 实际发送邮件
                with smtplib.SMTP(self.smtp_host, self.smtp_port) as server:
                    if self.smtp_use_tls:
                        server.starttls()
                    
                    if self.smtp_username and self.smtp_password:
                        server.login(self.smtp_username, self.smtp_password)
                    
                    server.send_message(msg)
                
                logger.info(f"邮件发送成功到 {to_email}")
                return True
                
        except Exception as e:
            logger.error(f"发送邮件失败: {e}")
            return False
    
    def process_order_delivery(self, order: Dict, product: Dict) -> Dict:
        """
        处理订单发货
        
        Args:
            order: 订单信息
            product: 商品信息
            
        Returns:
            Dict: 发货结果
        """
        try:
            # 获取发货内容
            delivery_content, success = self.get_delivery_content(product, order)
            
            if not success:
                return {
                    'success': False,
                    'message': delivery_content,
                    'delivery_content': None
                }
            
            # 发送邮件
            email_sent = self.send_delivery_email(order, product, delivery_content)
            
            # 记录发货信息
            delivery_record = {
                'order_id': order['id'],
                'product_id': product['id'],
                'delivery_content': delivery_content,
                'delivery_time': datetime.now().isoformat(),
                'email_sent': email_sent,
                'customer_email': order.get('customer_email')
            }
            
            # 这里应该将发货记录保存到数据库
            logger.info(f"订单 {order['id']} 发货完成: {delivery_record}")
            
            return {
                'success': True,
                'message': '发货成功',
                'delivery_content': delivery_content,
                'email_sent': email_sent,
                'delivery_record': delivery_record
            }
            
        except Exception as e:
            logger.error(f"处理订单发货失败: {e}")
            return {
                'success': False,
                'message': f'发货失败: {str(e)}',
                'delivery_content': None
            }

# 创建全局实例
delivery_service = DeliveryService()
