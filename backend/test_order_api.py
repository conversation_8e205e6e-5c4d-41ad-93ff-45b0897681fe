#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单API测试脚本
测试订单相关的所有API功能
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = 'http://127.0.0.1:5000'

def test_order_api():
    """测试订单API功能"""
    print("=== 订单API功能测试 ===\n")
    
    # 创建管理员会话
    admin_session = requests.Session()
    
    # 1. 管理员登录
    print("1. 管理员登录")
    admin_login = {
        "username": "admin",
        "password": "admin123"
    }
    response = admin_session.post(f"{BASE_URL}/api/auth/login", json=admin_login)
    print(f"   登录状态码: {response.status_code}")
    if response.status_code != 200:
        print("   管理员登录失败，创建管理员账户...")
        # 创建管理员账户
        admin_data = {
            "username": "admin",
            "email": "<EMAIL>",
            "password": "admin123",
            "is_admin": True
        }
        requests.post(f"{BASE_URL}/api/auth/register", json=admin_data)
        response = admin_session.post(f"{BASE_URL}/api/auth/login", json=admin_login)
        print(f"   重新登录状态码: {response.status_code}")
    
    # 2. 创建测试产品
    print("\n2. 创建测试产品")
    test_products = [
        {"name": "测试产品A", "price": 99.99, "stock": 50, "description": "这是测试产品A"},
        {"name": "测试产品B", "price": 199.99, "stock": 30, "description": "这是测试产品B"},
        {"name": "测试产品C", "price": 299.99, "stock": 20, "description": "这是测试产品C"}
    ]
    
    created_products = []
    for product_data in test_products:
        response = admin_session.post(f"{BASE_URL}/api/products", json=product_data)
        if response.status_code == 201:
            product = response.json().get('product', {})
            created_products.append(product)
            print(f"   创建产品: {product['name']} (ID: {product['id']})")
    
    if not created_products:
        print("   没有创建成功的产品，无法继续测试")
        return
    
    # 3. 创建普通用户并登录
    print("\n3. 创建普通用户")
    user_session = requests.Session()
    user_data = {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "123456"
    }
    response = requests.post(f"{BASE_URL}/api/auth/register", json=user_data)
    print(f"   注册状态码: {response.status_code}")
    
    # 用户登录
    login_data = {"username": "testuser", "password": "123456"}
    response = user_session.post(f"{BASE_URL}/api/auth/login", json=login_data)
    print(f"   登录状态码: {response.status_code}")
    
    # 4. 测试创建订单
    print("\n4. 测试创建订单")
    product = created_products[0]
    order_data = {
        "product_id": product['id'],
        "quantity": 2,
        "notes": "这是一个测试订单"
    }
    response = user_session.post(f"{BASE_URL}/api/orders", json=order_data)
    print(f"   创建订单状态码: {response.status_code}")
    
    created_order = None
    if response.status_code == 201:
        result = response.json()
        created_order = result.get('order', {})
        print(f"   订单创建成功: ID {created_order['id']}, 总价 {created_order['total_price']}")
    
    # 5. 测试获取订单详情
    if created_order:
        print("\n5. 测试获取订单详情")
        order_id = created_order['id']
        response = user_session.get(f"{BASE_URL}/api/orders/{order_id}")
        print(f"   获取订单详情状态码: {response.status_code}")
        if response.status_code == 200:
            order_detail = response.json()
            print(f"   订单状态: {order_detail['order']['status']}")
            print(f"   权限信息: {order_detail.get('permissions', {})}")
    
    # 6. 测试获取用户订单列表
    print("\n6. 测试获取用户订单列表")
    response = user_session.get(f"{BASE_URL}/api/orders/my?page=1&limit=5")
    print(f"   获取用户订单状态码: {response.status_code}")
    if response.status_code == 200:
        orders = response.json()
        print(f"   用户订单数量: {len(orders.get('items', []))}")
    
    # 7. 测试订单过滤功能
    print("\n7. 测试订单过滤功能")
    filter_params = {
        'status': 'pending',
        'sort_by': 'total_price',
        'sort_order': 'desc'
    }
    response = admin_session.get(f"{BASE_URL}/api/orders", params=filter_params)
    print(f"   过滤订单状态码: {response.status_code}")
    if response.status_code == 200:
        filtered_orders = response.json()
        print(f"   过滤后订单数量: {len(filtered_orders.get('items', []))}")
    
    # 8. 测试订单状态更新
    if created_order:
        print("\n8. 测试订单状态更新")
        order_id = created_order['id']
        status_data = {"status": "processing"}
        response = admin_session.put(f"{BASE_URL}/api/orders/{order_id}/status", json=status_data)
        print(f"   更新订单状态码: {response.status_code}")
        if response.status_code == 200:
            updated_order = response.json()
            print(f"   新状态: {updated_order['order']['status']}")
    
    # 9. 测试订单取消功能
    print("\n9. 测试订单取消功能")
    # 创建一个新订单用于取消测试
    cancel_order_data = {
        "product_id": product['id'],
        "quantity": 1,
        "notes": "用于取消测试的订单"
    }
    response = user_session.post(f"{BASE_URL}/api/orders", json=cancel_order_data)
    if response.status_code == 201:
        cancel_order = response.json()['order']
        order_id = cancel_order['id']
        
        # 取消订单
        cancel_data = {"reason": "用户主动取消"}
        response = user_session.post(f"{BASE_URL}/api/orders/{order_id}/cancel", json=cancel_data)
        print(f"   取消订单状态码: {response.status_code}")
        if response.status_code == 200:
            cancelled_order = response.json()
            print(f"   订单已取消: {cancelled_order['order']['status']}")
    
    # 10. 测试订单统计功能
    print("\n10. 测试订单统计功能")
    response = admin_session.get(f"{BASE_URL}/api/orders/statistics")
    print(f"   获取统计状态码: {response.status_code}")
    if response.status_code == 200:
        stats = response.json()
        print(f"   统计信息: {stats.get('statistics', {})}")
        print(f"   状态分布: {stats.get('status_distribution', {})}")
    
    # 11. 测试批量状态更新
    print("\n11. 测试批量状态更新")
    # 创建多个订单用于批量测试
    batch_orders = []
    for i in range(3):
        batch_order_data = {
            "product_id": product['id'],
            "quantity": 1,
            "notes": f"批量测试订单 {i+1}"
        }
        response = user_session.post(f"{BASE_URL}/api/orders", json=batch_order_data)
        if response.status_code == 201:
            batch_orders.append(response.json()['order']['id'])
    
    if batch_orders:
        batch_update_data = {
            "order_ids": batch_orders,
            "status": "processing"
        }
        response = admin_session.post(f"{BASE_URL}/api/orders/batch/update-status", json=batch_update_data)
        print(f"   批量更新状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   成功更新: {result['success_count']} 个订单")
            print(f"   失败数量: {result['failed_count']}")
    
    # 12. 测试订单导出功能
    print("\n12. 测试订单导出功能")
    export_params = {
        'format': 'json',
        'status': 'processing'
    }
    response = admin_session.get(f"{BASE_URL}/api/orders/export", params=export_params)
    print(f"   导出订单状态码: {response.status_code}")
    if response.status_code == 200:
        export_data = response.json()
        print(f"   导出订单数量: {export_data['count']}")
        print(f"   导出格式: {export_data['format']}")
    
    # 13. 测试错误处理
    print("\n13. 测试错误处理")
    
    # 测试无效订单ID
    response = user_session.get(f"{BASE_URL}/api/orders/99999")
    print(f"   无效订单ID状态码: {response.status_code}")
    
    # 测试库存不足
    insufficient_order_data = {
        "product_id": product['id'],
        "quantity": 1000  # 超过库存
    }
    response = user_session.post(f"{BASE_URL}/api/orders", json=insufficient_order_data)
    print(f"   库存不足状态码: {response.status_code}")
    
    # 测试权限不足
    response = user_session.get(f"{BASE_URL}/api/orders/statistics")
    print(f"   权限不足状态码: {response.status_code}")
    
    print("\n=== 订单API测试完成 ===")

if __name__ == "__main__":
    try:
        test_order_api()
    except requests.exceptions.ConnectionError:
        print("错误: 无法连接到服务器，请确保应用正在运行")
        print("启动命令: cd backend && source venv/bin/activate && python app.py")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
