<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>商品详情 - 发卡商城</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: #333;
    }
    
    .header {
      background: white;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      padding: 1rem 0;
      position: sticky;
      top: 0;
      z-index: 100;
    }
    
    .header-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .logo {
      font-size: 1.5rem;
      font-weight: 700;
      color: #667eea;
      text-decoration: none;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
    
    .nav-links {
      display: flex;
      gap: 2rem;
      align-items: center;
    }
    
    .nav-link {
      color: #666;
      text-decoration: none;
      font-weight: 500;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      transition: all 0.3s ease;
    }
    
    .nav-link:hover {
      background: #f8f9fa;
      color: #667eea;
    }
    
    .cart-badge {
      background: #dc3545;
      color: white;
      padding: 0.2rem 0.5rem;
      border-radius: 50%;
      font-size: 0.75rem;
      margin-left: 0.5rem;
      min-width: 1.2rem;
      text-align: center;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem 1rem;
    }
    
    .breadcrumb {
      background: rgba(255,255,255,0.1);
      padding: 1rem;
      border-radius: 8px;
      margin-bottom: 2rem;
      color: white;
    }
    
    .breadcrumb a {
      color: rgba(255,255,255,0.8);
      text-decoration: none;
      margin-right: 0.5rem;
    }
    
    .breadcrumb a:hover {
      color: white;
    }
    
    .product-detail-section {
      background: white;
      border-radius: 20px;
      padding: 2rem;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      margin-bottom: 2rem;
    }
    
    .product-detail-container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 3rem;
      align-items: start;
    }
    
    .product-image-section {
      position: sticky;
      top: 120px;
    }
    
    .product-image {
      width: 100%;
      height: 400px;
      object-fit: cover;
      border-radius: 16px;
      box-shadow: 0 8px 24px rgba(0,0,0,0.1);
    }
    
    .product-info {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
    }
    
    .product-title {
      font-size: 2rem;
      font-weight: 700;
      color: #333;
      line-height: 1.2;
    }
    
    .product-price {
      font-size: 2.5rem;
      font-weight: 700;
      color: #667eea;
      margin: 1rem 0;
    }
    
    .product-meta {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 1rem;
      padding: 1.5rem;
      background: #f8f9fa;
      border-radius: 12px;
    }
    
    .meta-item {
      text-align: center;
    }
    
    .meta-label {
      font-size: 0.875rem;
      color: #666;
      margin-bottom: 0.5rem;
    }
    
    .meta-value {
      font-weight: 600;
      color: #333;
    }
    
    .stock-status {
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-weight: 600;
      font-size: 0.875rem;
      display: inline-block;
    }
    
    .stock-available {
      background: #d4edda;
      color: #155724;
    }
    
    .stock-low {
      background: #fff3cd;
      color: #856404;
    }
    
    .stock-out {
      background: #f8d7da;
      color: #721c24;
    }
    
    .product-description {
      line-height: 1.6;
      color: #555;
      font-size: 1.1rem;
    }
    
    .quantity-selector {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin: 1.5rem 0;
    }
    
    .quantity-label {
      font-weight: 600;
      color: #333;
    }
    
    .quantity-controls {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      background: #f8f9fa;
      padding: 0.5rem;
      border-radius: 8px;
      border: 1px solid #e9ecef;
    }
    
    .quantity-btn {
      width: 40px;
      height: 40px;
      border: 1px solid #d1d5db;
      background: white;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      font-weight: 600;
      font-size: 1.2rem;
    }
    
    .quantity-btn:hover:not(:disabled) {
      background: #667eea;
      color: white;
      border-color: #667eea;
    }
    
    .quantity-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    .quantity-input {
      width: 60px;
      text-align: center;
      font-weight: 600;
      font-size: 1.1rem;
      border: none;
      background: transparent;
    }
    
    .action-buttons {
      display: flex;
      gap: 1rem;
      margin-top: 2rem;
    }
    
    .btn {
      padding: 1rem 2rem;
      border: none;
      border-radius: 10px;
      font-weight: 600;
      text-decoration: none;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 1rem;
      flex: 1;
    }
    
    .btn-primary {
      background: #667eea;
      color: white;
    }
    
    .btn-primary:hover:not(:disabled) {
      background: #5a67d8;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
    
    .btn-primary:disabled {
      background: #9ca3af;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
    
    .btn-outline {
      background: transparent;
      color: #667eea;
      border: 2px solid #667eea;
    }
    
    .btn-outline:hover {
      background: #667eea;
      color: white;
    }
    
    .loading {
      text-align: center;
      padding: 3rem;
      color: #666;
    }
    
    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #667eea;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .error-state {
      text-align: center;
      padding: 3rem;
      color: #dc3545;
    }
    
    .footer {
      background: #333;
      color: white;
      text-align: center;
      padding: 2rem;
    }
    
    .toast {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: #28a745;
      color: white;
      padding: 1rem 1.5rem;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 1000;
      transform: translateY(100px);
      opacity: 0;
      transition: all 0.3s ease;
    }
    
    .toast.show {
      transform: translateY(0);
      opacity: 1;
    }
    
    .toast.error {
      background: #dc3545;
    }
    
    .toast.warning {
      background: #ffc107;
      color: #333;
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
      .product-detail-container {
        grid-template-columns: 1fr;
        gap: 2rem;
      }
      
      .product-image-section {
        position: static;
      }
      
      .product-title {
        font-size: 1.5rem;
      }
      
      .product-price {
        font-size: 2rem;
      }
      
      .action-buttons {
        flex-direction: column;
      }
      
      .nav-links {
        gap: 1rem;
      }
      
      .nav-link {
        padding: 0.5rem;
        font-size: 0.9rem;
      }
    }
  </style>
</head>
<body>
  <header class="header">
    <div class="header-container">
      <a href="index_standalone.html" class="logo">
        🛒 发卡商城
      </a>
      <nav class="nav-links">
        <a href="index_standalone.html" class="nav-link">首页</a>
        <a href="cart_optimized.html" class="nav-link">购物车 <span id="cart-count" class="cart-badge">0</span></a>
        <a href="admin/login.html" class="nav-link">管理后台</a>
      </nav>
    </div>
  </header>

  <div class="container">
    <div class="breadcrumb">
      <a href="index_standalone.html">首页</a> > 
      <span id="breadcrumb-category">商品详情</span> > 
      <span id="breadcrumb-product">加载中...</span>
    </div>
    
    <div class="product-detail-section">
      <div id="product-content">
        <div class="loading">
          <div class="spinner"></div>
          <p>正在加载商品详情...</p>
        </div>
      </div>
    </div>
  </div>

  <footer class="footer">
    <p>&copy; 2025 发卡商城. 保留所有权利。</p>
  </footer>

  <script>
    // 购物车管理
    const Cart = {
      get() {
        try {
          return JSON.parse(localStorage.getItem('cart') || '[]');
        } catch {
          return [];
        }
      },

      add(product, quantity = 1) {
        const cart = this.get();
        const existingItem = cart.find(item => item.product_id === product.id);

        if (existingItem) {
          existingItem.quantity += quantity;
        } else {
          cart.push({
            product_id: product.id,
            name: product.name,
            price: product.price,
            quantity: quantity,
            image: `https://picsum.photos/100/100?random=${product.id}`
          });
        }

        localStorage.setItem('cart', JSON.stringify(cart));
        this.updateCount();
        this.showToast(`已添加到购物车: ${product.name} x${quantity}`);
      },

      updateCount() {
        const cart = this.get();
        const count = cart.reduce((total, item) => total + item.quantity, 0);
        document.getElementById('cart-count').textContent = count;
      },

      showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => toast.classList.add('show'), 10);
        setTimeout(() => {
          toast.classList.remove('show');
          setTimeout(() => toast.remove(), 300);
        }, 3000);
      }
    };

    // 全局变量
    let currentProduct = null;
    let currentQuantity = 1;

    // 获取URL参数
    function getProductId() {
      const urlParams = new URLSearchParams(window.location.search);
      const id = parseInt(urlParams.get('id'));
      return isNaN(id) ? null : id;
    }

    // 格式化价格
    function formatPrice(price) {
      return `¥${parseFloat(price).toFixed(2)}`;
    }

    // 格式化库存状态
    function formatStockStatus(stock) {
      const num = parseInt(stock) || 0;
      if (num <= 0) {
        return { text: '缺货', class: 'stock-out' };
      } else if (num <= 10) {
        return { text: `库存紧张 (${num})`, class: 'stock-low' };
      } else {
        return { text: `库存充足 (${num})`, class: 'stock-available' };
      }
    }

    // 创建商品详情HTML
    function createProductDetail(product) {
      const stockStatus = formatStockStatus(product.stock);

      return `
        <div class="product-detail-container">
          <div class="product-image-section">
            <img src="https://picsum.photos/500/400?random=${product.id}"
                 alt="${product.name}"
                 class="product-image"
                 loading="lazy">
          </div>

          <div class="product-info">
            <h1 class="product-title">${product.name}</h1>

            <div class="product-price">${formatPrice(product.price)}</div>

            <div class="product-meta">
              <div class="meta-item">
                <div class="meta-label">商品ID</div>
                <div class="meta-value">#${product.id}</div>
              </div>
              <div class="meta-item">
                <div class="meta-label">库存状态</div>
                <div class="meta-value">
                  <span class="stock-status ${stockStatus.class}">${stockStatus.text}</span>
                </div>
              </div>
              <div class="meta-item">
                <div class="meta-label">发货方式</div>
                <div class="meta-value">自动发货</div>
              </div>
              <div class="meta-item">
                <div class="meta-label">商品类型</div>
                <div class="meta-value">虚拟商品</div>
              </div>
            </div>

            <div class="product-description">
              <h3 style="margin-bottom: 1rem; color: #333;">商品描述</h3>
              <p>${product.description || '这是一款优质的虚拟商品，购买后将自动发货到您的邮箱。'}</p>
            </div>

            <div class="quantity-selector">
              <span class="quantity-label">购买数量:</span>
              <div class="quantity-controls">
                <button class="quantity-btn" onclick="updateQuantity(-1)" id="decrease-btn">-</button>
                <input type="number" class="quantity-input" value="1" min="1" max="${product.stock}"
                       id="quantity-input" onchange="setQuantity(this.value)">
                <button class="quantity-btn" onclick="updateQuantity(1)" id="increase-btn">+</button>
              </div>
            </div>

            <div class="action-buttons">
              <button class="btn btn-outline" onclick="goBack()">返回商品列表</button>
              <button class="btn btn-primary" onclick="addToCart()" id="add-to-cart-btn"
                      ${product.stock <= 0 ? 'disabled' : ''}>
                ${product.stock <= 0 ? '暂时缺货' : '加入购物车'}
              </button>
            </div>
          </div>
        </div>
      `;
    }

    // 加载商品详情
    async function loadProductDetail() {
      const productId = getProductId();
      const productContent = document.getElementById('product-content');
      const breadcrumbProduct = document.getElementById('breadcrumb-product');

      if (!productId) {
        productContent.innerHTML = `
          <div class="error-state">
            <h3>❌ 无效的商品ID</h3>
            <p>请检查URL中的商品ID是否正确</p>
            <button class="btn btn-primary" onclick="goBack()">返回商品列表</button>
          </div>
        `;
        return;
      }

      try {
        console.log('加载商品详情:', productId);

        const response = await fetch(`http://localhost:5000/api/products/${productId}`, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          },
          mode: 'cors'
        });

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('商品不存在');
          }
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        currentProduct = await response.json();
        console.log('商品详情:', currentProduct);

        // 更新页面标题和面包屑
        document.title = `${currentProduct.name} - 发卡商城`;
        breadcrumbProduct.textContent = currentProduct.name;

        // 渲染商品详情
        productContent.innerHTML = createProductDetail(currentProduct);

        // 更新数量控制
        updateQuantityControls();

      } catch (error) {
        console.error('加载商品详情失败:', error);
        productContent.innerHTML = `
          <div class="error-state">
            <h3>❌ 加载失败</h3>
            <p><strong>错误:</strong> ${error.message}</p>
            <p><strong>可能原因:</strong></p>
            <ul style="text-align: left; display: inline-block; margin: 1rem 0;">
              <li>商品不存在或已下架</li>
              <li>网络连接问题</li>
              <li>服务器暂时不可用</li>
            </ul>
            <button class="btn btn-primary" onclick="loadProductDetail()">重试</button>
            <button class="btn btn-outline" onclick="goBack()">返回商品列表</button>
          </div>
        `;
      }
    }

    // 更新数量
    function updateQuantity(change) {
      if (!currentProduct) return;

      const newQuantity = currentQuantity + change;
      if (newQuantity >= 1 && newQuantity <= currentProduct.stock) {
        currentQuantity = newQuantity;
        document.getElementById('quantity-input').value = currentQuantity;
        updateQuantityControls();
      }
    }

    // 设置数量
    function setQuantity(value) {
      if (!currentProduct) return;

      const newQuantity = parseInt(value) || 1;
      if (newQuantity >= 1 && newQuantity <= currentProduct.stock) {
        currentQuantity = newQuantity;
        updateQuantityControls();
      } else {
        document.getElementById('quantity-input').value = currentQuantity;
      }
    }

    // 更新数量控制按钮状态
    function updateQuantityControls() {
      if (!currentProduct) return;

      const decreaseBtn = document.getElementById('decrease-btn');
      const increaseBtn = document.getElementById('increase-btn');

      if (decreaseBtn) decreaseBtn.disabled = currentQuantity <= 1;
      if (increaseBtn) increaseBtn.disabled = currentQuantity >= currentProduct.stock;
    }

    // 添加到购物车
    function addToCart() {
      if (!currentProduct) {
        Cart.showToast('商品信息加载中，请稍后重试', 'warning');
        return;
      }

      if (currentProduct.stock <= 0) {
        Cart.showToast('商品暂时缺货', 'error');
        return;
      }

      if (currentQuantity > currentProduct.stock) {
        Cart.showToast('购买数量超过库存', 'error');
        return;
      }

      Cart.add(currentProduct, currentQuantity);
    }

    // 返回商品列表
    function goBack() {
      window.location.href = 'index_standalone.html';
    }

    // 页面初始化
    document.addEventListener('DOMContentLoaded', () => {
      console.log('商品详情页面加载完成');
      Cart.updateCount();
      loadProductDetail();
    });
  </script>
</body>
</html>
