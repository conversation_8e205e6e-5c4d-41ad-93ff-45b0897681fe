<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>支付页面</title>
  <link rel="stylesheet" href="../static/style.css" />
</head>
<body>

  <header>
    <h1>支付页面</h1>
  </header>

  <div class="container">
    <h2>订单信息</h2>
    <p>订单号：<span id="order-id"></span></p>
    <p>金额：<span id="order-amount"></span></p>
    <button onclick="simulatePayment()">模拟支付</button>
  </div>

  <footer>
    &copy; 2025 发卡商城. 保留所有权利。
  </footer>

  <script>
    const urlParams = new URLSearchParams(window.location.search);
    const orderId = urlParams.get('order_id');

    fetch(`http://localhost:5000/api/order/${orderId}`)
      .then(res => res.json())
      .then(order => {
        document.getElementById('order-id').innerText = order.id;
        document.getElementById('order-amount').innerText = `￥${order.amount}`;
      });

    function simulatePayment() {
      fetch(`http://localhost:5000/api/order/${orderId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'paid' })
      }).then(() => {
        alert('支付成功！');
        window.location.href = 'cart.html';
      });
    }
  </script>

</body>
</html>