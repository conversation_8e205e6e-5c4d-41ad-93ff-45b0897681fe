<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>系统设置 - 青云小铺管理后台</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background: #f8fafc;
      color: #333;
    }
    
    .container {
      display: flex;
      min-height: 100vh;
    }
    
    .sidebar {
      width: 280px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 2rem 0;
      position: fixed;
      height: 100vh;
      overflow-y: auto;
    }
    
    .sidebar-header {
      padding: 0 2rem 2rem;
      border-bottom: 1px solid rgba(255,255,255,0.1);
      margin-bottom: 2rem;
      display: flex;
      align-items: center;
      gap: 1rem;
    }
    
    .sidebar-logo {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      background: white;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .sidebar-logo img {
      width: 32px;
      height: 32px;
      border-radius: 6px;
    }
    
    .sidebar-title {
      font-size: 1.5rem;
      font-weight: 700;
    }
    
    .sidebar-subtitle {
      opacity: 0.8;
      font-size: 0.9rem;
    }
    
    .sidebar-nav {
      padding: 0 1rem;
    }
    
    .nav-item {
      display: block;
      color: rgba(255,255,255,0.8);
      text-decoration: none;
      padding: 1rem;
      margin: 0.25rem 0;
      border-radius: 8px;
      transition: all 0.3s ease;
      font-weight: 500;
    }
    
    .nav-item:hover,
    .nav-item.active {
      background: rgba(255,255,255,0.1);
      color: white;
      transform: translateX(4px);
    }
    
    .nav-item.active {
      background: rgba(255,255,255,0.2);
    }
    
    .main {
      flex: 1;
      margin-left: 280px;
      padding: 2rem;
    }
    
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;
      background: white;
      padding: 1.5rem 2rem;
      border-radius: 16px;
      box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    }
    
    .header h1 {
      font-size: 1.75rem;
      font-weight: 700;
      color: #1f2937;
    }
    
    .user-info {
      display: flex;
      align-items: center;
      gap: 1rem;
      color: #6b7280;
    }
    
    .user-avatar {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 600;
    }
    
    .logout-btn {
      color: #ef4444;
      text-decoration: none;
      font-weight: 500;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      transition: background 0.3s ease;
    }
    
    .logout-btn:hover {
      background: #fef2f2;
    }
    
    .settings-grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: 2rem;
    }
    
    .settings-card {
      background: white;
      border-radius: 16px;
      padding: 2rem;
      box-shadow: 0 4px 6px rgba(0,0,0,0.05);
      border: 1px solid #e5e7eb;
    }
    
    .settings-header {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1.5rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid #e5e7eb;
    }
    
    .settings-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.2rem;
      background: #f3f4f6;
    }
    
    .settings-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1f2937;
    }
    
    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1.5rem;
      margin-bottom: 1.5rem;
    }
    
    .form-group {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }
    
    .form-label {
      font-weight: 500;
      color: #374151;
      font-size: 0.9rem;
    }
    
    .form-input,
    .form-textarea,
    .form-select {
      padding: 0.75rem 1rem;
      border: 1px solid #d1d5db;
      border-radius: 8px;
      font-size: 0.9rem;
      transition: border-color 0.3s ease;
    }
    
    .form-input:focus,
    .form-textarea:focus,
    .form-select:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .form-textarea {
      resize: vertical;
      min-height: 100px;
    }
    
    .form-switch {
      display: flex;
      align-items: center;
      gap: 0.75rem;
    }
    
    .switch {
      position: relative;
      width: 44px;
      height: 24px;
      background: #d1d5db;
      border-radius: 12px;
      cursor: pointer;
      transition: background 0.3s ease;
    }
    
    .switch.active {
      background: #667eea;
    }
    
    .switch::after {
      content: '';
      position: absolute;
      top: 2px;
      left: 2px;
      width: 20px;
      height: 20px;
      background: white;
      border-radius: 50%;
      transition: transform 0.3s ease;
    }
    
    .switch.active::after {
      transform: translateX(20px);
    }
    
    .btn {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 8px;
      font-weight: 500;
      text-decoration: none;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 0.9rem;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }
    
    .btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    
    .btn-primary:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
    
    .btn-outline {
      background: transparent;
      color: #667eea;
      border: 1px solid #667eea;
    }
    
    .btn-outline:hover {
      background: #667eea;
      color: white;
    }
    
    .btn-danger {
      background: #ef4444;
      color: white;
    }
    
    .btn-danger:hover {
      background: #dc2626;
    }
    
    .settings-actions {
      display: flex;
      gap: 1rem;
      justify-content: flex-end;
      margin-top: 1.5rem;
      padding-top: 1.5rem;
      border-top: 1px solid #e5e7eb;
    }
    
    .toast {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: #10b981;
      color: white;
      padding: 1rem 1.5rem;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 1000;
      transform: translateY(100px);
      opacity: 0;
      transition: all 0.3s ease;
    }
    
    .toast.show {
      transform: translateY(0);
      opacity: 1;
    }
    
    .toast.error {
      background: #ef4444;
    }
    
    .toast.warning {
      background: #f59e0b;
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
      .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
      }
      
      .main {
        margin-left: 0;
        padding: 1rem;
      }
      
      .header {
        padding: 1rem;
      }
      
      .header h1 {
        font-size: 1.5rem;
      }
      
      .form-grid {
        grid-template-columns: 1fr;
      }
      
      .settings-actions {
        flex-direction: column;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="sidebar">
      <div class="sidebar-header">
        <div class="sidebar-logo">
          <img src="../../icon/8304CEF178EEF908F89E309C65C1E521.jpg" alt="青云小铺">
        </div>
        <div>
          <div class="sidebar-title">青云小铺</div>
          <div class="sidebar-subtitle">管理后台</div>
        </div>
      </div>
      <nav class="sidebar-nav">
        <a href="admin_optimized.html" class="nav-item">📦 商品管理</a>
        <a href="orders_optimized.html" class="nav-item">📋 订单管理</a>
        <a href="analytics.html" class="nav-item">📊 数据统计</a>
        <a href="settings.html" class="nav-item active">⚙️ 系统设置</a>
      </nav>
    </div>

    <div class="main">
      <div class="header">
        <h1>系统设置</h1>
        <div class="user-info">
          <div class="user-avatar" id="user-avatar">A</div>
          <span id="user-name">管理员</span>
          <a href="#" class="logout-btn" onclick="logout()">退出登录</a>
        </div>
      </div>

      <div class="settings-grid">
        <!-- 基本设置 -->
        <div class="settings-card">
          <div class="settings-header">
            <div class="settings-icon">🏪</div>
            <h3 class="settings-title">商店基本信息</h3>
          </div>
          
          <form id="basic-settings-form">
            <div class="form-grid">
              <div class="form-group">
                <label for="store-name" class="form-label">商店名称</label>
                <input type="text" id="store-name" name="store_name" class="form-input" value="青云小铺" required>
              </div>
              <div class="form-group">
                <label for="store-url" class="form-label">商店网址</label>
                <input type="url" id="store-url" name="store_url" class="form-input" value="https://qingyun.shop">
              </div>
              <div class="form-group">
                <label for="contact-email" class="form-label">联系邮箱</label>
                <input type="email" id="contact-email" name="contact_email" class="form-input" value="<EMAIL>">
              </div>
              <div class="form-group">
                <label for="icp-number" class="form-label">备案号</label>
                <input type="text" id="icp-number" name="icp_number" class="form-input" value="粤ICP备2023114300号-1" readonly>
              </div>
            </div>
            <div class="form-group">
              <label for="store-description" class="form-label">商店描述</label>
              <textarea id="store-description" name="store_description" class="form-textarea" placeholder="请输入商店描述">青云小铺 - 专业的虚拟商品交易平台，提供安全可靠的自动发货服务。</textarea>
            </div>
            
            <div class="settings-actions">
              <button type="button" class="btn btn-outline" onclick="resetBasicSettings()">重置</button>
              <button type="submit" class="btn btn-primary">保存设置</button>
            </div>
          </form>
        </div>

        <!-- 支付设置 -->
        <div class="settings-card">
          <div class="settings-header">
            <div class="settings-icon">💳</div>
            <h3 class="settings-title">支付设置</h3>
          </div>
          
          <form id="payment-settings-form">
            <div class="form-grid">
              <div class="form-group">
                <label class="form-label">PayPal支付</label>
                <div class="form-switch">
                  <div class="switch active" id="paypal-switch" onclick="toggleSwitch('paypal-switch')"></div>
                  <span>启用PayPal支付</span>
                </div>
              </div>
              <div class="form-group">
                <label for="paypal-client-id" class="form-label">PayPal Client ID</label>
                <input type="text" id="paypal-client-id" name="paypal_client_id" class="form-input" placeholder="请输入PayPal Client ID">
              </div>
              <div class="form-group">
                <label for="paypal-secret" class="form-label">PayPal Secret</label>
                <input type="password" id="paypal-secret" name="paypal_secret" class="form-input" placeholder="请输入PayPal Secret">
              </div>
              <div class="form-group">
                <label for="paypal-mode" class="form-label">PayPal模式</label>
                <select id="paypal-mode" name="paypal_mode" class="form-select">
                  <option value="sandbox">沙盒模式</option>
                  <option value="live">生产模式</option>
                </select>
              </div>
            </div>
            
            <div class="settings-actions">
              <button type="button" class="btn btn-outline" onclick="testPayPal()">测试连接</button>
              <button type="submit" class="btn btn-primary">保存设置</button>
            </div>
          </form>
        </div>

        <!-- 邮件设置 -->
        <div class="settings-card">
          <div class="settings-header">
            <div class="settings-icon">📧</div>
            <h3 class="settings-title">邮件设置</h3>
          </div>
          
          <form id="email-settings-form">
            <div class="form-grid">
              <div class="form-group">
                <label class="form-label">邮件通知</label>
                <div class="form-switch">
                  <div class="switch active" id="email-switch" onclick="toggleSwitch('email-switch')"></div>
                  <span>启用邮件通知</span>
                </div>
              </div>
              <div class="form-group">
                <label for="smtp-host" class="form-label">SMTP服务器</label>
                <input type="text" id="smtp-host" name="smtp_host" class="form-input" placeholder="smtp.gmail.com">
              </div>
              <div class="form-group">
                <label for="smtp-port" class="form-label">SMTP端口</label>
                <input type="number" id="smtp-port" name="smtp_port" class="form-input" placeholder="587">
              </div>
              <div class="form-group">
                <label for="smtp-username" class="form-label">SMTP用户名</label>
                <input type="text" id="smtp-username" name="smtp_username" class="form-input" placeholder="<EMAIL>">
              </div>
              <div class="form-group">
                <label for="smtp-password" class="form-label">SMTP密码</label>
                <input type="password" id="smtp-password" name="smtp_password" class="form-input" placeholder="应用专用密码">
              </div>
              <div class="form-group">
                <label class="form-label">SSL/TLS</label>
                <div class="form-switch">
                  <div class="switch active" id="smtp-ssl-switch" onclick="toggleSwitch('smtp-ssl-switch')"></div>
                  <span>启用SSL/TLS</span>
                </div>
              </div>
            </div>
            
            <div class="settings-actions">
              <button type="button" class="btn btn-outline" onclick="testEmail()">发送测试邮件</button>
              <button type="submit" class="btn btn-primary">保存设置</button>
            </div>
          </form>
        </div>

        <!-- 安全设置 -->
        <div class="settings-card">
          <div class="settings-header">
            <div class="settings-icon">🔒</div>
            <h3 class="settings-title">安全设置</h3>
          </div>
          
          <form id="security-settings-form">
            <div class="form-grid">
              <div class="form-group">
                <label class="form-label">强制HTTPS</label>
                <div class="form-switch">
                  <div class="switch" id="https-switch" onclick="toggleSwitch('https-switch')"></div>
                  <span>强制使用HTTPS</span>
                </div>
              </div>
              <div class="form-group">
                <label class="form-label">API限流</label>
                <div class="form-switch">
                  <div class="switch active" id="rate-limit-switch" onclick="toggleSwitch('rate-limit-switch')"></div>
                  <span>启用API限流</span>
                </div>
              </div>
              <div class="form-group">
                <label for="session-timeout" class="form-label">会话超时(分钟)</label>
                <input type="number" id="session-timeout" name="session_timeout" class="form-input" value="30" min="5" max="1440">
              </div>
              <div class="form-group">
                <label for="max-login-attempts" class="form-label">最大登录尝试次数</label>
                <input type="number" id="max-login-attempts" name="max_login_attempts" class="form-input" value="5" min="3" max="10">
              </div>
            </div>
            
            <div class="settings-actions">
              <button type="button" class="btn btn-danger" onclick="clearSessions()">清除所有会话</button>
              <button type="submit" class="btn btn-primary">保存设置</button>
            </div>
          </form>
        </div>

        <!-- 系统维护 -->
        <div class="settings-card">
          <div class="settings-header">
            <div class="settings-icon">🛠️</div>
            <h3 class="settings-title">系统维护</h3>
          </div>
          
          <div class="form-grid">
            <div class="form-group">
              <label class="form-label">维护模式</label>
              <div class="form-switch">
                <div class="switch" id="maintenance-switch" onclick="toggleSwitch('maintenance-switch')"></div>
                <span>启用维护模式</span>
              </div>
            </div>
            <div class="form-group">
              <label for="maintenance-message" class="form-label">维护提示信息</label>
              <textarea id="maintenance-message" name="maintenance_message" class="form-textarea" placeholder="系统正在维护中，请稍后再试...">系统正在进行例行维护，预计30分钟后恢复正常。给您带来的不便，敬请谅解。</textarea>
            </div>
          </div>
          
          <div class="settings-actions">
            <button type="button" class="btn btn-outline" onclick="backupDatabase()">备份数据库</button>
            <button type="button" class="btn btn-outline" onclick="clearCache()">清除缓存</button>
            <button type="button" class="btn btn-primary" onclick="saveMaintenanceSettings()">保存设置</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 全局变量
    let currentUser = null;

    // Toast 通知
    function showToast(message, type = 'success') {
      const toast = document.createElement('div');
      toast.className = `toast ${type}`;
      toast.textContent = message;
      document.body.appendChild(toast);

      setTimeout(() => toast.classList.add('show'), 10);
      setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => toast.remove(), 300);
      }, 3000);
    }

    // 检查登录状态
    async function checkLoginStatus() {
      try {
        const adminUser = localStorage.getItem('admin_user');
        if (!adminUser) {
          throw new Error('未登录');
        }

        currentUser = JSON.parse(adminUser);
        if (!currentUser || !currentUser.is_admin) {
          throw new Error('权限不足');
        }

        // 更新用户信息显示
        document.getElementById('user-name').textContent = currentUser.username || '管理员';
        document.getElementById('user-avatar').textContent = (currentUser.username || 'A').charAt(0).toUpperCase();

      } catch (error) {
        console.error('登录检查失败:', error);
        showToast('请先登录管理员账户', 'error');
        setTimeout(() => {
          window.location.href = 'login_fixed.html';
        }, 2000);
      }
    }

    // 退出登录
    async function logout() {
      if (!confirm('确定要退出登录吗？')) {
        return;
      }

      try {
        localStorage.removeItem('admin_user');
        showToast('已退出登录', 'success');
        setTimeout(() => {
          window.location.href = 'login_fixed.html';
        }, 1000);
      } catch (error) {
        console.error('退出登录失败:', error);
        window.location.href = 'login_fixed.html';
      }
    }

    // 切换开关状态
    function toggleSwitch(switchId) {
      const switchElement = document.getElementById(switchId);
      switchElement.classList.toggle('active');
    }

    // 重置基本设置
    function resetBasicSettings() {
      if (!confirm('确定要重置基本设置吗？')) {
        return;
      }

      document.getElementById('store-name').value = '青云小铺';
      document.getElementById('store-url').value = 'https://qingyun.shop';
      document.getElementById('contact-email').value = '<EMAIL>';
      document.getElementById('store-description').value = '青云小铺 - 专业的虚拟商品交易平台，提供安全可靠的自动发货服务。';

      showToast('基本设置已重置', 'success');
    }

    // 保存基本设置
    async function saveBasicSettings(event) {
      event.preventDefault();

      const formData = new FormData(event.target);
      const settings = {
        store_name: formData.get('store_name'),
        store_url: formData.get('store_url'),
        contact_email: formData.get('contact_email'),
        icp_number: formData.get('icp_number'),
        store_description: formData.get('store_description')
      };

      try {
        // 这里应该调用后端API保存设置
        // const response = await fetch('/api/settings/basic', {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify(settings)
        // });

        // 模拟保存成功
        showToast('基本设置保存成功', 'success');
        console.log('保存的基本设置:', settings);

      } catch (error) {
        console.error('保存基本设置失败:', error);
        showToast('保存基本设置失败', 'error');
      }
    }

    // 保存支付设置
    async function savePaymentSettings(event) {
      event.preventDefault();

      const formData = new FormData(event.target);
      const settings = {
        paypal_enabled: document.getElementById('paypal-switch').classList.contains('active'),
        paypal_client_id: formData.get('paypal_client_id'),
        paypal_secret: formData.get('paypal_secret'),
        paypal_mode: formData.get('paypal_mode')
      };

      try {
        // 这里应该调用后端API保存设置
        showToast('支付设置保存成功', 'success');
        console.log('保存的支付设置:', settings);

      } catch (error) {
        console.error('保存支付设置失败:', error);
        showToast('保存支付设置失败', 'error');
      }
    }

    // 测试PayPal连接
    async function testPayPal() {
      const clientId = document.getElementById('paypal-client-id').value;
      const secret = document.getElementById('paypal-secret').value;

      if (!clientId || !secret) {
        showToast('请先填写PayPal配置信息', 'warning');
        return;
      }

      try {
        showToast('正在测试PayPal连接...', 'success');

        // 这里应该调用后端API测试PayPal连接
        // 模拟测试成功
        setTimeout(() => {
          showToast('PayPal连接测试成功', 'success');
        }, 2000);

      } catch (error) {
        console.error('PayPal连接测试失败:', error);
        showToast('PayPal连接测试失败', 'error');
      }
    }

    // 保存邮件设置
    async function saveEmailSettings(event) {
      event.preventDefault();

      const formData = new FormData(event.target);
      const settings = {
        email_enabled: document.getElementById('email-switch').classList.contains('active'),
        smtp_host: formData.get('smtp_host'),
        smtp_port: formData.get('smtp_port'),
        smtp_username: formData.get('smtp_username'),
        smtp_password: formData.get('smtp_password'),
        smtp_ssl: document.getElementById('smtp-ssl-switch').classList.contains('active')
      };

      try {
        showToast('邮件设置保存成功', 'success');
        console.log('保存的邮件设置:', settings);

      } catch (error) {
        console.error('保存邮件设置失败:', error);
        showToast('保存邮件设置失败', 'error');
      }
    }

    // 发送测试邮件
    async function testEmail() {
      const host = document.getElementById('smtp-host').value;
      const username = document.getElementById('smtp-username').value;

      if (!host || !username) {
        showToast('请先填写SMTP配置信息', 'warning');
        return;
      }

      try {
        showToast('正在发送测试邮件...', 'success');

        // 这里应该调用后端API发送测试邮件
        setTimeout(() => {
          showToast('测试邮件发送成功', 'success');
        }, 3000);

      } catch (error) {
        console.error('发送测试邮件失败:', error);
        showToast('发送测试邮件失败', 'error');
      }
    }

    // 保存安全设置
    async function saveSecuritySettings(event) {
      event.preventDefault();

      const formData = new FormData(event.target);
      const settings = {
        force_https: document.getElementById('https-switch').classList.contains('active'),
        rate_limit_enabled: document.getElementById('rate-limit-switch').classList.contains('active'),
        session_timeout: formData.get('session_timeout'),
        max_login_attempts: formData.get('max_login_attempts')
      };

      try {
        showToast('安全设置保存成功', 'success');
        console.log('保存的安全设置:', settings);

      } catch (error) {
        console.error('保存安全设置失败:', error);
        showToast('保存安全设置失败', 'error');
      }
    }

    // 清除所有会话
    async function clearSessions() {
      if (!confirm('确定要清除所有用户会话吗？这将强制所有用户重新登录。')) {
        return;
      }

      try {
        showToast('正在清除所有会话...', 'success');

        // 这里应该调用后端API清除会话
        setTimeout(() => {
          showToast('所有会话已清除', 'success');
        }, 2000);

      } catch (error) {
        console.error('清除会话失败:', error);
        showToast('清除会话失败', 'error');
      }
    }

    // 保存维护设置
    async function saveMaintenanceSettings() {
      const maintenanceEnabled = document.getElementById('maintenance-switch').classList.contains('active');
      const maintenanceMessage = document.getElementById('maintenance-message').value;

      const settings = {
        maintenance_enabled: maintenanceEnabled,
        maintenance_message: maintenanceMessage
      };

      try {
        showToast('维护设置保存成功', 'success');
        console.log('保存的维护设置:', settings);

        if (maintenanceEnabled) {
          showToast('维护模式已启用，前台用户将看到维护提示', 'warning');
        }

      } catch (error) {
        console.error('保存维护设置失败:', error);
        showToast('保存维护设置失败', 'error');
      }
    }

    // 备份数据库
    async function backupDatabase() {
      if (!confirm('确定要备份数据库吗？')) {
        return;
      }

      try {
        showToast('正在备份数据库...', 'success');

        // 这里应该调用后端API备份数据库
        setTimeout(() => {
          showToast('数据库备份完成', 'success');
        }, 3000);

      } catch (error) {
        console.error('数据库备份失败:', error);
        showToast('数据库备份失败', 'error');
      }
    }

    // 清除缓存
    async function clearCache() {
      if (!confirm('确定要清除系统缓存吗？')) {
        return;
      }

      try {
        showToast('正在清除缓存...', 'success');

        // 这里应该调用后端API清除缓存
        setTimeout(() => {
          showToast('系统缓存已清除', 'success');
        }, 2000);

      } catch (error) {
        console.error('清除缓存失败:', error);
        showToast('清除缓存失败', 'error');
      }
    }

    // 页面初始化
    document.addEventListener('DOMContentLoaded', async () => {
      console.log('系统设置页面加载完成');

      // 检查登录状态
      await checkLoginStatus();

      // 绑定表单提交事件
      document.getElementById('basic-settings-form').addEventListener('submit', saveBasicSettings);
      document.getElementById('payment-settings-form').addEventListener('submit', savePaymentSettings);
      document.getElementById('email-settings-form').addEventListener('submit', saveEmailSettings);
      document.getElementById('security-settings-form').addEventListener('submit', saveSecuritySettings);
    });
  </script>
</body>
</html>
