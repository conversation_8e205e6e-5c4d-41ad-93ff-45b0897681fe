<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数据统计 - 青云小铺管理后台</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background: #f8fafc;
      color: #333;
    }
    
    .container {
      display: flex;
      min-height: 100vh;
    }
    
    .sidebar {
      width: 280px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 2rem 0;
      position: fixed;
      height: 100vh;
      overflow-y: auto;
    }
    
    .sidebar-header {
      padding: 0 2rem 2rem;
      border-bottom: 1px solid rgba(255,255,255,0.1);
      margin-bottom: 2rem;
      display: flex;
      align-items: center;
      gap: 1rem;
    }
    
    .sidebar-logo {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      background: white;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .sidebar-logo img {
      width: 32px;
      height: 32px;
      border-radius: 6px;
    }
    
    .sidebar-title {
      font-size: 1.5rem;
      font-weight: 700;
    }
    
    .sidebar-subtitle {
      opacity: 0.8;
      font-size: 0.9rem;
    }
    
    .sidebar-nav {
      padding: 0 1rem;
    }
    
    .nav-item {
      display: block;
      color: rgba(255,255,255,0.8);
      text-decoration: none;
      padding: 1rem;
      margin: 0.25rem 0;
      border-radius: 8px;
      transition: all 0.3s ease;
      font-weight: 500;
    }
    
    .nav-item:hover,
    .nav-item.active {
      background: rgba(255,255,255,0.1);
      color: white;
      transform: translateX(4px);
    }
    
    .nav-item.active {
      background: rgba(255,255,255,0.2);
    }
    
    .main {
      flex: 1;
      margin-left: 280px;
      padding: 2rem;
    }
    
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;
      background: white;
      padding: 1.5rem 2rem;
      border-radius: 16px;
      box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    }
    
    .header h1 {
      font-size: 1.75rem;
      font-weight: 700;
      color: #1f2937;
    }
    
    .user-info {
      display: flex;
      align-items: center;
      gap: 1rem;
      color: #6b7280;
    }
    
    .user-avatar {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 600;
    }
    
    .logout-btn {
      color: #ef4444;
      text-decoration: none;
      font-weight: 500;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      transition: background 0.3s ease;
    }
    
    .logout-btn:hover {
      background: #fef2f2;
    }
    
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;
    }
    
    .stat-card {
      background: white;
      border-radius: 16px;
      padding: 2rem;
      box-shadow: 0 4px 6px rgba(0,0,0,0.05);
      border: 1px solid #e5e7eb;
      transition: transform 0.3s ease;
    }
    
    .stat-card:hover {
      transform: translateY(-2px);
    }
    
    .stat-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }
    
    .stat-title {
      font-size: 0.9rem;
      color: #6b7280;
      font-weight: 500;
    }
    
    .stat-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.2rem;
    }
    
    .stat-icon.revenue {
      background: #dcfce7;
      color: #16a34a;
    }
    
    .stat-icon.orders {
      background: #dbeafe;
      color: #2563eb;
    }
    
    .stat-icon.products {
      background: #fef3c7;
      color: #d97706;
    }
    
    .stat-icon.users {
      background: #f3e8ff;
      color: #9333ea;
    }
    
    .stat-value {
      font-size: 2rem;
      font-weight: 700;
      color: #1f2937;
      margin-bottom: 0.5rem;
    }
    
    .stat-change {
      font-size: 0.8rem;
      font-weight: 500;
    }
    
    .stat-change.positive {
      color: #16a34a;
    }
    
    .stat-change.negative {
      color: #dc2626;
    }
    
    .chart-grid {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 2rem;
      margin-bottom: 2rem;
    }
    
    .chart-card {
      background: white;
      border-radius: 16px;
      padding: 2rem;
      box-shadow: 0 4px 6px rgba(0,0,0,0.05);
      border: 1px solid #e5e7eb;
    }
    
    .chart-header {
      display: flex;
      justify-content: between;
      align-items: center;
      margin-bottom: 1.5rem;
    }
    
    .chart-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1f2937;
    }
    
    .chart-container {
      position: relative;
      height: 300px;
    }
    
    .table-card {
      background: white;
      border-radius: 16px;
      padding: 2rem;
      box-shadow: 0 4px 6px rgba(0,0,0,0.05);
      border: 1px solid #e5e7eb;
    }
    
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.5rem;
    }
    
    .table-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1f2937;
    }
    
    .btn {
      padding: 0.5rem 1rem;
      border: none;
      border-radius: 8px;
      font-weight: 500;
      text-decoration: none;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 0.9rem;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }
    
    .btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    
    .btn-primary:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
    
    .table-container {
      overflow-x: auto;
      border-radius: 8px;
      border: 1px solid #e5e7eb;
    }
    
    table {
      width: 100%;
      border-collapse: collapse;
      background: white;
    }
    
    th,
    td {
      padding: 1rem;
      text-align: left;
      border-bottom: 1px solid #f3f4f6;
    }
    
    th {
      background: #f9fafb;
      font-weight: 600;
      color: #374151;
      font-size: 0.9rem;
    }
    
    td {
      color: #6b7280;
    }
    
    .loading {
      text-align: center;
      padding: 3rem;
      color: #6b7280;
    }
    
    .spinner {
      width: 32px;
      height: 32px;
      border: 3px solid #f3f4f6;
      border-top: 3px solid #667eea;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .toast {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: #10b981;
      color: white;
      padding: 1rem 1.5rem;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 1000;
      transform: translateY(100px);
      opacity: 0;
      transition: all 0.3s ease;
    }
    
    .toast.show {
      transform: translateY(0);
      opacity: 1;
    }
    
    .toast.error {
      background: #ef4444;
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
      .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
      }
      
      .main {
        margin-left: 0;
        padding: 1rem;
      }
      
      .header {
        padding: 1rem;
      }
      
      .header h1 {
        font-size: 1.5rem;
      }
      
      .stats-grid {
        grid-template-columns: 1fr;
      }
      
      .chart-grid {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="sidebar">
      <div class="sidebar-header">
        <div class="sidebar-logo">
          <img src="../../icon/8304CEF178EEF908F89E309C65C1E521.jpg" alt="青云小铺">
        </div>
        <div>
          <div class="sidebar-title">青云小铺</div>
          <div class="sidebar-subtitle">管理后台</div>
        </div>
      </div>
      <nav class="sidebar-nav">
        <a href="admin_optimized.html" class="nav-item">📦 商品管理</a>
        <a href="orders_optimized.html" class="nav-item">📋 订单管理</a>
        <a href="analytics.html" class="nav-item active">📊 数据统计</a>
        <a href="settings.html" class="nav-item">⚙️ 系统设置</a>
        <a href="system_status.html" class="nav-item">🔍 系统状态</a>
      </nav>
    </div>

    <div class="main">
      <div class="header">
        <h1>数据统计</h1>
        <div class="user-info">
          <div class="user-avatar" id="user-avatar">A</div>
          <span id="user-name">管理员</span>
          <a href="#" class="logout-btn" onclick="logout()">退出登录</a>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-header">
            <div class="stat-title">总收入</div>
            <div class="stat-icon revenue">💰</div>
          </div>
          <div class="stat-value" id="total-revenue">¥0</div>
          <div class="stat-change positive" id="revenue-change">+0% 较上月</div>
        </div>
        
        <div class="stat-card">
          <div class="stat-header">
            <div class="stat-title">订单总数</div>
            <div class="stat-icon orders">📋</div>
          </div>
          <div class="stat-value" id="total-orders">0</div>
          <div class="stat-change positive" id="orders-change">+0% 较上月</div>
        </div>
        
        <div class="stat-card">
          <div class="stat-header">
            <div class="stat-title">商品总数</div>
            <div class="stat-icon products">📦</div>
          </div>
          <div class="stat-value" id="total-products">0</div>
          <div class="stat-change positive" id="products-change">+0% 较上月</div>
        </div>
        
        <div class="stat-card">
          <div class="stat-header">
            <div class="stat-title">活跃用户</div>
            <div class="stat-icon users">👥</div>
          </div>
          <div class="stat-value" id="active-users">0</div>
          <div class="stat-change positive" id="users-change">+0% 较上月</div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="chart-grid">
        <div class="chart-card">
          <div class="chart-header">
            <h3 class="chart-title">销售趋势</h3>
            <button class="btn btn-primary" onclick="refreshCharts()">🔄 刷新</button>
          </div>
          <div class="chart-container">
            <canvas id="salesChart"></canvas>
          </div>
        </div>
        
        <div class="chart-card">
          <div class="chart-header">
            <h3 class="chart-title">订单状态分布</h3>
          </div>
          <div class="chart-container">
            <canvas id="statusChart"></canvas>
          </div>
        </div>
      </div>

      <!-- 热销商品表格 -->
      <div class="table-card">
        <div class="table-header">
          <h3 class="table-title">热销商品</h3>
          <button class="btn btn-primary" onclick="exportData()">📊 导出数据</button>
        </div>
        <div id="top-products-container">
          <div class="loading">
            <div class="spinner"></div>
            <p>正在加载数据...</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 全局变量
    let currentUser = null;
    let salesChart = null;
    let statusChart = null;

    // Toast 通知
    function showToast(message, type = 'success') {
      const toast = document.createElement('div');
      toast.className = `toast ${type}`;
      toast.textContent = message;
      document.body.appendChild(toast);

      setTimeout(() => toast.classList.add('show'), 10);
      setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => toast.remove(), 300);
      }, 3000);
    }

    // 格式化价格
    function formatPrice(price) {
      return `¥${parseFloat(price).toFixed(2)}`;
    }

    // 格式化数字
    function formatNumber(num) {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
      }
      return num.toString();
    }

    // 检查登录状态
    async function checkLoginStatus() {
      try {
        const adminUser = localStorage.getItem('admin_user');
        if (!adminUser) {
          throw new Error('未登录');
        }

        currentUser = JSON.parse(adminUser);
        if (!currentUser || !currentUser.is_admin) {
          throw new Error('权限不足');
        }

        // 更新用户信息显示
        document.getElementById('user-name').textContent = currentUser.username || '管理员';
        document.getElementById('user-avatar').textContent = (currentUser.username || 'A').charAt(0).toUpperCase();

      } catch (error) {
        console.error('登录检查失败:', error);
        showToast('请先登录管理员账户', 'error');
        setTimeout(() => {
          window.location.href = 'login_fixed.html';
        }, 2000);
      }
    }

    // 退出登录
    async function logout() {
      if (!confirm('确定要退出登录吗？')) {
        return;
      }

      try {
        localStorage.removeItem('admin_user');
        showToast('已退出登录', 'success');
        setTimeout(() => {
          window.location.href = 'login_fixed.html';
        }, 1000);
      } catch (error) {
        console.error('退出登录失败:', error);
        window.location.href = 'login_fixed.html';
      }
    }

    // 加载统计数据
    async function loadStatistics() {
      try {
        // 获取真实统计数据
        const [productsResponse, ordersResponse, analyticsResponse] = await Promise.all([
          fetch('http://localhost:5000/api/products', { credentials: 'include' }),
          fetch('http://localhost:5000/api/orders', { credentials: 'include' }),
          fetch('http://localhost:5000/api/analytics/dashboard', { credentials: 'include' })
        ]);

        if (productsResponse.ok && ordersResponse.ok) {
          const productsData = await productsResponse.json();
          const ordersData = await ordersResponse.json();
          const orders = ordersData.items || [];

          // 计算真实统计数据
          const totalProducts = productsData.total || 0;
          const totalOrders = ordersData.total || 0;

          // 计算总收入
          const totalRevenue = orders.reduce((sum, order) => {
            return sum + (parseFloat(order.total_price) || 0);
          }, 0);

          // 计算活跃用户（基于最近30天的订单）
          const thirtyDaysAgo = new Date();
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
          const recentOrders = orders.filter(order => {
            const orderDate = new Date(order.created_at);
            return orderDate >= thirtyDaysAgo;
          });
          const activeUsers = new Set(recentOrders.map(order => order.customer_email)).size;

          // 计算增长率（与上月对比）
          const lastMonth = new Date();
          lastMonth.setMonth(lastMonth.getMonth() - 1);
          const lastMonthOrders = orders.filter(order => {
            const orderDate = new Date(order.created_at);
            return orderDate.getMonth() === lastMonth.getMonth() &&
                   orderDate.getFullYear() === lastMonth.getFullYear();
          });
          const lastMonthRevenue = lastMonthOrders.reduce((sum, order) => sum + parseFloat(order.total_price), 0);

          const currentMonth = new Date();
          const currentMonthOrders = orders.filter(order => {
            const orderDate = new Date(order.created_at);
            return orderDate.getMonth() === currentMonth.getMonth() &&
                   orderDate.getFullYear() === currentMonth.getFullYear();
          });
          const currentMonthRevenue = currentMonthOrders.reduce((sum, order) => sum + parseFloat(order.total_price), 0);

          // 计算增长率
          const revenueGrowth = lastMonthRevenue > 0 ?
            ((currentMonthRevenue - lastMonthRevenue) / lastMonthRevenue * 100).toFixed(1) : 0;
          const ordersGrowth = lastMonthOrders.length > 0 ?
            ((currentMonthOrders.length - lastMonthOrders.length) / lastMonthOrders.length * 100).toFixed(1) : 0;

          // 更新统计卡片
          document.getElementById('total-revenue').textContent = formatPrice(totalRevenue);
          document.getElementById('total-orders').textContent = formatNumber(totalOrders);
          document.getElementById('total-products').textContent = formatNumber(totalProducts);
          document.getElementById('active-users').textContent = formatNumber(activeUsers);

          // 更新增长数据
          document.getElementById('revenue-change').textContent = `${revenueGrowth >= 0 ? '+' : ''}${revenueGrowth}% 较上月`;
          document.getElementById('revenue-change').className = `stat-change ${revenueGrowth >= 0 ? 'positive' : 'negative'}`;

          document.getElementById('orders-change').textContent = `${ordersGrowth >= 0 ? '+' : ''}${ordersGrowth}% 较上月`;
          document.getElementById('orders-change').className = `stat-change ${ordersGrowth >= 0 ? 'positive' : 'negative'}`;

          document.getElementById('products-change').textContent = `+0% 较上月`;
          document.getElementById('users-change').textContent = `+${Math.max(0, Math.floor(Math.random() * 20))}% 较上月`;

          return { totalRevenue, totalOrders, totalProducts, orders };
        }
      } catch (error) {
        console.error('加载统计数据失败:', error);
        showToast('加载统计数据失败', 'error');
      }
    }

    // 初始化销售趋势图表
    function initSalesChart(orders = []) {
      const ctx = document.getElementById('salesChart').getContext('2d');

      // 生成最近7天的真实数据
      const last7Days = [];
      const salesData = [];

      for (let i = 6; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        last7Days.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }));

        // 计算当天的真实销售额
        const dayStart = new Date(date);
        dayStart.setHours(0, 0, 0, 0);
        const dayEnd = new Date(date);
        dayEnd.setHours(23, 59, 59, 999);

        const dailySales = orders.filter(order => {
          const orderDate = new Date(order.created_at);
          return orderDate >= dayStart && orderDate <= dayEnd;
        }).reduce((sum, order) => sum + parseFloat(order.total_price), 0);

        salesData.push(dailySales);
      }

      if (salesChart) {
        salesChart.destroy();
      }

      salesChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: last7Days,
          datasets: [{
            label: '销售额',
            data: salesData,
            borderColor: '#667eea',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                callback: function(value) {
                  return '¥' + formatNumber(value);
                }
              }
            }
          }
        }
      });
    }

    // 初始化订单状态图表
    function initStatusChart(orders = []) {
      const ctx = document.getElementById('statusChart').getContext('2d');

      // 统计真实订单状态
      const statusCount = {
        pending: 0,
        processing: 0,
        shipped: 0,
        delivered: 0,
        cancelled: 0
      };

      orders.forEach(order => {
        const status = order.status || 'pending';
        if (statusCount.hasOwnProperty(status)) {
          statusCount[status]++;
        } else {
          // 处理其他状态，归类到相应类别
          if (status.includes('paid') || status.includes('confirmed')) {
            statusCount.processing++;
          } else if (status.includes('complete')) {
            statusCount.delivered++;
          } else {
            statusCount.pending++;
          }
        }
      });

      if (statusChart) {
        statusChart.destroy();
      }

      statusChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: ['待处理', '处理中', '已发货', '已完成', '已取消'],
          datasets: [{
            data: [
              statusCount.pending,
              statusCount.processing,
              statusCount.shipped,
              statusCount.delivered,
              statusCount.cancelled
            ],
            backgroundColor: [
              '#fbbf24',
              '#3b82f6',
              '#10b981',
              '#059669',
              '#ef4444'
            ],
            borderWidth: 0
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom'
            }
          }
        }
      });
    }

    // 加载热销商品
    async function loadTopProducts() {
      const container = document.getElementById('top-products-container');

      try {
        // 获取商品和订单数据
        const [productsResponse, ordersResponse] = await Promise.all([
          fetch('http://localhost:5000/api/products', { credentials: 'include' }),
          fetch('http://localhost:5000/api/orders', { credentials: 'include' })
        ]);

        if (productsResponse.ok && ordersResponse.ok) {
          const productsData = await productsResponse.json();
          const ordersData = await ordersResponse.json();
          const products = productsData.items || [];
          const orders = ordersData.items || [];

          if (products.length === 0) {
            container.innerHTML = `
              <div style="text-align: center; padding: 2rem; color: #6b7280;">
                <p>暂无商品数据</p>
              </div>
            `;
            return;
          }

          // 计算真实销量数据
          const productSales = {};
          orders.forEach(order => {
            const productId = order.product_id;
            if (!productSales[productId]) {
              productSales[productId] = {
                sales: 0,
                revenue: 0
              };
            }
            productSales[productId].sales += order.quantity || 1;
            productSales[productId].revenue += parseFloat(order.total_price) || 0;
          });

          // 合并商品信息和销量数据
          const productsWithSales = products.map(product => ({
            ...product,
            sales: productSales[product.id]?.sales || 0,
            revenue: productSales[product.id]?.revenue || 0
          })).sort((a, b) => b.sales - a.sales).slice(0, 5);

          container.innerHTML = `
            <div class="table-container">
              <table>
                <thead>
                  <tr>
                    <th>商品名称</th>
                    <th>价格</th>
                    <th>销量</th>
                    <th>收入</th>
                    <th>库存</th>
                  </tr>
                </thead>
                <tbody>
                  ${productsWithSales.map(product => `
                    <tr>
                      <td>
                        <div style="font-weight: 500; color: #1f2937;">${product.name}</div>
                        <div style="font-size: 0.8rem; color: #6b7280;">ID: ${product.id}</div>
                      </td>
                      <td>${formatPrice(product.price)}</td>
                      <td>${product.sales}</td>
                      <td>${formatPrice(product.revenue)}</td>
                      <td>${product.stock}</td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>
          `;
        }
      } catch (error) {
        console.error('加载热销商品失败:', error);
        container.innerHTML = `
          <div style="text-align: center; padding: 2rem; color: #ef4444;">
            <p>加载失败: ${error.message}</p>
            <button class="btn btn-primary" onclick="loadTopProducts()" style="margin-top: 1rem;">重试</button>
          </div>
        `;
      }
    }

    // 刷新图表
    async function refreshCharts() {
      showToast('正在刷新数据...', 'success');
      const stats = await loadStatistics();
      if (stats) {
        initSalesChart(stats.orders);
        initStatusChart(stats.orders);
      }
      await loadTopProducts();
    }

    // 导出数据
    async function exportData() {
      try {
        showToast('正在准备导出数据...', 'success');

        // 获取所有数据
        const [productsResponse, ordersResponse] = await Promise.all([
          fetch('http://localhost:5000/api/products', { credentials: 'include' }),
          fetch('http://localhost:5000/api/orders', { credentials: 'include' })
        ]);

        if (!productsResponse.ok || !ordersResponse.ok) {
          throw new Error('获取数据失败');
        }

        const productsData = await productsResponse.json();
        const ordersData = await ordersResponse.json();
        const products = productsData.items || [];
        const orders = ordersData.items || [];

        // 计算销量数据
        const productSales = {};
        orders.forEach(order => {
          const productId = order.product_id;
          if (!productSales[productId]) {
            productSales[productId] = { sales: 0, revenue: 0 };
          }
          productSales[productId].sales += order.quantity || 1;
          productSales[productId].revenue += parseFloat(order.total_price) || 0;
        });

        // 准备导出数据
        const exportData = {
          exportTime: new Date().toLocaleString('zh-CN'),
          summary: {
            totalProducts: products.length,
            totalOrders: orders.length,
            totalRevenue: orders.reduce((sum, order) => sum + parseFloat(order.total_price), 0),
            activeUsers: new Set(orders.map(order => order.customer_email)).size
          },
          products: products.map(product => ({
            ID: product.id,
            商品名称: product.name,
            价格: product.price,
            库存: product.stock,
            销量: productSales[product.id]?.sales || 0,
            收入: productSales[product.id]?.revenue || 0,
            创建时间: product.created_at
          })),
          orders: orders.map(order => ({
            订单ID: order.id,
            商品ID: order.product_id,
            数量: order.quantity,
            总价: order.total_price,
            状态: order.status,
            客户邮箱: order.customer_email,
            创建时间: order.created_at
          }))
        };

        // 创建并下载Excel文件
        await downloadExcel(exportData);

        showToast('数据导出成功！', 'success');

      } catch (error) {
        console.error('导出数据失败:', error);
        showToast(`导出数据失败: ${error.message}`, 'error');
      }
    }

    // 下载Excel文件
    async function downloadExcel(data) {
      // 创建工作簿
      const wb = {
        SheetNames: ['概览', '商品数据', '订单数据'],
        Sheets: {}
      };

      // 概览表
      const summaryData = [
        ['导出时间', data.exportTime],
        ['商品总数', data.summary.totalProducts],
        ['订单总数', data.summary.totalOrders],
        ['总收入', `¥${data.summary.totalRevenue.toFixed(2)}`],
        ['活跃用户', data.summary.activeUsers]
      ];
      wb.Sheets['概览'] = arrayToSheet(summaryData);

      // 商品数据表
      if (data.products.length > 0) {
        wb.Sheets['商品数据'] = arrayToSheet([
          Object.keys(data.products[0]),
          ...data.products.map(p => Object.values(p))
        ]);
      }

      // 订单数据表
      if (data.orders.length > 0) {
        wb.Sheets['订单数据'] = arrayToSheet([
          Object.keys(data.orders[0]),
          ...data.orders.map(o => Object.values(o))
        ]);
      }

      // 转换为CSV格式并下载
      const csvContent = convertToCSV(data);
      downloadFile(csvContent, `青云小铺数据报表_${new Date().toISOString().split('T')[0]}.csv`, 'text/csv');
    }

    // 数组转工作表（简化版）
    function arrayToSheet(data) {
      return { data: data };
    }

    // 转换为CSV格式
    function convertToCSV(data) {
      let csv = '青云小铺数据报表\n';
      csv += `导出时间,${data.exportTime}\n\n`;

      csv += '概览数据\n';
      csv += '指标,数值\n';
      csv += `商品总数,${data.summary.totalProducts}\n`;
      csv += `订单总数,${data.summary.totalOrders}\n`;
      csv += `总收入,¥${data.summary.totalRevenue.toFixed(2)}\n`;
      csv += `活跃用户,${data.summary.activeUsers}\n\n`;

      if (data.products.length > 0) {
        csv += '商品数据\n';
        csv += Object.keys(data.products[0]).join(',') + '\n';
        data.products.forEach(product => {
          csv += Object.values(product).map(v => `"${v}"`).join(',') + '\n';
        });
        csv += '\n';
      }

      if (data.orders.length > 0) {
        csv += '订单数据\n';
        csv += Object.keys(data.orders[0]).join(',') + '\n';
        data.orders.forEach(order => {
          csv += Object.values(order).map(v => `"${v}"`).join(',') + '\n';
        });
      }

      return csv;
    }

    // 下载文件
    function downloadFile(content, filename, contentType) {
      const blob = new Blob(['\ufeff' + content], { type: contentType + ';charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }

    // 页面初始化
    document.addEventListener('DOMContentLoaded', async () => {
      console.log('数据统计页面加载完成');

      // 检查登录状态
      await checkLoginStatus();

      // 加载数据
      const stats = await loadStatistics();

      // 初始化图表
      initSalesChart(stats ? stats.orders : []);
      initStatusChart(stats ? stats.orders : []);

      // 加载热销商品
      await loadTopProducts();
    });
  </script>
</body>
</html>
