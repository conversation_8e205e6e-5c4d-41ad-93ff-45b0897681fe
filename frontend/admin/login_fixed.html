<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>管理员登录 - 发卡商城</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #333;
    }
    
    .login-container {
      width: 100%;
      max-width: 450px;
      padding: 2rem;
    }
    
    .login-card {
      background: white;
      border-radius: 20px;
      padding: 3rem 2.5rem;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      backdrop-filter: blur(10px);
    }
    
    .login-header {
      text-align: center;
      margin-bottom: 2.5rem;
    }
    
    .login-logo {
      font-size: 3rem;
      margin-bottom: 1rem;
    }
    
    .login-title {
      font-size: 1.75rem;
      font-weight: 700;
      color: #333;
      margin-bottom: 0.5rem;
    }
    
    .login-subtitle {
      color: #666;
      font-size: 1rem;
    }
    
    .form-group {
      margin-bottom: 1.5rem;
    }
    
    .form-label {
      display: block;
      margin-bottom: 0.75rem;
      color: #333;
      font-weight: 500;
      font-size: 0.95rem;
    }
    
    .form-input {
      width: 100%;
      padding: 1rem 1.25rem;
      border: 2px solid #e5e7eb;
      border-radius: 12px;
      font-size: 1rem;
      transition: all 0.3s ease;
      background: #f9fafb;
    }
    
    .form-input:focus {
      outline: none;
      border-color: #667eea;
      background: white;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .form-input::placeholder {
      color: #9ca3af;
    }
    
    .btn {
      width: 100%;
      padding: 1rem 1.5rem;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      border-radius: 12px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-top: 1rem;
    }
    
    .btn:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }
    
    .btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
    
    .btn-loading {
      position: relative;
      color: transparent;
    }
    
    .btn-loading::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 20px;
      height: 20px;
      border: 2px solid transparent;
      border-top: 2px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: translate(-50%, -50%) rotate(0deg); }
      100% { transform: translate(-50%, -50%) rotate(360deg); }
    }
    
    .error-message {
      background: #fef2f2;
      color: #dc2626;
      padding: 1rem;
      border-radius: 8px;
      margin-top: 1rem;
      border: 1px solid #fecaca;
      font-size: 0.9rem;
      text-align: center;
    }
    
    .success-message {
      background: #f0fdf4;
      color: #16a34a;
      padding: 1rem;
      border-radius: 8px;
      margin-top: 1rem;
      border: 1px solid #bbf7d0;
      font-size: 0.9rem;
      text-align: center;
    }
    
    .back-link {
      text-align: center;
      margin-top: 2rem;
      padding-top: 1.5rem;
      border-top: 1px solid #e5e7eb;
    }
    
    .back-link a {
      color: #667eea;
      text-decoration: none;
      font-weight: 500;
      transition: color 0.3s ease;
    }
    
    .back-link a:hover {
      color: #5a67d8;
    }
    
    .demo-info {
      background: #eff6ff;
      border: 1px solid #dbeafe;
      border-radius: 8px;
      padding: 1rem;
      margin-bottom: 1.5rem;
      font-size: 0.9rem;
      color: #1e40af;
    }
    
    .demo-info strong {
      display: block;
      margin-bottom: 0.5rem;
    }
    
    .debug-info {
      background: #f3f4f6;
      border: 1px solid #d1d5db;
      border-radius: 8px;
      padding: 1rem;
      margin-top: 1rem;
      font-size: 0.8rem;
      color: #374151;
      font-family: monospace;
    }
    
    /* 响应式设计 */
    @media (max-width: 480px) {
      .login-container {
        padding: 1rem;
      }
      
      .login-card {
        padding: 2rem 1.5rem;
      }
      
      .login-title {
        font-size: 1.5rem;
      }
      
      .login-logo {
        font-size: 2.5rem;
      }
    }
  </style>
</head>
<body>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <div class="login-logo">🛒</div>
        <h1 class="login-title">管理员登录</h1>
        <p class="login-subtitle">欢迎回来，请登录您的管理员账户</p>
      </div>
      
      <div class="demo-info">
        <strong>演示账户信息:</strong>
        用户名: admin<br>
        密码: admin123
      </div>
      
      <form id="login-form">
        <div class="form-group">
          <label for="username" class="form-label">用户名</label>
          <input 
            type="text" 
            id="username" 
            name="username" 
            class="form-input" 
            placeholder="请输入用户名"
            value="admin"
            required
          >
        </div>
        
        <div class="form-group">
          <label for="password" class="form-label">密码</label>
          <input 
            type="password" 
            id="password" 
            name="password" 
            class="form-input" 
            placeholder="请输入密码"
            value="admin123"
            required
          >
        </div>
        
        <button type="submit" class="btn" id="login-btn">
          登录
        </button>
        
        <div id="message-container"></div>
        
        <div class="debug-info" id="debug-info" style="display: none;">
          <strong>调试信息:</strong><br>
          <span id="debug-content"></span>
        </div>
      </form>
      
      <div class="back-link">
        <a href="../index_standalone.html">← 返回首页</a>
      </div>
    </div>
  </div>

  <script>
    // 调试模式
    const DEBUG = true;
    
    // 调试日志
    function debugLog(message) {
      if (DEBUG) {
        console.log('[DEBUG]', message);
        const debugInfo = document.getElementById('debug-info');
        const debugContent = document.getElementById('debug-content');
        debugContent.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
        debugInfo.style.display = 'block';
      }
    }

    // 显示消息
    function showMessage(message, type = 'error') {
      const container = document.getElementById('message-container');
      container.innerHTML = `
        <div class="${type}-message">
          ${message}
        </div>
      `;
    }

    // 清除消息
    function clearMessage() {
      document.getElementById('message-container').innerHTML = '';
    }

    // 设置按钮加载状态
    function setButtonLoading(loading) {
      const btn = document.getElementById('login-btn');
      if (loading) {
        btn.disabled = true;
        btn.classList.add('btn-loading');
        btn.textContent = '';
      } else {
        btn.disabled = false;
        btn.classList.remove('btn-loading');
        btn.textContent = '登录';
      }
    }

    // 处理登录 - 使用XMLHttpRequest作为备选方案
    async function handleLoginWithXHR(loginData) {
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open('POST', 'http://localhost:5000/api/auth/login', true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.withCredentials = true;
        
        xhr.onreadystatechange = function() {
          if (xhr.readyState === 4) {
            debugLog(`XHR状态: ${xhr.status}, 响应: ${xhr.responseText}`);
            
            if (xhr.status === 200) {
              try {
                const data = JSON.parse(xhr.responseText);
                resolve(data);
              } catch (e) {
                reject(new Error('响应解析失败'));
              }
            } else {
              reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
            }
          }
        };
        
        xhr.onerror = function() {
          debugLog('XHR网络错误');
          reject(new Error('网络连接失败'));
        };
        
        xhr.ontimeout = function() {
          debugLog('XHR超时');
          reject(new Error('请求超时'));
        };
        
        xhr.timeout = 10000; // 10秒超时
        
        try {
          xhr.send(JSON.stringify(loginData));
          debugLog('XHR请求已发送');
        } catch (e) {
          debugLog(`XHR发送失败: ${e.message}`);
          reject(e);
        }
      });
    }

    // 处理登录
    async function handleLogin(event) {
      event.preventDefault();
      
      const formData = new FormData(event.target);
      const loginData = {
        username: formData.get('username').trim(),
        password: formData.get('password').trim()
      };
      
      debugLog(`开始登录: ${loginData.username}`);
      
      // 验证输入
      if (!loginData.username || !loginData.password) {
        showMessage('请输入用户名和密码');
        return;
      }
      
      clearMessage();
      setButtonLoading(true);
      
      try {
        debugLog('尝试使用Fetch API');
        
        // 首先尝试使用fetch
        let result;
        try {
          const response = await fetch('http://localhost:5000/api/auth/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify(loginData)
          });
          
          debugLog(`Fetch响应状态: ${response.status}`);
          
          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
          }
          
          result = await response.json();
          debugLog('Fetch登录成功');
          
        } catch (fetchError) {
          debugLog(`Fetch失败: ${fetchError.message}`);
          debugLog('尝试使用XMLHttpRequest');
          
          // 如果fetch失败，尝试XMLHttpRequest
          result = await handleLoginWithXHR(loginData);
        }
        
        debugLog(`登录成功: ${JSON.stringify(result)}`);
        
        showMessage('登录成功，正在跳转...', 'success');
        
        // 保存登录状态
        if (result.user) {
          localStorage.setItem('admin_user', JSON.stringify(result.user));
          debugLog('用户信息已保存到localStorage');
        }
        
        // 延迟跳转，让用户看到成功消息
        setTimeout(() => {
          window.location.href = 'admin_optimized.html';
        }, 1500);
        
      } catch (error) {
        debugLog(`登录失败: ${error.message}`);
        console.error('登录失败:', error);
        showMessage(`登录失败: ${error.message}`);
      } finally {
        setButtonLoading(false);
      }
    }

    // 检查是否已登录
    function checkLoginStatus() {
      const adminUser = localStorage.getItem('admin_user');
      if (adminUser) {
        try {
          const user = JSON.parse(adminUser);
          if (user && user.is_admin) {
            debugLog('用户已登录，跳转到管理页面');
            window.location.href = 'admin_optimized.html';
            return;
          }
        } catch (e) {
          debugLog('解析用户信息失败，清除localStorage');
          localStorage.removeItem('admin_user');
        }
      }
    }

    // 测试网络连接
    async function testConnection() {
      try {
        debugLog('测试网络连接...');
        const response = await fetch('http://localhost:5000/api/health', {
          method: 'GET',
          timeout: 5000
        });
        
        if (response.ok) {
          const data = await response.json();
          debugLog(`网络连接正常: ${JSON.stringify(data)}`);
        } else {
          debugLog(`网络连接异常: ${response.status}`);
        }
      } catch (error) {
        debugLog(`网络连接测试失败: ${error.message}`);
      }
    }

    // 页面初始化
    document.addEventListener('DOMContentLoaded', () => {
      debugLog('登录页面加载完成');
      
      // 测试网络连接
      testConnection();
      
      // 检查登录状态
      checkLoginStatus();
      
      // 绑定表单提交事件
      document.getElementById('login-form').addEventListener('submit', handleLogin);
      
      // 自动聚焦到用户名输入框
      document.getElementById('username').focus();
      
      debugLog('页面初始化完成');
    });
  </script>
</body>
</html>
