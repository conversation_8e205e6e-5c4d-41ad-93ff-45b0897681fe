<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>商品管理 - 发卡商城管理后台</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background: #f8fafc;
      color: #333;
    }
    
    .container {
      display: flex;
      min-height: 100vh;
    }
    
    .sidebar {
      width: 280px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 2rem 0;
      position: fixed;
      height: 100vh;
      overflow-y: auto;
    }
    
    .sidebar-header {
      padding: 0 2rem 2rem;
      border-bottom: 1px solid rgba(255,255,255,0.1);
      margin-bottom: 2rem;
    }
    
    .sidebar-logo {
      font-size: 1.5rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
    }
    
    .sidebar-subtitle {
      opacity: 0.8;
      font-size: 0.9rem;
    }
    
    .sidebar-nav {
      padding: 0 1rem;
    }
    
    .nav-item {
      display: block;
      color: rgba(255,255,255,0.8);
      text-decoration: none;
      padding: 1rem;
      margin: 0.25rem 0;
      border-radius: 8px;
      transition: all 0.3s ease;
      font-weight: 500;
    }
    
    .nav-item:hover,
    .nav-item.active {
      background: rgba(255,255,255,0.1);
      color: white;
      transform: translateX(4px);
    }
    
    .nav-item.active {
      background: rgba(255,255,255,0.2);
    }
    
    .main {
      flex: 1;
      margin-left: 280px;
      padding: 2rem;
    }
    
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;
      background: white;
      padding: 1.5rem 2rem;
      border-radius: 16px;
      box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    }
    
    .header h1 {
      font-size: 1.75rem;
      font-weight: 700;
      color: #1f2937;
    }
    
    .user-info {
      display: flex;
      align-items: center;
      gap: 1rem;
      color: #6b7280;
    }
    
    .user-avatar {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 600;
    }
    
    .logout-btn {
      color: #ef4444;
      text-decoration: none;
      font-weight: 500;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      transition: background 0.3s ease;
    }
    
    .logout-btn:hover {
      background: #fef2f2;
    }
    
    .card {
      background: white;
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: 0 4px 6px rgba(0,0,0,0.05);
      border: 1px solid #e5e7eb;
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.5rem;
    }
    
    .card-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1f2937;
    }
    
    .btn {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 8px;
      font-weight: 500;
      text-decoration: none;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 0.9rem;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }
    
    .btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    
    .btn-primary:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
    
    .btn-outline {
      background: transparent;
      color: #667eea;
      border: 1px solid #667eea;
    }
    
    .btn-outline:hover {
      background: #667eea;
      color: white;
    }
    
    .btn-danger {
      background: #ef4444;
      color: white;
    }
    
    .btn-danger:hover {
      background: #dc2626;
    }
    
    .btn-sm {
      padding: 0.5rem 1rem;
      font-size: 0.8rem;
    }
    
    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1.5rem;
      margin-bottom: 1.5rem;
    }
    
    .form-group {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }
    
    .form-label {
      font-weight: 500;
      color: #374151;
      font-size: 0.9rem;
    }
    
    .form-input,
    .form-textarea {
      padding: 0.75rem 1rem;
      border: 1px solid #d1d5db;
      border-radius: 8px;
      font-size: 0.9rem;
      transition: border-color 0.3s ease;
    }
    
    .form-input:focus,
    .form-textarea:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .form-textarea {
      resize: vertical;
      min-height: 100px;
    }
    
    .table-container {
      overflow-x: auto;
      border-radius: 8px;
      border: 1px solid #e5e7eb;
    }
    
    table {
      width: 100%;
      border-collapse: collapse;
      background: white;
    }
    
    th,
    td {
      padding: 1rem;
      text-align: left;
      border-bottom: 1px solid #f3f4f6;
    }
    
    th {
      background: #f9fafb;
      font-weight: 600;
      color: #374151;
      font-size: 0.9rem;
    }
    
    td {
      color: #6b7280;
    }
    
    .product-image {
      width: 60px;
      height: 60px;
      object-fit: cover;
      border-radius: 8px;
    }
    
    .status-badge {
      padding: 0.25rem 0.75rem;
      border-radius: 12px;
      font-size: 0.8rem;
      font-weight: 500;
    }
    
    .status-available {
      background: #d1fae5;
      color: #065f46;
    }
    
    .status-low {
      background: #fef3c7;
      color: #92400e;
    }
    
    .status-out {
      background: #fee2e2;
      color: #991b1b;
    }
    
    .loading {
      text-align: center;
      padding: 3rem;
      color: #6b7280;
    }
    
    .spinner {
      width: 32px;
      height: 32px;
      border: 3px solid #f3f4f6;
      border-top: 3px solid #667eea;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .empty-state {
      text-align: center;
      padding: 3rem;
      color: #6b7280;
    }
    
    .toast {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: #10b981;
      color: white;
      padding: 1rem 1.5rem;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 1000;
      transform: translateY(100px);
      opacity: 0;
      transition: all 0.3s ease;
    }
    
    .toast.show {
      transform: translateY(0);
      opacity: 1;
    }
    
    .toast.error {
      background: #ef4444;
    }
    
    .toast.warning {
      background: #f59e0b;
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
      .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
      }
      
      .sidebar.open {
        transform: translateX(0);
      }
      
      .main {
        margin-left: 0;
        padding: 1rem;
      }
      
      .header {
        padding: 1rem;
      }
      
      .header h1 {
        font-size: 1.5rem;
      }
      
      .form-grid {
        grid-template-columns: 1fr;
      }
      
      .card {
        padding: 1.5rem;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="sidebar">
      <div class="sidebar-header">
        <div class="sidebar-logo">🛒 发卡商城</div>
        <div class="sidebar-subtitle">管理后台</div>
      </div>
      <nav class="sidebar-nav">
        <a href="admin_optimized.html" class="nav-item active">📦 商品管理</a>
        <a href="orders_optimized.html" class="nav-item">📋 订单管理</a>
        <a href="analytics.html" class="nav-item">📊 数据统计</a>
        <a href="settings.html" class="nav-item">⚙️ 系统设置</a>
        <a href="system_status.html" class="nav-item">🔍 系统状态</a>
      </nav>
    </div>

    <div class="main">
      <div class="header">
        <h1>商品管理</h1>
        <div class="user-info">
          <div class="user-avatar" id="user-avatar">A</div>
          <span id="user-name">管理员</span>
          <a href="#" class="logout-btn" onclick="logout()">退出登录</a>
        </div>
      </div>

      <!-- 添加商品表单 -->
      <div class="card">
        <div class="card-header">
          <h2 class="card-title">添加新商品</h2>
        </div>
        <form id="add-product-form">
          <div class="form-grid">
            <div class="form-group">
              <label for="name" class="form-label">商品名称 *</label>
              <input type="text" id="name" name="name" class="form-input" placeholder="请输入商品名称" required>
            </div>
            <div class="form-group">
              <label for="price" class="form-label">价格 *</label>
              <input type="number" id="price" name="price" class="form-input" placeholder="0.00" step="0.01" min="0" required>
            </div>
            <div class="form-group">
              <label for="stock" class="form-label">库存数量 *</label>
              <input type="number" id="stock" name="stock" class="form-input" placeholder="0" min="0" required>
            </div>
            <div class="form-group">
              <label for="image" class="form-label">商品图片URL</label>
              <input type="url" id="image" name="image" class="form-input" placeholder="https://example.com/image.jpg">
            </div>
          </div>
          <div class="form-group">
            <label for="description" class="form-label">商品描述</label>
            <textarea id="description" name="description" class="form-textarea" placeholder="请输入商品描述信息"></textarea>
          </div>
          <button type="submit" class="btn btn-primary">
            ➕ 添加商品
          </button>
        </form>
      </div>

      <!-- 商品列表 -->
      <div class="card">
        <div class="card-header">
          <h2 class="card-title">商品列表</h2>
          <button class="btn btn-outline" onclick="loadProducts()">
            🔄 刷新
          </button>
        </div>
        <div id="products-container">
          <div class="loading">
            <div class="spinner"></div>
            <p>正在加载商品列表...</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 全局变量
    let currentUser = null;

    // Toast 通知
    function showToast(message, type = 'success') {
      const toast = document.createElement('div');
      toast.className = `toast ${type}`;
      toast.textContent = message;
      document.body.appendChild(toast);

      setTimeout(() => toast.classList.add('show'), 10);
      setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => toast.remove(), 300);
      }, 3000);
    }

    // 格式化价格
    function formatPrice(price) {
      return `¥${parseFloat(price).toFixed(2)}`;
    }

    // 格式化库存状态
    function formatStockStatus(stock) {
      const num = parseInt(stock) || 0;
      if (num <= 0) {
        return { text: '缺货', class: 'status-out' };
      } else if (num <= 10) {
        return { text: `库存紧张 (${num})`, class: 'status-low' };
      } else {
        return { text: `库存充足 (${num})`, class: 'status-available' };
      }
    }

    // 检查登录状态
    async function checkLoginStatus() {
      try {
        // 先检查本地存储
        const adminUser = localStorage.getItem('admin_user');
        if (!adminUser) {
          throw new Error('未登录');
        }

        currentUser = JSON.parse(adminUser);
        if (!currentUser || !currentUser.is_admin) {
          throw new Error('权限不足');
        }

        // 更新用户信息显示
        document.getElementById('user-name').textContent = currentUser.username || '管理员';
        document.getElementById('user-avatar').textContent = (currentUser.username || 'A').charAt(0).toUpperCase();

      } catch (error) {
        console.error('登录检查失败:', error);
        showToast('请先登录管理员账户', 'warning');
        setTimeout(() => {
          window.location.href = 'login_optimized.html';
        }, 2000);
      }
    }

    // 退出登录
    async function logout() {
      if (!confirm('确定要退出登录吗？')) {
        return;
      }

      try {
        // 清除本地存储
        localStorage.removeItem('admin_user');

        // 尝试调用后端登出API
        try {
          await fetch('http://localhost:5000/api/auth/logout', {
            method: 'POST',
            credentials: 'include'
          });
        } catch (e) {
          console.log('后端登出失败，但本地已清除登录状态');
        }

        showToast('已退出登录', 'success');
        setTimeout(() => {
          window.location.href = 'login_optimized.html';
        }, 1000);

      } catch (error) {
        console.error('退出登录失败:', error);
        // 即使出错也跳转到登录页
        window.location.href = 'login_optimized.html';
      }
    }

    // 创建商品表格行
    function createProductRow(product) {
      const stockStatus = formatStockStatus(product.stock);

      return `
        <tr>
          <td>
            <img src="${product.image || `https://picsum.photos/60/60?random=${product.id}`}"
                 alt="${product.name}"
                 class="product-image"
                 loading="lazy">
          </td>
          <td>
            <div style="font-weight: 500; color: #1f2937;">${product.name}</div>
            <div style="font-size: 0.8rem; color: #6b7280;">ID: ${product.id}</div>
          </td>
          <td>${formatPrice(product.price)}</td>
          <td>
            <span class="status-badge ${stockStatus.class}">${stockStatus.text}</span>
          </td>
          <td style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            ${product.description || '无描述'}
          </td>
          <td>
            <div style="display: flex; gap: 0.5rem;">
              <button class="btn btn-outline btn-sm" onclick="editProduct(${product.id})">
                ✏️ 编辑
              </button>
              <button class="btn btn-danger btn-sm" onclick="deleteProduct(${product.id})">
                🗑️ 删除
              </button>
            </div>
          </td>
        </tr>
      `;
    }

    // 加载商品列表
    async function loadProducts() {
      const container = document.getElementById('products-container');

      try {
        container.innerHTML = `
          <div class="loading">
            <div class="spinner"></div>
            <p>正在加载商品列表...</p>
          </div>
        `;

        const response = await fetch('http://localhost:5000/api/products', {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          },
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        const products = data.items || [];

        if (products.length === 0) {
          container.innerHTML = `
            <div class="empty-state">
              <h3>📭 暂无商品</h3>
              <p>还没有添加任何商品，快来添加第一个商品吧！</p>
            </div>
          `;
          return;
        }

        container.innerHTML = `
          <div class="table-container">
            <table>
              <thead>
                <tr>
                  <th>图片</th>
                  <th>商品名称</th>
                  <th>价格</th>
                  <th>库存状态</th>
                  <th>描述</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                ${products.map(product => createProductRow(product)).join('')}
              </tbody>
            </table>
          </div>
        `;

      } catch (error) {
        console.error('加载商品列表失败:', error);
        container.innerHTML = `
          <div style="text-align: center; padding: 3rem; color: #ef4444;">
            <h3>❌ 加载失败</h3>
            <p>错误: ${error.message}</p>
            <button class="btn btn-primary" onclick="loadProducts()" style="margin-top: 1rem;">重试</button>
          </div>
        `;
      }
    }

    // 添加商品
    async function handleAddProduct(event) {
      event.preventDefault();

      const formData = new FormData(event.target);
      const productData = {
        name: formData.get('name').trim(),
        price: parseFloat(formData.get('price')),
        stock: parseInt(formData.get('stock')),
        image: formData.get('image').trim() || null,
        description: formData.get('description').trim() || null
      };

      // 验证数据
      if (!productData.name) {
        showToast('请输入商品名称', 'error');
        return;
      }

      if (isNaN(productData.price) || productData.price < 0) {
        showToast('请输入有效的价格', 'error');
        return;
      }

      if (isNaN(productData.stock) || productData.stock < 0) {
        showToast('请输入有效的库存数量', 'error');
        return;
      }

      try {
        const submitBtn = event.target.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.textContent = '添加中...';

        const response = await fetch('http://localhost:5000/api/products', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify(productData)
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        showToast('商品添加成功', 'success');

        // 重置表单
        event.target.reset();

        // 重新加载商品列表
        await loadProducts();

      } catch (error) {
        console.error('添加商品失败:', error);
        showToast(`添加商品失败: ${error.message}`, 'error');
      } finally {
        const submitBtn = event.target.querySelector('button[type="submit"]');
        submitBtn.disabled = false;
        submitBtn.innerHTML = '➕ 添加商品';
      }
    }

    // 编辑商品
    function editProduct(productId) {
      window.location.href = `edit_product_optimized.html?id=${productId}`;
    }

    // 删除商品
    async function deleteProduct(productId) {
      if (!confirm('确定要删除这个商品吗？此操作不可恢复。')) {
        return;
      }

      try {
        const response = await fetch(`http://localhost:5000/api/products/${productId}`, {
          method: 'DELETE',
          credentials: 'include'
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
        }

        showToast('商品删除成功', 'success');

        // 重新加载商品列表
        await loadProducts();

      } catch (error) {
        console.error('删除商品失败:', error);
        showToast(`删除商品失败: ${error.message}`, 'error');
      }
    }

    // 页面初始化
    document.addEventListener('DOMContentLoaded', async () => {
      console.log('管理后台页面加载完成');

      // 检查登录状态
      await checkLoginStatus();

      // 加载商品列表
      await loadProducts();

      // 绑定表单提交事件
      document.getElementById('add-product-form').addEventListener('submit', handleAddProduct);
    });
  </script>
</body>
</html>
