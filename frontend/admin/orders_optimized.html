<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>订单管理 - 发卡商城管理后台</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background: #f8fafc;
      color: #333;
    }
    
    .container {
      display: flex;
      min-height: 100vh;
    }
    
    .sidebar {
      width: 280px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 2rem 0;
      position: fixed;
      height: 100vh;
      overflow-y: auto;
    }
    
    .sidebar-header {
      padding: 0 2rem 2rem;
      border-bottom: 1px solid rgba(255,255,255,0.1);
      margin-bottom: 2rem;
    }
    
    .sidebar-logo {
      font-size: 1.5rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
    }
    
    .sidebar-subtitle {
      opacity: 0.8;
      font-size: 0.9rem;
    }
    
    .sidebar-nav {
      padding: 0 1rem;
    }
    
    .nav-item {
      display: block;
      color: rgba(255,255,255,0.8);
      text-decoration: none;
      padding: 1rem;
      margin: 0.25rem 0;
      border-radius: 8px;
      transition: all 0.3s ease;
      font-weight: 500;
    }
    
    .nav-item:hover,
    .nav-item.active {
      background: rgba(255,255,255,0.1);
      color: white;
      transform: translateX(4px);
    }
    
    .nav-item.active {
      background: rgba(255,255,255,0.2);
    }
    
    .main {
      flex: 1;
      margin-left: 280px;
      padding: 2rem;
    }
    
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;
      background: white;
      padding: 1.5rem 2rem;
      border-radius: 16px;
      box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    }
    
    .header h1 {
      font-size: 1.75rem;
      font-weight: 700;
      color: #1f2937;
    }
    
    .user-info {
      display: flex;
      align-items: center;
      gap: 1rem;
      color: #6b7280;
    }
    
    .user-avatar {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 600;
    }
    
    .logout-btn {
      color: #ef4444;
      text-decoration: none;
      font-weight: 500;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      transition: background 0.3s ease;
    }
    
    .logout-btn:hover {
      background: #fef2f2;
    }
    
    .card {
      background: white;
      border-radius: 16px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: 0 4px 6px rgba(0,0,0,0.05);
      border: 1px solid #e5e7eb;
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.5rem;
    }
    
    .card-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1f2937;
    }
    
    .filters {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-bottom: 1.5rem;
    }
    
    .form-group {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }
    
    .form-label {
      font-weight: 500;
      color: #374151;
      font-size: 0.9rem;
    }
    
    .form-input,
    .form-select {
      padding: 0.75rem 1rem;
      border: 1px solid #d1d5db;
      border-radius: 8px;
      font-size: 0.9rem;
      transition: border-color 0.3s ease;
    }
    
    .form-input:focus,
    .form-select:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .btn {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 8px;
      font-weight: 500;
      text-decoration: none;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 0.9rem;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }
    
    .btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    
    .btn-primary:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
    
    .btn-outline {
      background: transparent;
      color: #667eea;
      border: 1px solid #667eea;
    }
    
    .btn-outline:hover {
      background: #667eea;
      color: white;
    }
    
    .btn-sm {
      padding: 0.5rem 1rem;
      font-size: 0.8rem;
    }
    
    .table-container {
      overflow-x: auto;
      border-radius: 8px;
      border: 1px solid #e5e7eb;
    }
    
    table {
      width: 100%;
      border-collapse: collapse;
      background: white;
    }
    
    th,
    td {
      padding: 1rem;
      text-align: left;
      border-bottom: 1px solid #f3f4f6;
    }
    
    th {
      background: #f9fafb;
      font-weight: 600;
      color: #374151;
      font-size: 0.9rem;
    }
    
    td {
      color: #6b7280;
    }
    
    .status-badge {
      padding: 0.25rem 0.75rem;
      border-radius: 12px;
      font-size: 0.8rem;
      font-weight: 500;
    }
    
    .status-pending {
      background: #fef3c7;
      color: #92400e;
    }
    
    .status-processing {
      background: #dbeafe;
      color: #1e40af;
    }
    
    .status-shipped {
      background: #d1fae5;
      color: #065f46;
    }
    
    .status-delivered {
      background: #d1fae5;
      color: #065f46;
    }
    
    .status-cancelled {
      background: #fee2e2;
      color: #991b1b;
    }
    
    .status-refunded {
      background: #f3f4f6;
      color: #374151;
    }
    
    .loading {
      text-align: center;
      padding: 3rem;
      color: #6b7280;
    }
    
    .spinner {
      width: 32px;
      height: 32px;
      border: 3px solid #f3f4f6;
      border-top: 3px solid #667eea;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .empty-state {
      text-align: center;
      padding: 3rem;
      color: #6b7280;
    }
    
    .toast {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: #10b981;
      color: white;
      padding: 1rem 1.5rem;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 1000;
      transform: translateY(100px);
      opacity: 0;
      transition: all 0.3s ease;
    }
    
    .toast.show {
      transform: translateY(0);
      opacity: 1;
    }
    
    .toast.error {
      background: #ef4444;
    }
    
    .toast.warning {
      background: #f59e0b;
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
      .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
      }
      
      .main {
        margin-left: 0;
        padding: 1rem;
      }
      
      .header {
        padding: 1rem;
      }
      
      .header h1 {
        font-size: 1.5rem;
      }
      
      .filters {
        grid-template-columns: 1fr;
      }
      
      .card {
        padding: 1.5rem;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="sidebar">
      <div class="sidebar-header">
        <div class="sidebar-logo">
          <img src="../../icon/8304CEF178EEF908F89E309C65C1E521.jpg" alt="青云小铺" style="width: 32px; height: 32px; border-radius: 6px; margin-right: 0.5rem; vertical-align: middle;">
          青云小铺
        </div>
        <div class="sidebar-subtitle">管理后台</div>
      </div>
      <nav class="sidebar-nav">
        <a href="admin_optimized.html" class="nav-item">📦 商品管理</a>
        <a href="orders_optimized.html" class="nav-item active">📋 订单管理</a>
        <a href="analytics.html" class="nav-item">📊 数据统计</a>
        <a href="settings.html" class="nav-item">⚙️ 系统设置</a>
        <a href="system_status.html" class="nav-item">🔍 系统状态</a>
      </nav>
    </div>

    <div class="main">
      <div class="header">
        <h1>订单管理</h1>
        <div class="user-info">
          <div class="user-avatar" id="user-avatar">A</div>
          <span id="user-name">管理员</span>
          <a href="#" class="logout-btn" onclick="logout()">退出登录</a>
        </div>
      </div>

      <!-- 订单筛选 -->
      <div class="card">
        <div class="card-header">
          <h2 class="card-title">订单筛选</h2>
          <button class="btn btn-outline" onclick="clearFilters()">
            🔄 清除筛选
          </button>
        </div>
        <div class="filters">
          <div class="form-group">
            <label for="status-filter" class="form-label">订单状态</label>
            <select id="status-filter" class="form-select">
              <option value="">全部状态</option>
              <option value="pending">待处理</option>
              <option value="processing">处理中</option>
              <option value="shipped">已发货</option>
              <option value="delivered">已完成</option>
              <option value="cancelled">已取消</option>
              <option value="refunded">已退款</option>
            </select>
          </div>
          <div class="form-group">
            <label for="start-date" class="form-label">开始日期</label>
            <input type="date" id="start-date" class="form-input">
          </div>
          <div class="form-group">
            <label for="end-date" class="form-label">结束日期</label>
            <input type="date" id="end-date" class="form-input">
          </div>
          <div class="form-group">
            <label class="form-label">&nbsp;</label>
            <button class="btn btn-primary" onclick="applyFilters()">
              🔍 应用筛选
            </button>
          </div>
        </div>
      </div>

      <!-- 订单列表 -->
      <div class="card">
        <div class="card-header">
          <h2 class="card-title">订单列表</h2>
          <button class="btn btn-outline" onclick="loadOrders()">
            🔄 刷新
          </button>
        </div>
        <div id="orders-container">
          <div class="loading">
            <div class="spinner"></div>
            <p>正在加载订单列表...</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 全局变量
    let currentUser = null;
    let currentFilters = {};

    // Toast 通知
    function showToast(message, type = 'success') {
      const toast = document.createElement('div');
      toast.className = `toast ${type}`;
      toast.textContent = message;
      document.body.appendChild(toast);

      setTimeout(() => toast.classList.add('show'), 10);
      setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => toast.remove(), 300);
      }, 3000);
    }

    // 格式化价格
    function formatPrice(price) {
      return `¥${parseFloat(price).toFixed(2)}`;
    }

    // 格式化日期
    function formatDate(dateString) {
      if (!dateString) return '未知';
      const date = new Date(dateString);
      return date.toLocaleString('zh-CN');
    }

    // 格式化订单状态
    function formatOrderStatus(status) {
      const statusMap = {
        'pending': '待处理',
        'processing': '处理中',
        'shipped': '已发货',
        'delivered': '已完成',
        'cancelled': '已取消',
        'refunded': '已退款'
      };
      return statusMap[status] || status;
    }

    // 检查登录状态
    async function checkLoginStatus() {
      try {
        const adminUser = localStorage.getItem('admin_user');
        if (!adminUser) {
          throw new Error('未登录');
        }

        currentUser = JSON.parse(adminUser);
        if (!currentUser || !currentUser.is_admin) {
          throw new Error('权限不足');
        }

        // 更新用户信息显示
        document.getElementById('user-name').textContent = currentUser.username || '管理员';
        document.getElementById('user-avatar').textContent = (currentUser.username || 'A').charAt(0).toUpperCase();

      } catch (error) {
        console.error('登录检查失败:', error);
        showToast('请先登录管理员账户', 'warning');
        setTimeout(() => {
          window.location.href = 'login_optimized.html';
        }, 2000);
      }
    }

    // 退出登录
    async function logout() {
      if (!confirm('确定要退出登录吗？')) {
        return;
      }

      try {
        localStorage.removeItem('admin_user');
        showToast('已退出登录', 'success');
        setTimeout(() => {
          window.location.href = 'login_optimized.html';
        }, 1000);
      } catch (error) {
        console.error('退出登录失败:', error);
        window.location.href = 'login_optimized.html';
      }
    }

    // 创建订单表格行
    function createOrderRow(order) {
      return `
        <tr>
          <td>
            <div style="font-weight: 500; color: #1f2937;">#${order.id}</div>
            <div style="font-size: 0.8rem; color: #6b7280;">${formatDate(order.created_at)}</div>
          </td>
          <td>
            <div style="font-weight: 500; color: #1f2937;">${order.product_name || '未知产品'}</div>
            <div style="font-size: 0.8rem; color: #6b7280;">数量: ${order.quantity}</div>
          </td>
          <td>${formatPrice(order.total_price)}</td>
          <td>
            <span class="status-badge status-${order.status}">
              ${formatOrderStatus(order.status)}
            </span>
          </td>
          <td style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
            ${order.notes || '无备注'}
          </td>
          <td>
            <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
              ${order.status === 'pending' ? `
                <button class="btn btn-primary btn-sm" onclick="updateOrderStatus(${order.id}, 'processing')">
                  处理
                </button>
                <button class="btn btn-outline btn-sm" onclick="cancelOrder(${order.id})">
                  取消
                </button>
              ` : ''}
              ${order.status === 'processing' ? `
                <button class="btn btn-primary btn-sm" onclick="updateOrderStatus(${order.id}, 'shipped')">
                  发货
                </button>
              ` : ''}
              ${order.status === 'shipped' ? `
                <button class="btn btn-primary btn-sm" onclick="updateOrderStatus(${order.id}, 'delivered')">
                  完成
                </button>
              ` : ''}
              <button class="btn btn-outline btn-sm" onclick="viewOrderDetails(${order.id})">
                详情
              </button>
            </div>
          </td>
        </tr>
      `;
    }

    // 加载订单列表
    async function loadOrders() {
      const container = document.getElementById('orders-container');

      try {
        container.innerHTML = `
          <div class="loading">
            <div class="spinner"></div>
            <p>正在加载订单列表...</p>
          </div>
        `;

        // 构建查询参数
        const params = new URLSearchParams();
        Object.keys(currentFilters).forEach(key => {
          if (currentFilters[key]) {
            params.append(key, currentFilters[key]);
          }
        });

        const url = `http://localhost:5000/api/orders${params.toString() ? '?' + params.toString() : ''}`;
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          },
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        const orders = data.items || [];

        if (orders.length === 0) {
          container.innerHTML = `
            <div class="empty-state">
              <h3>📭 暂无订单</h3>
              <p>还没有任何订单记录</p>
            </div>
          `;
          return;
        }

        container.innerHTML = `
          <div class="table-container">
            <table>
              <thead>
                <tr>
                  <th>订单信息</th>
                  <th>商品信息</th>
                  <th>总价</th>
                  <th>状态</th>
                  <th>备注</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                ${orders.map(order => createOrderRow(order)).join('')}
              </tbody>
            </table>
          </div>
        `;

      } catch (error) {
        console.error('加载订单列表失败:', error);
        container.innerHTML = `
          <div style="text-align: center; padding: 3rem; color: #ef4444;">
            <h3>❌ 加载失败</h3>
            <p>错误: ${error.message}</p>
            <button class="btn btn-primary" onclick="loadOrders()" style="margin-top: 1rem;">重试</button>
          </div>
        `;
      }
    }

    // 应用筛选
    function applyFilters() {
      currentFilters = {
        status: document.getElementById('status-filter').value,
        start_date: document.getElementById('start-date').value,
        end_date: document.getElementById('end-date').value
      };

      // 移除空值
      Object.keys(currentFilters).forEach(key => {
        if (!currentFilters[key]) {
          delete currentFilters[key];
        }
      });

      loadOrders();
    }

    // 清除筛选
    function clearFilters() {
      document.getElementById('status-filter').value = '';
      document.getElementById('start-date').value = '';
      document.getElementById('end-date').value = '';

      currentFilters = {};
      loadOrders();
    }

    // 更新订单状态
    async function updateOrderStatus(orderId, newStatus) {
      try {
        const response = await fetch(`http://localhost:5000/api/orders/${orderId}/status`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({ status: newStatus })
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
        }

        showToast('订单状态更新成功', 'success');
        await loadOrders(); // 重新加载订单列表

      } catch (error) {
        console.error('更新订单状态失败:', error);
        showToast(`更新订单状态失败: ${error.message}`, 'error');
      }
    }

    // 取消订单
    async function cancelOrder(orderId) {
      const reason = prompt('请输入取消原因（可选）:') || '管理员取消';

      if (!confirm('确定要取消这个订单吗？')) {
        return;
      }

      try {
        const response = await fetch(`http://localhost:5000/api/orders/${orderId}/cancel`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({ reason })
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
        }

        showToast('订单已取消', 'success');
        await loadOrders(); // 重新加载订单列表

      } catch (error) {
        console.error('取消订单失败:', error);
        showToast(`取消订单失败: ${error.message}`, 'error');
      }
    }

    // 查看订单详情
    function viewOrderDetails(orderId) {
      // 这里可以打开订单详情模态框或跳转到详情页面
      alert(`查看订单 #${orderId} 的详情功能开发中...`);
    }

    // 页面初始化
    document.addEventListener('DOMContentLoaded', async () => {
      console.log('订单管理页面加载完成');

      // 检查登录状态
      await checkLoginStatus();

      // 加载订单列表
      await loadOrders();
    });
  </script>
</body>
</html>
