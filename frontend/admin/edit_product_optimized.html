<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>编辑商品 - 青云小铺管理后台</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background: #f8fafc;
      color: #333;
    }
    
    .container {
      display: flex;
      min-height: 100vh;
    }
    
    .sidebar {
      width: 280px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 2rem 0;
      position: fixed;
      height: 100vh;
      overflow-y: auto;
    }
    
    .sidebar-header {
      padding: 0 2rem 2rem;
      border-bottom: 1px solid rgba(255,255,255,0.1);
      margin-bottom: 2rem;
      display: flex;
      align-items: center;
      gap: 1rem;
    }
    
    .sidebar-logo {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      background: white;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .sidebar-logo img {
      width: 32px;
      height: 32px;
      border-radius: 6px;
    }
    
    .sidebar-title {
      font-size: 1.5rem;
      font-weight: 700;
    }
    
    .sidebar-subtitle {
      opacity: 0.8;
      font-size: 0.9rem;
    }
    
    .sidebar-nav {
      padding: 0 1rem;
    }
    
    .nav-item {
      display: block;
      color: rgba(255,255,255,0.8);
      text-decoration: none;
      padding: 1rem;
      margin: 0.25rem 0;
      border-radius: 8px;
      transition: all 0.3s ease;
      font-weight: 500;
    }
    
    .nav-item:hover,
    .nav-item.active {
      background: rgba(255,255,255,0.1);
      color: white;
      transform: translateX(4px);
    }
    
    .nav-item.active {
      background: rgba(255,255,255,0.2);
    }
    
    .main {
      flex: 1;
      margin-left: 280px;
      padding: 2rem;
    }
    
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;
      background: white;
      padding: 1.5rem 2rem;
      border-radius: 16px;
      box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    }
    
    .header h1 {
      font-size: 1.75rem;
      font-weight: 700;
      color: #1f2937;
    }
    
    .breadcrumb {
      color: #6b7280;
      font-size: 0.9rem;
      margin-bottom: 0.5rem;
    }
    
    .breadcrumb a {
      color: #667eea;
      text-decoration: none;
    }
    
    .user-info {
      display: flex;
      align-items: center;
      gap: 1rem;
      color: #6b7280;
    }
    
    .user-avatar {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 600;
    }
    
    .logout-btn {
      color: #ef4444;
      text-decoration: none;
      font-weight: 500;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      transition: background 0.3s ease;
    }
    
    .logout-btn:hover {
      background: #fef2f2;
    }
    
    .form-container {
      background: white;
      border-radius: 16px;
      padding: 2rem;
      box-shadow: 0 4px 6px rgba(0,0,0,0.05);
      border: 1px solid #e5e7eb;
    }
    
    .form-section {
      margin-bottom: 2rem;
      padding-bottom: 2rem;
      border-bottom: 1px solid #e5e7eb;
    }
    
    .form-section:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }
    
    .section-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
    
    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;
    }
    
    .form-group {
      margin-bottom: 1rem;
    }
    
    .form-label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #374151;
    }
    
    .form-input,
    .form-select,
    .form-textarea {
      width: 100%;
      padding: 0.75rem 1rem;
      border: 2px solid #e5e7eb;
      border-radius: 8px;
      font-size: 1rem;
      transition: all 0.3s ease;
    }
    
    .form-input:focus,
    .form-select:focus,
    .form-textarea:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .form-textarea {
      resize: vertical;
      min-height: 120px;
    }
    
    .form-textarea.large {
      min-height: 200px;
    }
    
    .required {
      color: #dc3545;
    }
    
    .form-help {
      font-size: 0.875rem;
      color: #6b7280;
      margin-top: 0.25rem;
    }
    
    .delivery-content-section {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 1.5rem;
      margin-top: 1rem;
    }
    
    .content-type-selector {
      display: flex;
      gap: 1rem;
      margin-bottom: 1rem;
    }
    
    .content-type-btn {
      padding: 0.5rem 1rem;
      border: 2px solid #e5e7eb;
      border-radius: 8px;
      background: white;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 500;
    }
    
    .content-type-btn.active {
      border-color: #667eea;
      background: #667eea;
      color: white;
    }
    
    .content-editor {
      margin-top: 1rem;
    }
    
    .list-item {
      display: flex;
      gap: 0.5rem;
      margin-bottom: 0.5rem;
      align-items: center;
    }
    
    .list-item input {
      flex: 1;
    }
    
    .remove-item-btn {
      background: #ef4444;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 0.5rem;
      cursor: pointer;
      font-size: 0.8rem;
    }
    
    .add-item-btn {
      background: #10b981;
      color: white;
      border: none;
      border-radius: 8px;
      padding: 0.75rem 1rem;
      cursor: pointer;
      font-weight: 500;
      margin-top: 0.5rem;
    }
    
    .preview-section {
      background: #eff6ff;
      border: 1px solid #dbeafe;
      border-radius: 8px;
      padding: 1rem;
      margin-top: 1rem;
    }
    
    .preview-title {
      font-weight: 600;
      color: #1e40af;
      margin-bottom: 0.5rem;
    }
    
    .preview-content {
      background: white;
      border-radius: 4px;
      padding: 1rem;
      font-family: monospace;
      white-space: pre-wrap;
      max-height: 200px;
      overflow-y: auto;
    }
    
    .btn {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 8px;
      font-weight: 500;
      text-decoration: none;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 1rem;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }
    
    .btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    
    .btn-primary:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
    
    .btn-outline {
      background: transparent;
      color: #667eea;
      border: 2px solid #667eea;
    }
    
    .btn-outline:hover {
      background: #667eea;
      color: white;
    }
    
    .btn-danger {
      background: #ef4444;
      color: white;
    }
    
    .btn-danger:hover {
      background: #dc2626;
    }
    
    .form-actions {
      display: flex;
      gap: 1rem;
      justify-content: flex-end;
      margin-top: 2rem;
      padding-top: 2rem;
      border-top: 1px solid #e5e7eb;
    }
    
    .toast {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: #10b981;
      color: white;
      padding: 1rem 1.5rem;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 1000;
      transform: translateY(100px);
      opacity: 0;
      transition: all 0.3s ease;
    }
    
    .toast.show {
      transform: translateY(0);
      opacity: 1;
    }
    
    .toast.error {
      background: #ef4444;
    }
    
    .toast.warning {
      background: #f59e0b;
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
      .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
      }
      
      .main {
        margin-left: 0;
        padding: 1rem;
      }
      
      .header {
        padding: 1rem;
      }
      
      .header h1 {
        font-size: 1.5rem;
      }
      
      .form-grid {
        grid-template-columns: 1fr;
      }
      
      .form-actions {
        flex-direction: column;
      }
      
      .content-type-selector {
        flex-direction: column;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="sidebar">
      <div class="sidebar-header">
        <div class="sidebar-logo">
          <img src="../../icon/8304CEF178EEF908F89E309C65C1E521.jpg" alt="青云小铺">
        </div>
        <div>
          <div class="sidebar-title">青云小铺</div>
          <div class="sidebar-subtitle">管理后台</div>
        </div>
      </div>
      <nav class="sidebar-nav">
        <a href="admin_optimized.html" class="nav-item active">📦 商品管理</a>
        <a href="orders_optimized.html" class="nav-item">📋 订单管理</a>
        <a href="analytics.html" class="nav-item">📊 数据统计</a>
        <a href="settings.html" class="nav-item">⚙️ 系统设置</a>
        <a href="system_status.html" class="nav-item">🔍 系统状态</a>
      </nav>
    </div>

    <div class="main">
      <div class="header">
        <div>
          <div class="breadcrumb">
            <a href="admin_optimized.html">商品管理</a> > 编辑商品
          </div>
          <h1>编辑商品</h1>
        </div>
        <div class="user-info">
          <div class="user-avatar" id="user-avatar">A</div>
          <span id="user-name">管理员</span>
          <a href="#" class="logout-btn" onclick="logout()">退出登录</a>
        </div>
      </div>

      <div class="form-container">
        <form id="product-form">
          <!-- 基本信息 -->
          <div class="form-section">
            <h3 class="section-title">📝 基本信息</h3>
            <div class="form-grid">
              <div class="form-group">
                <label for="product-name" class="form-label">商品名称 <span class="required">*</span></label>
                <input type="text" id="product-name" name="name" class="form-input" placeholder="请输入商品名称" required>
              </div>
              <div class="form-group">
                <label for="product-price" class="form-label">价格 <span class="required">*</span></label>
                <input type="number" id="product-price" name="price" class="form-input" placeholder="0.00" step="0.01" min="0" required>
              </div>
              <div class="form-group">
                <label for="product-stock" class="form-label">库存数量 <span class="required">*</span></label>
                <input type="number" id="product-stock" name="stock" class="form-input" placeholder="0" min="0" required>
                <div class="form-help">虚拟商品库存表示可发货次数</div>
              </div>
              <div class="form-group">
                <label for="product-category" class="form-label">商品分类</label>
                <select id="product-category" name="category" class="form-select">
                  <option value="">请选择分类</option>
                  <option value="software">软件激活码</option>
                  <option value="game">游戏道具</option>
                  <option value="account">账号密码</option>
                  <option value="course">在线课程</option>
                  <option value="other">其他</option>
                </select>
              </div>
            </div>
            <div class="form-group">
              <label for="product-description" class="form-label">商品描述</label>
              <textarea id="product-description" name="description" class="form-textarea" placeholder="请输入商品描述..."></textarea>
            </div>
          </div>

          <!-- 虚拟商品发货内容 -->
          <div class="form-section">
            <h3 class="section-title">📦 虚拟商品发货内容</h3>
            <p class="form-help">设置用户购买后自动发送的内容，支持文本和列表格式</p>
            
            <div class="delivery-content-section">
              <div class="content-type-selector">
                <button type="button" class="content-type-btn active" data-type="text" onclick="switchContentType('text')">
                  📝 文本内容
                </button>
                <button type="button" class="content-type-btn" data-type="list" onclick="switchContentType('list')">
                  📋 列表内容
                </button>
              </div>
              
              <!-- 文本内容编辑器 -->
              <div id="text-editor" class="content-editor">
                <div class="form-group">
                  <label for="delivery-text" class="form-label">发货内容 <span class="required">*</span></label>
                  <textarea id="delivery-text" name="delivery_content" class="form-textarea large" 
                    placeholder="请输入发货内容，例如：&#10;激活码：XXXX-XXXX-XXXX-XXXX&#10;使用说明：&#10;1. 打开软件&#10;2. 输入激活码&#10;3. 点击激活"></textarea>
                  <div class="form-help">支持换行，用户将收到完整的文本内容</div>
                </div>
              </div>
              
              <!-- 列表内容编辑器 -->
              <div id="list-editor" class="content-editor" style="display: none;">
                <div class="form-group">
                  <label class="form-label">发货列表 <span class="required">*</span></label>
                  <div id="delivery-list">
                    <div class="list-item">
                      <input type="text" class="form-input" placeholder="请输入发货项目，例如：激活码1" data-index="0">
                      <button type="button" class="remove-item-btn" onclick="removeListItem(0)">删除</button>
                    </div>
                  </div>
                  <button type="button" class="add-item-btn" onclick="addListItem()">+ 添加项目</button>
                  <div class="form-help">每次发货时会随机选择一个项目发送给用户，发送后该项目会被标记为已使用</div>
                </div>
              </div>
              
              <!-- 预览区域 -->
              <div class="preview-section">
                <div class="preview-title">📧 发货邮件预览</div>
                <div class="preview-content" id="preview-content">
                  请输入发货内容以查看预览...
                </div>
              </div>
            </div>
          </div>

          <!-- 邮件设置 -->
          <div class="form-section">
            <h3 class="section-title">📧 邮件发货设置</h3>
            <div class="form-grid">
              <div class="form-group">
                <label for="email-subject" class="form-label">邮件主题</label>
                <input type="text" id="email-subject" name="email_subject" class="form-input" 
                  placeholder="【青云小铺】您购买的商品已发货">
                <div class="form-help">留空则使用默认主题</div>
              </div>
              <div class="form-group">
                <label for="sender-email" class="form-label">发件人邮箱</label>
                <input type="email" id="sender-email" name="sender_email" class="form-input" 
                  value="<EMAIL>" readonly>
                <div class="form-help">使用本站域名作为发件人</div>
              </div>
            </div>
            <div class="form-group">
              <label for="email-template" class="form-label">邮件模板</label>
              <textarea id="email-template" name="email_template" class="form-textarea" 
                placeholder="亲爱的用户，感谢您在青云小铺的购买！&#10;&#10;您购买的商品：{product_name}&#10;订单号：{order_id}&#10;购买时间：{purchase_time}&#10;&#10;发货内容：&#10;{delivery_content}&#10;&#10;如有问题请联系客服。&#10;&#10;青云小铺&#10;<EMAIL>"></textarea>
              <div class="form-help">可使用变量：{product_name}、{order_id}、{purchase_time}、{delivery_content}</div>
            </div>
          </div>

          <div class="form-actions">
            <a href="admin_optimized.html" class="btn btn-outline">取消</a>
            <button type="button" class="btn btn-outline" onclick="previewEmail()">预览邮件</button>
            <button type="submit" class="btn btn-primary">保存商品</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <script>
    // 全局变量
    let currentUser = null;
    let productId = null;
    let currentContentType = 'text';
    let listItemIndex = 1;

    // Toast 通知
    function showToast(message, type = 'success') {
      const toast = document.createElement('div');
      toast.className = `toast ${type}`;
      toast.textContent = message;
      document.body.appendChild(toast);

      setTimeout(() => toast.classList.add('show'), 10);
      setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => toast.remove(), 300);
      }, 3000);
    }

    // 检查登录状态
    async function checkLoginStatus() {
      try {
        const adminUser = localStorage.getItem('admin_user');
        if (!adminUser) {
          throw new Error('未登录');
        }

        currentUser = JSON.parse(adminUser);
        if (!currentUser || !currentUser.is_admin) {
          throw new Error('权限不足');
        }

        // 更新用户信息显示
        document.getElementById('user-name').textContent = currentUser.username || '管理员';
        document.getElementById('user-avatar').textContent = (currentUser.username || 'A').charAt(0).toUpperCase();

      } catch (error) {
        console.error('登录检查失败:', error);
        showToast('请先登录管理员账户', 'error');
        setTimeout(() => {
          window.location.href = 'login_fixed.html';
        }, 2000);
      }
    }

    // 退出登录
    async function logout() {
      if (!confirm('确定要退出登录吗？')) {
        return;
      }

      try {
        localStorage.removeItem('admin_user');
        showToast('已退出登录', 'success');
        setTimeout(() => {
          window.location.href = 'login_fixed.html';
        }, 1000);
      } catch (error) {
        console.error('退出登录失败:', error);
        window.location.href = 'login_fixed.html';
      }
    }

    // 获取URL参数
    function getUrlParameter(name) {
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get(name);
    }

    // 切换内容类型
    function switchContentType(type) {
      currentContentType = type;

      // 更新按钮状态
      document.querySelectorAll('.content-type-btn').forEach(btn => {
        btn.classList.remove('active');
      });
      document.querySelector(`[data-type="${type}"]`).classList.add('active');

      // 显示/隐藏编辑器
      const textEditor = document.getElementById('text-editor');
      const listEditor = document.getElementById('list-editor');

      if (type === 'text') {
        textEditor.style.display = 'block';
        listEditor.style.display = 'none';
      } else {
        textEditor.style.display = 'none';
        listEditor.style.display = 'block';
      }

      updatePreview();
    }

    // 添加列表项
    function addListItem() {
      const listContainer = document.getElementById('delivery-list');
      const newItem = document.createElement('div');
      newItem.className = 'list-item';
      newItem.innerHTML = `
        <input type="text" class="form-input" placeholder="请输入发货项目" data-index="${listItemIndex}">
        <button type="button" class="remove-item-btn" onclick="removeListItem(${listItemIndex})">删除</button>
      `;

      listContainer.appendChild(newItem);
      listItemIndex++;

      // 绑定输入事件
      newItem.querySelector('input').addEventListener('input', updatePreview);
    }

    // 删除列表项
    function removeListItem(index) {
      const listItems = document.querySelectorAll('.list-item');
      if (listItems.length <= 1) {
        showToast('至少需要保留一个项目', 'warning');
        return;
      }

      const itemToRemove = document.querySelector(`[data-index="${index}"]`).closest('.list-item');
      itemToRemove.remove();
      updatePreview();
    }

    // 更新预览
    function updatePreview() {
      const previewContent = document.getElementById('preview-content');
      let content = '';

      if (currentContentType === 'text') {
        const textContent = document.getElementById('delivery-text').value;
        content = textContent || '请输入发货内容...';
      } else {
        const listInputs = document.querySelectorAll('#delivery-list input');
        const items = Array.from(listInputs)
          .map(input => input.value.trim())
          .filter(value => value);

        if (items.length > 0) {
          content = '发货列表：\n' + items.map((item, index) => `${index + 1}. ${item}`).join('\n');
          content += '\n\n注：系统将随机选择一个项目发送给用户';
        } else {
          content = '请添加发货项目...';
        }
      }

      previewContent.textContent = content;
    }

    // 预览邮件
    function previewEmail() {
      const productName = document.getElementById('product-name').value || '示例商品';
      const emailSubject = document.getElementById('email-subject').value || '【青云小铺】您购买的商品已发货';
      const emailTemplate = document.getElementById('email-template').value;

      let deliveryContent = '';
      if (currentContentType === 'text') {
        deliveryContent = document.getElementById('delivery-text').value || '示例发货内容';
      } else {
        const listInputs = document.querySelectorAll('#delivery-list input');
        const items = Array.from(listInputs)
          .map(input => input.value.trim())
          .filter(value => value);
        deliveryContent = items.length > 0 ? items[0] : '示例发货项目';
      }

      // 替换模板变量
      const emailContent = emailTemplate
        .replace(/{product_name}/g, productName)
        .replace(/{order_id}/g, 'QY' + Date.now())
        .replace(/{purchase_time}/g, new Date().toLocaleString('zh-CN'))
        .replace(/{delivery_content}/g, deliveryContent);

      // 创建预览窗口
      const previewWindow = window.open('', '_blank', 'width=600,height=800');
      previewWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>邮件预览</title>
          <style>
            body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
            .email-header { background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
            .email-content { background: white; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
            pre { background: #f8f9fa; padding: 10px; border-radius: 4px; white-space: pre-wrap; }
          </style>
        </head>
        <body>
          <div class="email-header">
            <h2>📧 邮件预览</h2>
            <p><strong>主题：</strong> ${emailSubject}</p>
            <p><strong>发件人：</strong> <EMAIL></p>
            <p><strong>收件人：</strong> <EMAIL></p>
          </div>
          <div class="email-content">
            <pre>${emailContent}</pre>
          </div>
        </body>
        </html>
      `);
    }

    // 加载商品信息
    async function loadProductInfo() {
      productId = getUrlParameter('id');

      if (!productId) {
        // 新建商品
        document.querySelector('.breadcrumb').innerHTML = '<a href="admin_optimized.html">商品管理</a> > 新建商品';
        document.querySelector('.header h1').textContent = '新建商品';
        return;
      }

      try {
        const response = await fetch(`http://localhost:5000/api/products/${productId}`, {
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const product = await response.json();

        // 填充表单
        document.getElementById('product-name').value = product.name || '';
        document.getElementById('product-price').value = product.price || '';
        document.getElementById('product-stock').value = product.stock || '';
        document.getElementById('product-category').value = product.category || '';
        document.getElementById('product-description').value = product.description || '';

        // 填充发货内容
        if (product.delivery_content) {
          if (product.delivery_type === 'list') {
            switchContentType('list');
            // 解析列表内容
            const items = JSON.parse(product.delivery_content);
            const listContainer = document.getElementById('delivery-list');
            listContainer.innerHTML = '';

            items.forEach((item, index) => {
              const itemDiv = document.createElement('div');
              itemDiv.className = 'list-item';
              itemDiv.innerHTML = `
                <input type="text" class="form-input" value="${item}" data-index="${index}">
                <button type="button" class="remove-item-btn" onclick="removeListItem(${index})">删除</button>
              `;
              listContainer.appendChild(itemDiv);
              itemDiv.querySelector('input').addEventListener('input', updatePreview);
            });

            listItemIndex = items.length;
          } else {
            switchContentType('text');
            document.getElementById('delivery-text').value = product.delivery_content;
          }
        }

        // 填充邮件设置
        document.getElementById('email-subject').value = product.email_subject || '';
        document.getElementById('email-template').value = product.email_template || '';

        updatePreview();

      } catch (error) {
        console.error('加载商品信息失败:', error);
        showToast(`加载商品信息失败: ${error.message}`, 'error');
      }
    }

    // 保存商品
    async function saveProduct(event) {
      event.preventDefault();

      const formData = new FormData(event.target);

      // 获取发货内容
      let deliveryContent = '';
      if (currentContentType === 'text') {
        deliveryContent = document.getElementById('delivery-text').value;
      } else {
        const listInputs = document.querySelectorAll('#delivery-list input');
        const items = Array.from(listInputs)
          .map(input => input.value.trim())
          .filter(value => value);
        deliveryContent = JSON.stringify(items);
      }

      const productData = {
        name: formData.get('name'),
        price: parseFloat(formData.get('price')),
        stock: parseInt(formData.get('stock')),
        category: formData.get('category'),
        description: formData.get('description'),
        delivery_type: currentContentType,
        delivery_content: deliveryContent,
        email_subject: formData.get('email_subject'),
        email_template: formData.get('email_template'),
        sender_email: formData.get('sender_email')
      };

      // 验证必填字段
      if (!productData.name || !productData.price || productData.stock < 0) {
        showToast('请填写完整的商品信息', 'error');
        return;
      }

      if (!deliveryContent) {
        showToast('请设置发货内容', 'error');
        return;
      }

      try {
        const url = productId
          ? `http://localhost:5000/api/products/${productId}`
          : 'http://localhost:5000/api/products';

        const method = productId ? 'PUT' : 'POST';

        const response = await fetch(url, {
          method: method,
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify(productData)
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        showToast(productId ? '商品更新成功' : '商品创建成功', 'success');

        setTimeout(() => {
          window.location.href = 'admin_optimized.html';
        }, 1500);

      } catch (error) {
        console.error('保存商品失败:', error);
        showToast(`保存商品失败: ${error.message}`, 'error');
      }
    }

    // 页面初始化
    document.addEventListener('DOMContentLoaded', async () => {
      console.log('商品编辑页面加载完成');

      // 检查登录状态
      await checkLoginStatus();

      // 加载商品信息
      await loadProductInfo();

      // 绑定事件
      document.getElementById('product-form').addEventListener('submit', saveProduct);
      document.getElementById('delivery-text').addEventListener('input', updatePreview);

      // 为现有列表项绑定事件
      document.querySelectorAll('#delivery-list input').forEach(input => {
        input.addEventListener('input', updatePreview);
      });

      // 初始化预览
      updatePreview();
    });
  </script>
</body>
</html>
