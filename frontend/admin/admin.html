<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>后台管理</title>
  <link rel="stylesheet" href="../../static/style.css" />
  <style>
    body { background: #f6f8fa; }
    .sidebar { background: #222; color: #fff; width: 200px; min-height: 100vh; float: left; padding: 2em 1em; box-sizing: border-box; }
    .sidebar h2 { font-size: 1.5em; margin-bottom: 1em; }
    .sidebar ul { list-style: none; padding: 0; }
    .sidebar li { margin: 1em 0; }
    .sidebar a { color: #fff; text-decoration: none; transition: color 0.2s; }
    .sidebar a:hover { color: #ffd700; }
    .main { margin-left: 220px; padding: 2em 3vw; }
    .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 2em; }
    .header h1 { margin: 0; font-size: 2em; }
    .user-info { color: #666; font-size: 1em; }
    .user-info a { color: #007bff; text-decoration: none; margin-left: 1em; }
    .user-info a:hover { text-decoration: underline; }
    .card { background: #fff; border-radius: 8px; box-shadow: 0 2px 12px #0001; padding: 2em; margin-bottom: 2em; }
    .card h3 { margin-top: 0; }
    #add-product-form input, #add-product-form textarea { width: 100%; margin-bottom: 1em; padding: 0.7em; border: 1px solid #ddd; border-radius: 4px; font-size: 1em; }
    #add-product-form button { background: #007bff; color: #fff; border: none; padding: 0.7em 2em; border-radius: 4px; font-size: 1em; cursor: pointer; transition: background 0.2s; }
    #add-product-form button:hover { background: #0056b3; }
    table { width: 100%; border-collapse: collapse; margin-top: 1em; background: #fff; }
    th, td { padding: 0.8em 0.5em; text-align: center; border-bottom: 1px solid #eee; }
    th { background: #f2f2f2; }
    tr:hover { background: #f9f9f9; }
    .pagination { display: flex; justify-content: center; align-items: center; gap: 1em; margin: 1.5em 0 0 0; }
    .pagination button { background: #eee; border: none; border-radius: 4px; padding: 0.5em 1.5em; cursor: pointer; transition: background 0.2s; }
    .pagination button:hover { background: #007bff; color: #fff; }
    .loading { text-align: center; color: #888; margin: 2em 0; }
    .empty-tip { text-align: center; color: #bbb; margin: 2em 0; }
    .action-btns a, .action-btns button { margin: 0 0.3em; }
    .action-btns a { color: #007bff; text-decoration: none; }
    .action-btns a:hover { text-decoration: underline; }
    .action-btns button { background: #ff4d4f; color: #fff; border: none; border-radius: 3px; padding: 0.3em 1em; cursor: pointer; transition: background 0.2s; }
    .action-btns button:hover { background: #d9363e; }
  </style>
</head>
<body>

  <div class="sidebar">
    <h2>发卡后台</h2>
    <ul>
      <li><a href="#">商品管理</a></li>
      <li><a href="#">订单管理</a></li>
      <li><a href="#">系统设置</a></li>
    </ul>
  </div>

  <div class="main">
    <div class="header">
      <h1>商品管理</h1>
      <div class="user-info">管理员 | <a href="#">退出</a></div>
    </div>

    <div class="card">
      <h3>添加新商品</h3>
      <form id="add-product-form">
        <input type="text" placeholder="商品名称" required>
        <input type="number" placeholder="价格" step="0.01" required>
        <input type="number" placeholder="库存" required>
        <textarea placeholder="描述信息"></textarea>
        <button type="submit">提交</button>
      </form>
    </div>

    <div class="card">
      <h3>商品列表</h3>
      <div class="loading" id="loading">加载中...</div>
      <table id="product-table" style="display:none;">
        <thead>
          <tr>
            <th>ID</th>
            <th>商品名称</th>
            <th>价格</th>
            <th>库存</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody id="product-list"></tbody>
      </table>
      <div class="empty-tip" id="empty-tip" style="display:none;">暂无商品</div>
      <div class="pagination">
        <button onclick="prevPage()">上一页</button>
        <span id="page-info">第 1 页</span>
        <button onclick="nextPage()">下一页</button>
      </div>
    </div>
  </div>

  <script>
    let currentPage = 1;
    const limit = 10;

    function loadProducts(page = 1) {
      document.getElementById('loading').style.display = '';
      document.getElementById('product-table').style.display = 'none';
      document.getElementById('empty-tip').style.display = 'none';
      fetch(`http://localhost:5000/api/products?page=${page}&limit=${limit}`)
        .then(res => res.json())
        .then(data => {
          document.getElementById('loading').style.display = 'none';
          const tbody = document.getElementById('product-list');
          tbody.innerHTML = '';
          if (!data.items || data.items.length === 0) {
            document.getElementById('empty-tip').style.display = '';
            document.getElementById('product-table').style.display = 'none';
          } else {
            data.items.forEach(p => {
              tbody.innerHTML += `
                <tr>
                  <td>${p.id}</td>
                  <td>${p.name}</td>
                  <td>￥${p.price}</td>
                  <td>${p.stock}</td>
                  <td class="action-btns">
                    <a href="edit_product.html?id=${p.id}">编辑</a>
                    <button onclick="deleteProduct(${p.id})">删除</button>
                  </td>
                </tr>
              `;
            });
            document.getElementById('product-table').style.display = '';
            document.getElementById('empty-tip').style.display = 'none';
          }
          document.getElementById('page-info').innerText = `第 ${data.page} 页`;
          currentPage = data.page;
        })
        .catch(() => {
          document.getElementById('loading').style.display = 'none';
          document.getElementById('empty-tip').style.display = '';
          document.getElementById('empty-tip').innerText = '加载失败，请稍后重试';
        });
    }

    function prevPage() {
      if (currentPage > 1) {
        loadProducts(currentPage - 1);
      }
    }

    function nextPage() {
      loadProducts(currentPage + 1);
    }

    document.getElementById('add-product-form').addEventListener('submit', function (e) {
      e.preventDefault();
      const form = e.target;
      const data = {
        name: form[0].value,
        price: form[1].value,
        stock: form[2].value,
        description: form[3].value
      };
      fetch('http://localhost:5000/api/products', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      }).then(res => res.json())
        .then(() => {
          form.reset();
          loadProducts();
        });
    });

    function deleteProduct(id) {
      if (!confirm('确认删除该商品？')) return;
      fetch(`http://localhost:5000/api/products/${id}`, {
        method: 'DELETE'
      }).then(() => loadProducts());
    }

    loadProducts();
  </script>

</body>
</html>
