<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>后台管理</title>
  <link rel="stylesheet" href="../../static/style.css" />
  <style>
    body { background: #f6f8fa; }
    .sidebar { background: #222; color: #fff; width: 200px; min-height: 100vh; float: left; padding: 2em 1em; box-sizing: border-box; }
    .sidebar h2 { font-size: 1.5em; margin-bottom: 1em; }
    .sidebar ul { list-style: none; padding: 0; }
    .sidebar li { margin: 1em 0; }
    .sidebar a { color: #fff; text-decoration: none; transition: color 0.2s; }
    .sidebar a:hover { color: #ffd700; }
    .main { margin-left: 220px; padding: 2em 3vw; }
    .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 2em; }
    .header h1 { margin: 0; font-size: 2em; }
    .user-info { color: #666; font-size: 1em; }
    .user-info a { color: #007bff; text-decoration: none; margin-left: 1em; }
    .user-info a:hover { text-decoration: underline; }
    .card { background: #fff; border-radius: 8px; box-shadow: 0 2px 12px #0001; padding: 2em; margin-bottom: 2em; }
    .card h3 { margin-top: 0; }
    #add-product-form input, #add-product-form textarea { width: 100%; margin-bottom: 1em; padding: 0.7em; border: 1px solid #ddd; border-radius: 4px; font-size: 1em; }
    #add-product-form button { background: #007bff; color: #fff; border: none; padding: 0.7em 2em; border-radius: 4px; font-size: 1em; cursor: pointer; transition: background 0.2s; }
    #add-product-form button:hover { background: #0056b3; }
    table { width: 100%; border-collapse: collapse; margin-top: 1em; background: #fff; }
    th, td { padding: 0.8em 0.5em; text-align: center; border-bottom: 1px solid #eee; }
    th { background: #f2f2f2; }
    tr:hover { background: #f9f9f9; }
    .pagination { display: flex; justify-content: center; align-items: center; gap: 1em; margin: 1.5em 0 0 0; }
    .pagination button { background: #eee; border: none; border-radius: 4px; padding: 0.5em 1.5em; cursor: pointer; transition: background 0.2s; }
    .pagination button:hover { background: #007bff; color: #fff; }
    .loading { text-align: center; color: #888; margin: 2em 0; }
    .empty-tip { text-align: center; color: #bbb; margin: 2em 0; }
    .action-btns { display: flex; gap: 0.5rem; justify-content: center; }
    .action-btns .btn { padding: 0.25rem 0.75rem; font-size: 0.875rem; }
    .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem; }
    .product-info { display: flex; align-items: center; gap: 0.5rem; }
    .product-thumb { width: 40px; height: 40px; object-fit: cover; border-radius: 4px; }
    .sidebar a.active { background: #333; color: #ffd700; padding: 0.5rem; border-radius: 4px; }
    .tab-content { display: block; }
    .btn { display: inline-flex; align-items: center; justify-content: center; padding: 0.5rem 1rem; border: 1px solid #ddd; border-radius: 4px; background: #fff; color: #333; text-decoration: none; cursor: pointer; transition: all 0.2s; }
    .btn:hover { background: #f8f9fa; }
    .btn-primary { background: #007bff; color: white; border-color: #007bff; }
    .btn-primary:hover { background: #0056b3; }
    .btn-danger { background: #dc3545; color: white; border-color: #dc3545; }
    .btn-danger:hover { background: #c82333; }
    .btn-outline { background: transparent; }
    .btn-sm { padding: 0.25rem 0.5rem; font-size: 0.875rem; }
  </style>
</head>
<body>

  <div class="sidebar">
    <h2>发卡后台</h2>
    <ul>
      <li><a href="#">商品管理</a></li>
      <li><a href="#">订单管理</a></li>
      <li><a href="#">系统设置</a></li>
    </ul>
  </div>

  <div class="main">
    <div class="header">
      <h1 id="page-title">商品管理</h1>
      <div class="user-info">加载中... <a href="#" onclick="logout()">退出</a></div>
    </div>

    <!-- 商品管理标签页 -->
    <div id="products-content" class="tab-content">
      <div class="card">
        <h3>添加新商品</h3>
        <form id="add-product-form">
          <div class="form-row">
            <input type="text" name="name" placeholder="商品名称" required />
            <input type="number" name="price" step="0.01" placeholder="价格" required />
          </div>
          <div class="form-row">
            <input type="number" name="stock" placeholder="库存" required />
            <input type="url" name="image" placeholder="商品图片URL (可选)" />
          </div>
          <textarea name="description" placeholder="商品描述" rows="3"></textarea>
          <button type="submit">添加商品</button>
        </form>
      </div>

      <div class="card">
        <h3>商品列表</h3>
        <div id="product-list-container">
          <table id="product-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>商品信息</th>
                <th>价格</th>
                <th>库存</th>
                <th>创建时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody id="product-list"></tbody>
          </table>
        </div>
        <div class="pagination">
          <button id="prev-btn" onclick="prevPage()">上一页</button>
          <span id="page-info">第 1 页</span>
          <button id="next-btn" onclick="nextPage()">下一页</button>
        </div>
      </div>
    </div>

    <!-- 订单管理标签页 -->
    <div id="orders-content" class="tab-content" style="display: none;">
      <div class="card">
        <h3>订单管理</h3>
        <div id="orders-list-container">
          <p>订单管理功能开发中...</p>
        </div>
      </div>
    </div>

    <!-- 系统设置标签页 -->
    <div id="settings-content" class="tab-content" style="display: none;">
      <div class="card">
        <h3>系统设置</h3>
        <div id="settings-container">
          <p>系统设置功能开发中...</p>
        </div>
      </div>
    </div>
  </div>

  <!-- 引入通用组件 -->
  <script src="../../static/common.js"></script>

  <script>
    let currentPage = 1;
    const limit = 10;
    let currentTab = 'products'; // 当前选中的标签页

    // 页面初始化
    document.addEventListener('DOMContentLoaded', async () => {
      // 检查登录状态
      await checkLoginStatus();

      // 加载产品列表
      loadProducts();

      // 绑定标签页切换事件
      bindTabEvents();
    });

    // 检查登录状态
    async function checkLoginStatus() {
      try {
        const user = await FakaMall.API.getCurrentUser();
        if (!user || !user.is_admin) {
          FakaMall.UI.showToast('请先登录管理员账户', 'warning');
          setTimeout(() => {
            window.location.href = 'login.html';
          }, 2000);
          return;
        }

        // 显示用户信息
        document.querySelector('.user-info').innerHTML = `
          欢迎，${user.username}
          <a href="#" onclick="logout()">退出登录</a>
        `;
      } catch (error) {
        console.error('检查登录状态失败:', error);
        window.location.href = 'login.html';
      }
    }

    // 退出登录
    async function logout() {
      try {
        await FakaMall.API.logout();
        FakaMall.UI.showToast('已退出登录', 'success');
        setTimeout(() => {
          window.location.href = 'login.html';
        }, 1000);
      } catch (error) {
        console.error('退出登录失败:', error);
        window.location.href = 'login.html';
      }
    }

    // 绑定标签页事件
    function bindTabEvents() {
      document.querySelectorAll('.sidebar a').forEach(link => {
        link.addEventListener('click', (e) => {
          e.preventDefault();
          const text = link.textContent;

          if (text.includes('商品管理')) {
            switchTab('products');
          } else if (text.includes('订单管理')) {
            switchTab('orders');
          } else if (text.includes('系统设置')) {
            switchTab('settings');
          }
        });
      });
    }

    // 切换标签页
    function switchTab(tab) {
      currentTab = tab;

      // 更新侧边栏激活状态
      document.querySelectorAll('.sidebar a').forEach(link => {
        link.classList.remove('active');
      });

      // 隐藏所有内容区域
      document.querySelectorAll('.tab-content').forEach(content => {
        content.style.display = 'none';
      });

      // 显示对应的内容区域
      const targetContent = document.getElementById(`${tab}-content`);
      if (targetContent) {
        targetContent.style.display = 'block';
      }

      // 加载对应的数据
      switch (tab) {
        case 'products':
          loadProducts();
          break;
        case 'orders':
          loadOrders();
          break;
        case 'settings':
          loadSettings();
          break;
      }
    }

    // 加载产品列表
    async function loadProducts(page = 1) {
      try {
        FakaMall.UI.showLoading('#product-list-container', '加载产品中...');

        const response = await FakaMall.API.getProducts({ page, limit });
        const products = response.items || [];

        const container = document.getElementById('product-list');

        if (products.length === 0) {
          FakaMall.UI.showEmpty(container, '暂无产品');
          return;
        }

        // 渲染产品列表
        container.innerHTML = products.map(product => `
          <tr>
            <td>${product.id}</td>
            <td>
              <div class="product-info">
                <img src="https://picsum.photos/50/50?random=${product.id}" alt="${product.name}" class="product-thumb">
                <span>${product.name}</span>
              </div>
            </td>
            <td>${FakaMall.DataFormatter.formatPrice(product.price)}</td>
            <td>${FakaMall.DataFormatter.formatStock(product.stock)}</td>
            <td>${FakaMall.DataFormatter.formatDate(product.created_at)}</td>
            <td class="action-btns">
              <button class="btn btn-sm btn-outline" onclick="editProduct(${product.id})">编辑</button>
              <button class="btn btn-sm btn-danger" onclick="deleteProduct(${product.id})">删除</button>
            </td>
          </tr>
        `).join('');

        // 更新分页信息
        updatePagination(response);
        currentPage = page;

      } catch (error) {
        console.error('加载产品失败:', error);
        FakaMall.UI.showError('#product-list-container', '加载产品失败: ' + error.message, 'loadProducts()');
      }
    }

    // 更新分页信息
    function updatePagination(response) {
      const pageInfo = document.getElementById('page-info');
      const prevBtn = document.getElementById('prev-btn');
      const nextBtn = document.getElementById('next-btn');

      pageInfo.textContent = `第 ${response.page} 页，共 ${response.total} 个产品`;
      prevBtn.disabled = !response.has_prev;
      nextBtn.disabled = !response.has_next;
    }

    // 上一页
    function prevPage() {
      if (currentPage > 1) {
        loadProducts(currentPage - 1);
      }
    }

    // 下一页
    function nextPage() {
      loadProducts(currentPage + 1);
    }

    // 添加产品表单提交
    document.getElementById('add-product-form').addEventListener('submit', async function (e) {
      e.preventDefault();

      const formData = new FormData(e.target);
      const productData = {
        name: formData.get('name'),
        price: parseFloat(formData.get('price')),
        stock: parseInt(formData.get('stock')),
        description: formData.get('description')
      };

      try {
        const submitBtn = e.target.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.textContent = '添加中...';

        await FakaMall.API.createProduct(productData);

        FakaMall.UI.showToast('产品添加成功', 'success');
        e.target.reset();
        loadProducts(); // 重新加载产品列表

      } catch (error) {
        console.error('添加产品失败:', error);
        FakaMall.UI.showToast('添加产品失败: ' + error.message, 'error');
      } finally {
        const submitBtn = e.target.querySelector('button[type="submit"]');
        submitBtn.disabled = false;
        submitBtn.textContent = '添加产品';
      }
    });

    // 编辑产品
    function editProduct(id) {
      window.location.href = `edit_product.html?id=${id}`;
    }

    // 删除产品
    async function deleteProduct(id) {
      if (!confirm('确认删除该产品？此操作不可恢复。')) {
        return;
      }

      try {
        await FakaMall.API.deleteProduct(id);
        FakaMall.UI.showToast('产品删除成功', 'success');
        loadProducts(); // 重新加载产品列表
      } catch (error) {
        console.error('删除产品失败:', error);
        FakaMall.UI.showToast('删除产品失败: ' + error.message, 'error');
      }
    }

    // 加载订单列表（占位函数）
    async function loadOrders() {
      FakaMall.UI.showEmpty('#orders-content', '订单管理功能开发中...');
    }

    // 加载系统设置（占位函数）
    async function loadSettings() {
      FakaMall.UI.showEmpty('#settings-content', '系统设置功能开发中...');
    }
  </script>

</body>
</html>
