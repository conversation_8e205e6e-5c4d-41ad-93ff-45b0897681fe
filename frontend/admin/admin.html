<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>后台管理</title>
  <link rel="stylesheet" href="../../static/style.css" />
</head>
<body>

  <div class="sidebar">
    <h2>发卡后台</h2>
    <ul>
      <li><a href="#">商品管理</a></li>
      <li><a href="#">订单管理</a></li>
      <li><a href="#">系统设置</a></li>
    </ul>
  </div>

  <div class="main">
    <div class="header">
      <h1>商品管理</h1>
      <div class="user-info">管理员 | <a href="#">退出</a></div>
    </div>

    <div class="card">
      <h3>添加新商品</h3>
      <form id="add-product-form">
        <input type="text" placeholder="商品名称" required>
        <input type="number" placeholder="价格" step="0.01" required>
        <input type="number" placeholder="库存" required>
        <textarea placeholder="描述信息"></textarea>
        <button type="submit">提交</button>
      </form>
    </div>

    <div class="card">
      <h3>商品列表</h3>
      <div class="loading" id="loading">加载中...</div>
      <table>
        <thead>
          <tr>
            <th>ID</th>
            <th>商品名称</th>
            <th>价格</th>
            <th>库存</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody id="product-list"></tbody>
      </table>
      <div class="pagination">
        <button onclick="prevPage()">上一页</button>
        <span id="page-info">第 1 页</span>
        <button onclick="nextPage()">下一页</button>
      </div>
    </div>
  </div>

  <script>
    let currentPage = 1;
    const limit = 10;

    function loadProducts(page = 1) {
      fetch(`http://localhost:5000/api/products?page=${page}&limit=${limit}`)
        .then(res => res.json())
        .then(data => {
          const tbody = document.getElementById('product-list');
          tbody.innerHTML = '';
          data.items.forEach(p => {
            tbody.innerHTML += `
              <tr>
                <td>${p.id}</td>
                <td>${p.name}</td>
                <td>￥${p.price}</td>
                <td>${p.stock}</td>
                <td>
                  <a href="edit_product.html?id=${p.id}">编辑</a>
                  <button onclick="deleteProduct(${p.id})">删除</button>
                </td>
              </tr>
            `;
          });
          document.getElementById('page-info').innerText = `第 ${data.page} 页`;
          currentPage = data.page;
        });
    }

    function prevPage() {
      if (currentPage > 1) {
        loadProducts(currentPage - 1);
      }
    }

    function nextPage() {
      loadProducts(currentPage + 1);
    }

    document.getElementById('add-product-form').addEventListener('submit', function (e) {
      e.preventDefault();
      const form = e.target;
      const data = {
        name: form[0].value,
        price: form[1].value,
        stock: form[2].value,
        description: form[3].value
      };
      fetch('http://localhost:5000/api/products', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      }).then(res => res.json())
        .then(() => {
          form.reset();
          loadProducts();
        });
    });

    function deleteProduct(id) {
      if (!confirm('确认删除该商品？')) return;
      fetch(`http://localhost:5000/api/products/${id}`, {
        method: 'DELETE'
      }).then(() => loadProducts());
    }

    loadProducts();
  </script>

</body>
</html>
