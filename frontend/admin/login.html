<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>管理员登录</title>
  <link rel="stylesheet" href="../../static/style.css" />
  <style>
    body {
      background: linear-gradient(120deg, #f6d365 0%, #fda085 100%);
      min-height: 100vh;
      margin: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .login-container {
      background: #fff;
      border-radius: 10px;
      box-shadow: 0 4px 24px #0002;
      padding: 2.5em 2em 2em 2em;
      max-width: 350px;
      width: 100%;
      margin: 2em auto;
      text-align: center;
    }
    .login-container h2 {
      margin-bottom: 1.5em;
      color: #333;
      font-weight: 600;
    }
    #login-form input {
      width: 100%;
      padding: 0.8em;
      margin-bottom: 1.2em;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 1em;
      box-sizing: border-box;
    }
    #login-form button {
      width: 100%;
      background: #007bff;
      color: #fff;
      border: none;
      border-radius: 4px;
      padding: 0.8em;
      font-size: 1.1em;
      cursor: pointer;
      transition: background 0.2s;
      font-weight: 500;
    }
    #login-form button:hover {
      background: #0056b3;
    }
    #error-message {
      color: #ff4d4f;
      margin-top: 0.5em;
      min-height: 1.5em;
      font-size: 1em;
    }
  </style>
</head>
<body>

  <div class="login-container">
    <h2>管理员登录</h2>
    <form id="login-form">
      <input type="text" id="username" placeholder="用户名" required />
      <input type="password" id="password" placeholder="密码" required />
      <button type="submit">登录</button>
    </form>
    <p id="error-message" style="color: red;"></p>
  </div>

  <script>
    document.getElementById('login-form').addEventListener('submit', function (e) {
      e.preventDefault();
      const username = document.getElementById('username').value;
      const password = document.getElementById('password').value;

      fetch('http://localhost:5000/api/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, password })
      })
      .then(res => res.json())
      .then(data => {
        if (data.success) {
          window.location.href = 'admin.html';
        } else {
          document.getElementById('error-message').innerText = '用户名或密码错误';
        }
      });
    });
  </script>

</body>
</html>