<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>管理员登录</title>
  <link rel="stylesheet" href="../../static/style.css" />
  <style>
    body {
      background: linear-gradient(120deg, #f6d365 0%, #fda085 100%);
      min-height: 100vh;
      margin: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .login-container {
      background: #fff;
      border-radius: 10px;
      box-shadow: 0 4px 24px #0002;
      padding: 2.5em 2em 2em 2em;
      max-width: 350px;
      width: 100%;
      margin: 2em auto;
      text-align: center;
    }
    .login-container h2 {
      margin-bottom: 1.5em;
      color: #333;
      font-weight: 600;
    }
    #login-form input {
      width: 100%;
      padding: 0.8em;
      margin-bottom: 1.2em;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 1em;
      box-sizing: border-box;
    }
    #login-form button {
      width: 100%;
      background: #007bff;
      color: #fff;
      border: none;
      border-radius: 4px;
      padding: 0.8em;
      font-size: 1.1em;
      cursor: pointer;
      transition: background 0.2s;
      font-weight: 500;
    }
    #login-form button:hover {
      background: #0056b3;
    }
    #error-message {
      color: #ff4d4f;
      margin-top: 0.5em;
      min-height: 1.5em;
      font-size: 1em;
    }
  </style>
</head>
<body>

  <div class="login-container">
    <h2>管理员登录</h2>
    <form id="login-form">
      <input type="text" id="username" placeholder="用户名" required />
      <input type="password" id="password" placeholder="密码" required />
      <button type="submit">登录</button>
    </form>
    <p id="error-message" style="color: red;"></p>
  </div>

  <!-- 引入通用组件 -->
  <script src="../../static/common.js"></script>

  <script>
    document.getElementById('login-form').addEventListener('submit', async function (e) {
      e.preventDefault();

      const username = document.getElementById('username').value;
      const password = document.getElementById('password').value;
      const errorMessage = document.getElementById('error-message');
      const submitBtn = e.target.querySelector('button[type="submit"]');

      // 清除之前的错误信息
      errorMessage.textContent = '';

      // 显示加载状态
      submitBtn.disabled = true;
      submitBtn.textContent = '登录中...';

      try {
        // 使用统一的API调用
        const response = await FakaMall.API.login({ username, password });

        if (response.success || response.message === '登录成功') {
          // 登录成功，跳转到管理后台
          FakaMall.UI.showToast('登录成功', 'success');
          setTimeout(() => {
            window.location.href = 'admin.html';
          }, 1000);
        } else {
          throw new Error(response.error || '登录失败');
        }
      } catch (error) {
        console.error('登录失败:', error);
        errorMessage.textContent = error.message || '用户名或密码错误';
        FakaMall.UI.showToast('登录失败: ' + (error.message || '用户名或密码错误'), 'error');
      } finally {
        // 恢复按钮状态
        submitBtn.disabled = false;
        submitBtn.textContent = '登录';
      }
    });
  </script>

</body>
</html>