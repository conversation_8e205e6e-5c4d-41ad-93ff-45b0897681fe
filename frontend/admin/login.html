<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>管理员登录</title>
  <link rel="stylesheet" href="../../static/style.css" />
</head>
<body>

  <div class="login-container">
    <h2>管理员登录</h2>
    <form id="login-form">
      <input type="text" id="username" placeholder="用户名" required />
      <input type="password" id="password" placeholder="密码" required />
      <button type="submit">登录</button>
    </form>
    <p id="error-message" style="color: red;"></p>
  </div>

  <script>
    document.getElementById('login-form').addEventListener('submit', function (e) {
      e.preventDefault();
      const username = document.getElementById('username').value;
      const password = document.getElementById('password').value;

      fetch('http://localhost:5000/api/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, password })
      })
      .then(res => res.json())
      .then(data => {
        if (data.success) {
          window.location.href = 'admin.html';
        } else {
          document.getElementById('error-message').innerText = '用户名或密码错误';
        }
      });
    });
  </script>

</body>
</html>