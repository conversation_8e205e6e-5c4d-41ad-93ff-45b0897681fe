<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>订单管理 - 发卡商城管理后台</title>
  <link rel="stylesheet" href="../../static/style.css">
  <style>
    body { font-family: Arial, sans-serif; margin: 0; background: #f5f5f5; }
    .container { display: flex; min-height: 100vh; }
    .sidebar { width: 250px; background: #2c3e50; color: white; padding: 1rem; }
    .sidebar h2 { margin-bottom: 1rem; color: #ecf0f1; }
    .sidebar a { display: block; color: #bdc3c7; text-decoration: none; padding: 0.75rem; margin: 0.25rem 0; border-radius: 4px; transition: background 0.2s; }
    .sidebar a:hover, .sidebar a.active { background: #34495e; color: white; }
    .main { flex: 1; padding: 2rem; }
    .header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem; }
    .header h1 { margin: 0; color: #2c3e50; }
    .user-info { color: #7f8c8d; }
    .user-info a { color: #e74c3c; text-decoration: none; }
    .card { background: white; border-radius: 8px; padding: 1.5rem; margin-bottom: 1.5rem; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    .card h3 { margin-top: 0; color: #2c3e50; }
    .filters { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1rem; }
    .filters select, .filters input { padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px; }
    .btn { padding: 0.5rem 1rem; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; transition: background 0.2s; }
    .btn-primary { background: #3498db; color: white; }
    .btn-primary:hover { background: #2980b9; }
    .btn-success { background: #27ae60; color: white; }
    .btn-success:hover { background: #229954; }
    .btn-warning { background: #f39c12; color: white; }
    .btn-warning:hover { background: #e67e22; }
    .btn-danger { background: #e74c3c; color: white; }
    .btn-danger:hover { background: #c0392b; }
    .btn-sm { padding: 0.25rem 0.5rem; font-size: 0.875rem; }
    table { width: 100%; border-collapse: collapse; margin-top: 1rem; }
    th, td { padding: 0.75rem; text-align: left; border-bottom: 1px solid #eee; }
    th { background: #f8f9fa; font-weight: 600; }
    .status-badge { padding: 0.25rem 0.5rem; border-radius: 12px; font-size: 0.75rem; font-weight: 600; }
    .status-pending { background: #fff3cd; color: #856404; }
    .status-processing { background: #d1ecf1; color: #0c5460; }
    .status-shipped { background: #d4edda; color: #155724; }
    .status-delivered { background: #d1ecf1; color: #0c5460; }
    .status-cancelled { background: #f8d7da; color: #721c24; }
    .status-refunded { background: #e2e3e5; color: #383d41; }
    .pagination { display: flex; justify-content: center; align-items: center; gap: 1rem; margin-top: 1rem; }
    .pagination button { padding: 0.5rem 1rem; border: 1px solid #ddd; background: white; border-radius: 4px; cursor: pointer; }
    .pagination button:hover:not(:disabled) { background: #f8f9fa; }
    .pagination button:disabled { opacity: 0.5; cursor: not-allowed; }
    .order-details { background: #f8f9fa; padding: 1rem; border-radius: 4px; margin-top: 0.5rem; }
    .loading, .empty-state, .error-state { text-align: center; padding: 2rem; color: #6c757d; }
    .spinner { width: 24px; height: 24px; border: 2px solid #e5e7eb; border-top: 2px solid #3b82f6; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 1rem; }
    @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
  </style>
</head>
<body>
  <div class="container">
    <div class="sidebar">
      <h2>管理后台</h2>
      <a href="admin.html">商品管理</a>
      <a href="orders.html" class="active">订单管理</a>
      <a href="#" onclick="alert('功能开发中')">系统设置</a>
    </div>

    <div class="main">
      <div class="header">
        <h1>订单管理</h1>
        <div class="user-info">管理员 | <a href="#" onclick="logout()">退出</a></div>
      </div>

      <!-- 过滤器 -->
      <div class="card">
        <h3>订单筛选</h3>
        <div class="filters">
          <select id="status-filter">
            <option value="">全部状态</option>
            <option value="pending">待处理</option>
            <option value="processing">处理中</option>
            <option value="shipped">已发货</option>
            <option value="delivered">已完成</option>
            <option value="cancelled">已取消</option>
            <option value="refunded">已退款</option>
          </select>
          <input type="date" id="start-date" placeholder="开始日期">
          <input type="date" id="end-date" placeholder="结束日期">
          <input type="number" id="min-amount" placeholder="最小金额">
          <input type="number" id="max-amount" placeholder="最大金额">
          <button class="btn btn-primary" onclick="applyFilters()">筛选</button>
          <button class="btn btn-warning" onclick="clearFilters()">清除</button>
        </div>
      </div>

      <!-- 订单列表 -->
      <div class="card">
        <h3>订单列表</h3>
        <div id="orders-container">
          <div class="loading">
            <div class="spinner"></div>
            <p>加载订单中...</p>
          </div>
        </div>
        
        <div class="pagination" id="pagination" style="display: none;">
          <button id="prev-btn" onclick="prevPage()">上一页</button>
          <span id="page-info">第 1 页</span>
          <button id="next-btn" onclick="nextPage()">下一页</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 引入通用组件 -->
  <script src="../../static/common.js"></script>
  
  <script>
    let currentPage = 1;
    let currentFilters = {};
    const limit = 10;

    // 页面初始化
    document.addEventListener('DOMContentLoaded', async () => {
      await checkLoginStatus();
      loadOrders();
    });

    // 检查登录状态
    async function checkLoginStatus() {
      try {
        const user = await FakaMall.API.getCurrentUser();
        if (!user || !user.is_admin) {
          FakaMall.UI.showToast('请先登录管理员账户', 'warning');
          setTimeout(() => {
            window.location.href = 'login.html';
          }, 2000);
          return;
        }
        
        document.querySelector('.user-info').innerHTML = `
          ${user.username} | <a href="#" onclick="logout()">退出登录</a>
        `;
      } catch (error) {
        console.error('检查登录状态失败:', error);
        window.location.href = 'login.html';
      }
    }

    // 退出登录
    async function logout() {
      try {
        await FakaMall.API.logout();
        FakaMall.UI.showToast('已退出登录', 'success');
        setTimeout(() => {
          window.location.href = 'login.html';
        }, 1000);
      } catch (error) {
        console.error('退出登录失败:', error);
        window.location.href = 'login.html';
      }
    }

    // 加载订单列表
    async function loadOrders(page = 1) {
      try {
        const container = document.getElementById('orders-container');
        const pagination = document.getElementById('pagination');
        
        // 显示加载状态
        container.innerHTML = `
          <div class="loading">
            <div class="spinner"></div>
            <p>加载订单中...</p>
          </div>
        `;
        pagination.style.display = 'none';
        
        // 构建查询参数
        const params = {
          page,
          limit,
          ...currentFilters
        };
        
        // 获取订单数据
        const response = await FakaMall.API.getOrders(params);
        const orders = response.items || [];
        
        if (orders.length === 0) {
          container.innerHTML = `
            <div class="empty-state">
              <p>📭 暂无订单数据</p>
            </div>
          `;
          return;
        }
        
        // 渲染订单列表
        container.innerHTML = `
          <table>
            <thead>
              <tr>
                <th>订单ID</th>
                <th>产品信息</th>
                <th>数量</th>
                <th>总价</th>
                <th>状态</th>
                <th>创建时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              ${orders.map(order => createOrderRow(order)).join('')}
            </tbody>
          </table>
        `;
        
        // 更新分页
        updatePagination(response);
        currentPage = page;
        
      } catch (error) {
        console.error('加载订单失败:', error);
        document.getElementById('orders-container').innerHTML = `
          <div class="error-state">
            <p>❌ 加载订单失败: ${error.message}</p>
            <button class="btn btn-primary" onclick="loadOrders()">重试</button>
          </div>
        `;
      }
    }

    // 创建订单行
    function createOrderRow(order) {
      const statusClass = `status-${order.status}`;
      const statusText = FakaMall.DataFormatter.formatOrderStatus(order.status);
      
      return `
        <tr>
          <td>#${order.id}</td>
          <td>
            <div>
              <strong>${order.product_name || '未知产品'}</strong>
              ${order.notes ? `<br><small style="color: #6c757d;">${order.notes}</small>` : ''}
            </div>
          </td>
          <td>${order.quantity}</td>
          <td>${FakaMall.DataFormatter.formatPrice(order.total_price)}</td>
          <td><span class="status-badge ${statusClass}">${statusText}</span></td>
          <td>${FakaMall.DataFormatter.formatDate(order.created_at)}</td>
          <td>
            <button class="btn btn-sm btn-primary" onclick="viewOrder(${order.id})">查看</button>
            ${order.status === 'pending' ? `
              <button class="btn btn-sm btn-success" onclick="updateOrderStatus(${order.id}, 'processing')">处理</button>
              <button class="btn btn-sm btn-danger" onclick="cancelOrder(${order.id})">取消</button>
            ` : ''}
            ${order.status === 'processing' ? `
              <button class="btn btn-sm btn-success" onclick="updateOrderStatus(${order.id}, 'shipped')">发货</button>
            ` : ''}
          </td>
        </tr>
      `;
    }

    // 更新分页信息
    function updatePagination(response) {
      const pagination = document.getElementById('pagination');
      const pageInfo = document.getElementById('page-info');
      const prevBtn = document.getElementById('prev-btn');
      const nextBtn = document.getElementById('next-btn');
      
      pageInfo.textContent = `第 ${response.page} 页，共 ${response.total} 个订单`;
      prevBtn.disabled = !response.has_prev;
      nextBtn.disabled = !response.has_next;
      pagination.style.display = 'flex';
    }

    // 应用过滤器
    function applyFilters() {
      currentFilters = {
        status: document.getElementById('status-filter').value,
        start_date: document.getElementById('start-date').value,
        end_date: document.getElementById('end-date').value,
        min_amount: document.getElementById('min-amount').value,
        max_amount: document.getElementById('max-amount').value
      };
      
      // 移除空值
      Object.keys(currentFilters).forEach(key => {
        if (!currentFilters[key]) {
          delete currentFilters[key];
        }
      });
      
      currentPage = 1;
      loadOrders(1);
    }

    // 清除过滤器
    function clearFilters() {
      document.getElementById('status-filter').value = '';
      document.getElementById('start-date').value = '';
      document.getElementById('end-date').value = '';
      document.getElementById('min-amount').value = '';
      document.getElementById('max-amount').value = '';
      
      currentFilters = {};
      currentPage = 1;
      loadOrders(1);
    }

    // 上一页
    function prevPage() {
      if (currentPage > 1) {
        loadOrders(currentPage - 1);
      }
    }

    // 下一页
    function nextPage() {
      loadOrders(currentPage + 1);
    }

    // 查看订单详情
    function viewOrder(orderId) {
      alert(`查看订单 #${orderId} 的详情功能开发中...`);
    }

    // 更新订单状态
    async function updateOrderStatus(orderId, newStatus) {
      try {
        await FakaMall.API.request(`/orders/${orderId}/status`, {
          method: 'PUT',
          body: JSON.stringify({ status: newStatus })
        });
        
        FakaMall.UI.showToast('订单状态更新成功', 'success');
        loadOrders(currentPage); // 重新加载当前页
      } catch (error) {
        console.error('更新订单状态失败:', error);
        FakaMall.UI.showToast('更新订单状态失败: ' + error.message, 'error');
      }
    }

    // 取消订单
    async function cancelOrder(orderId) {
      if (!confirm('确定要取消这个订单吗？')) {
        return;
      }
      
      try {
        const reason = prompt('请输入取消原因（可选）:') || '管理员取消';
        
        await FakaMall.API.request(`/orders/${orderId}/cancel`, {
          method: 'POST',
          body: JSON.stringify({ reason })
        });
        
        FakaMall.UI.showToast('订单已取消', 'success');
        loadOrders(currentPage); // 重新加载当前页
      } catch (error) {
        console.error('取消订单失败:', error);
        FakaMall.UI.showToast('取消订单失败: ' + error.message, 'error');
      }
    }
  </script>
</body>
</html>
