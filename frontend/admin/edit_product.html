<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>编辑商品</title>
  <link rel="stylesheet" href="../../../static/style.css" />
  <style>
    body { background: #f6f8fa; }
    .sidebar { background: #222; color: #fff; width: 200px; min-height: 100vh; float: left; padding: 2em 1em; box-sizing: border-box; }
    .sidebar h2 { font-size: 1.5em; margin-bottom: 1em; }
    .sidebar ul { list-style: none; padding: 0; }
    .sidebar li { margin: 1em 0; }
    .sidebar a { color: #fff; text-decoration: none; transition: color 0.2s; }
    .sidebar a:hover { color: #ffd700; }
    .main { margin-left: 220px; padding: 2em 3vw; }
    .header { margin-bottom: 2em; }
    .header h1 { margin: 0; font-size: 2em; }
    .card { background: #fff; border-radius: 8px; box-shadow: 0 2px 12px #0001; padding: 2em; max-width: 500px; margin: 0 auto; }
    #edit-product-form input, #edit-product-form textarea { width: 100%; margin-bottom: 1.2em; padding: 0.7em; border: 1px solid #ddd; border-radius: 4px; font-size: 1em; }
    #edit-product-form textarea { min-height: 80px; resize: vertical; }
    #edit-product-form button { background: #007bff; color: #fff; border: none; padding: 0.7em 2em; border-radius: 4px; font-size: 1em; cursor: pointer; transition: background 0.2s; }
    #edit-product-form button:hover { background: #0056b3; }
    .loading, .error-tip { text-align: center; color: #888; margin: 1em 0; }
    .error-tip { color: #ff4d4f; }
  </style>
</head>
<body>

  <div class="sidebar">
    <h2>发卡后台</h2>
    <ul>
      <li><a href="../admin.html">返回商品管理</a></li>
    </ul>
  </div>

  <div class="main">
    <div class="header">
      <h1>编辑商品</h1>
    </div>

    <div class="card">
      <div class="loading" id="loading">加载中...</div>
      <div class="error-tip" id="error-tip" style="display:none;"></div>
      <form id="edit-product-form" style="display:none;">
        <input type="text" id="name" name="name" placeholder="商品名称" required>
        <input type="number" id="price" name="price" placeholder="价格" step="0.01" required>
        <input type="number" id="stock" name="stock" placeholder="库存" required>
        <input type="url" id="image" name="image" placeholder="商品图片URL (可选)">
        <textarea id="description" name="description" placeholder="商品描述" rows="4"></textarea>
        <button type="submit">保存修改</button>
        <button type="button" onclick="window.location.href='admin.html'" style="background: #6c757d; margin-left: 1rem;">取消</button>
      </form>
    </div>
  </div>

  <!-- 引入通用组件 -->
  <script src="../../static/common.js"></script>

  <script>
    const urlParams = new URLSearchParams(window.location.search);
    const productId = parseInt(urlParams.get('id'));

    let currentProduct = null;

    // 页面初始化
    document.addEventListener('DOMContentLoaded', async () => {
      if (!productId || isNaN(productId)) {
        FakaMall.UI.showError('#error-tip', '无效的产品ID');
        return;
      }

      await loadProduct();
    });

    // 加载产品信息
    async function loadProduct() {
      const loading = document.getElementById('loading');
      const errorTip = document.getElementById('error-tip');
      const form = document.getElementById('edit-product-form');

      try {
        loading.style.display = 'block';
        errorTip.style.display = 'none';
        form.style.display = 'none';

        // 获取产品信息
        currentProduct = await FakaMall.API.getProduct(productId);

        // 填充表单
        document.getElementById('name').value = currentProduct.name || '';
        document.getElementById('price').value = currentProduct.price || '';
        document.getElementById('stock').value = currentProduct.stock || '';
        document.getElementById('description').value = currentProduct.description || '';

        // 显示表单
        loading.style.display = 'none';
        form.style.display = 'block';

      } catch (error) {
        console.error('加载产品失败:', error);
        loading.style.display = 'none';
        errorTip.style.display = 'block';
        errorTip.textContent = '产品加载失败: ' + error.message;
      }
    }

    // 表单提交处理
    document.getElementById('edit-product-form').addEventListener('submit', async function (e) {
      e.preventDefault();

      const formData = new FormData(e.target);
      const productData = {
        name: document.getElementById('name').value.trim(),
        price: parseFloat(document.getElementById('price').value),
        stock: parseInt(document.getElementById('stock').value),
        description: document.getElementById('description').value.trim()
      };

      // 验证数据
      if (!productData.name) {
        FakaMall.UI.showToast('请输入产品名称', 'error');
        return;
      }

      if (isNaN(productData.price) || productData.price <= 0) {
        FakaMall.UI.showToast('请输入有效的价格', 'error');
        return;
      }

      if (isNaN(productData.stock) || productData.stock < 0) {
        FakaMall.UI.showToast('请输入有效的库存数量', 'error');
        return;
      }

      try {
        const submitBtn = e.target.querySelector('button[type="submit"]');
        submitBtn.disabled = true;
        submitBtn.textContent = '保存中...';

        // 更新产品
        await FakaMall.API.updateProduct(productId, productData);

        FakaMall.UI.showToast('产品更新成功', 'success');

        // 延迟跳转，让用户看到成功提示
        setTimeout(() => {
          window.location.href = 'admin.html';
        }, 1500);

      } catch (error) {
        console.error('更新产品失败:', error);
        FakaMall.UI.showToast('更新产品失败: ' + error.message, 'error');

        // 恢复按钮状态
        const submitBtn = e.target.querySelector('button[type="submit"]');
        submitBtn.disabled = false;
        submitBtn.textContent = '保存修改';
      }
    });
  </script>

</body>
</html>
