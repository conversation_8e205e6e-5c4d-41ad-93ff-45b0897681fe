<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>编辑商品</title>
  <link rel="stylesheet" href="../../../static/style.css" />
  <style>
    body { background: #f6f8fa; }
    .sidebar { background: #222; color: #fff; width: 200px; min-height: 100vh; float: left; padding: 2em 1em; box-sizing: border-box; }
    .sidebar h2 { font-size: 1.5em; margin-bottom: 1em; }
    .sidebar ul { list-style: none; padding: 0; }
    .sidebar li { margin: 1em 0; }
    .sidebar a { color: #fff; text-decoration: none; transition: color 0.2s; }
    .sidebar a:hover { color: #ffd700; }
    .main { margin-left: 220px; padding: 2em 3vw; }
    .header { margin-bottom: 2em; }
    .header h1 { margin: 0; font-size: 2em; }
    .card { background: #fff; border-radius: 8px; box-shadow: 0 2px 12px #0001; padding: 2em; max-width: 500px; margin: 0 auto; }
    #edit-product-form input, #edit-product-form textarea { width: 100%; margin-bottom: 1.2em; padding: 0.7em; border: 1px solid #ddd; border-radius: 4px; font-size: 1em; }
    #edit-product-form textarea { min-height: 80px; resize: vertical; }
    #edit-product-form button { background: #007bff; color: #fff; border: none; padding: 0.7em 2em; border-radius: 4px; font-size: 1em; cursor: pointer; transition: background 0.2s; }
    #edit-product-form button:hover { background: #0056b3; }
    .loading, .error-tip { text-align: center; color: #888; margin: 1em 0; }
    .error-tip { color: #ff4d4f; }
  </style>
</head>
<body>

  <div class="sidebar">
    <h2>发卡后台</h2>
    <ul>
      <li><a href="../admin.html">返回商品管理</a></li>
    </ul>
  </div>

  <div class="main">
    <div class="header">
      <h1>编辑商品</h1>
    </div>

    <div class="card">
      <div class="loading" id="loading">加载中...</div>
      <div class="error-tip" id="error-tip" style="display:none;"></div>
      <form id="edit-product-form" style="display:none;">
        <input type="text" id="name" placeholder="商品名称" required>
        <input type="number" id="price" placeholder="价格" step="0.01" required>
        <input type="number" id="stock" placeholder="库存" required>
        <textarea id="description" placeholder="描述信息"></textarea>
        <button type="submit">提交</button>
      </form>
    </div>
  </div>

  <script>
    const urlParams = new URLSearchParams(window.location.search);
    const productId = parseInt(urlParams.get('id'));


    const loading = document.getElementById('loading');
    const errorTip = document.getElementById('error-tip');
    const form = document.getElementById('edit-product-form');

    fetch(`http://localhost:5000/api/products/${productId}`)
      .then(res => {
        if (!res.ok) throw new Error('加载失败');
        return res.json();
      })
      .then(product => {
        loading.style.display = 'none';
        form.style.display = '';
        document.getElementById('name').value = product.name;
        document.getElementById('price').value = product.price;
        document.getElementById('stock').value = product.stock;
        document.getElementById('description').value = product.description;
      })
      .catch(() => {
        loading.style.display = 'none';
        errorTip.style.display = '';
        errorTip.innerText = '商品加载失败，请稍后重试';
      });

    form.addEventListener('submit', function (e) {
      e.preventDefault();
      const data = {
        name: document.getElementById('name').value,
        price: document.getElementById('price').value,
        stock: document.getElementById('stock').value,
        description: document.getElementById('description').value
      };
      loading.style.display = '';
      errorTip.style.display = 'none';
      fetch(`http://localhost:5000/api/products/${productId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })
        .then(res => {
          loading.style.display = 'none';
          if (!res.ok) throw new Error('保存失败');
          return res.json();
        })
        .then(() => {
          alert('修改成功');
          window.location.href = 'admin.html';
        })
        .catch(() => {
          errorTip.style.display = '';
          errorTip.innerText = '保存失败，请检查输入或稍后重试';
        });
    });
  </script>

</body>
</html>
