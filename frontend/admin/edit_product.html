<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>编辑商品</title>
  <link rel="stylesheet" href="../../../static/style.css" />
</head>
<body>

  <div class="sidebar">
    <h2>发卡后台</h2>
    <ul>
      <li><a href="../admin.html">返回商品管理</a></li>
    </ul>
  </div>

  <div class="main">
    <div class="header">
      <h1>编辑商品</h1>
    </div>

    <div class="card">
      <form id="edit-product-form">
        <input type="text" id="name" placeholder="商品名称" required>
        <input type="number" id="price" placeholder="价格" step="0.01" required>
        <input type="number" id="stock" placeholder="库存" required>
        <textarea id="description" placeholder="描述信息"></textarea>
        <button type="submit">提交</button>
      </form>
    </div>
  </div>

  <script>
    const urlParams = new URLSearchParams(window.location.search);
    const productId = parseInt(urlParams.get('id'));

    fetch(`http://localhost:5000/api/products/${productId}`)
      .then(res => res.json())
      .then(product => {
        document.getElementById('name').value = product.name;
        document.getElementById('price').value = product.price;
        document.getElementById('stock').value = product.stock;
        document.getElementById('description').value = product.description;
      });

    document.getElementById('edit-product-form').addEventListener('submit', function (e) {
      e.preventDefault();
      const data = {
        name: document.getElementById('name').value,
        price: document.getElementById('price').value,
        stock: document.getElementById('stock').value,
        description: document.getElementById('description').value
      };
      fetch(`http://localhost:5000/api/products/${productId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      }).then(() => {
        alert('修改成功');
        window.location.href = 'admin.html';
      });
    });
  </script>

</body>
</html>
