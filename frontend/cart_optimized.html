<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>我的购物车 - 发卡商城</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: #333;
    }
    
    .header {
      background: white;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      padding: 1rem 0;
      position: sticky;
      top: 0;
      z-index: 100;
    }
    
    .header-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .logo {
      font-size: 1.5rem;
      font-weight: 700;
      color: #667eea;
      text-decoration: none;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
    
    .nav-links {
      display: flex;
      gap: 2rem;
      align-items: center;
    }
    
    .nav-link {
      color: #666;
      text-decoration: none;
      font-weight: 500;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      transition: all 0.3s ease;
      position: relative;
    }
    
    .nav-link:hover {
      background: #f8f9fa;
      color: #667eea;
    }
    
    .nav-link.active {
      background: #667eea;
      color: white;
    }
    
    .cart-badge {
      background: #dc3545;
      color: white;
      padding: 0.2rem 0.5rem;
      border-radius: 50%;
      font-size: 0.75rem;
      margin-left: 0.5rem;
      min-width: 1.2rem;
      text-align: center;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem 1rem;
    }
    
    .page-header {
      text-align: center;
      color: white;
      margin-bottom: 2rem;
    }
    
    .page-header h1 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
      text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }
    
    .page-header p {
      font-size: 1.1rem;
      opacity: 0.9;
    }
    
    .cart-section {
      background: white;
      border-radius: 20px;
      padding: 2rem;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      margin-bottom: 2rem;
    }
    
    .cart-container {
      display: grid;
      grid-template-columns: 1fr 350px;
      gap: 2rem;
    }
    
    .cart-items {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }
    
    .cart-item {
      display: grid;
      grid-template-columns: 100px 1fr auto auto auto;
      gap: 1rem;
      align-items: center;
      background: #f8f9fa;
      padding: 1.5rem;
      border-radius: 12px;
      border: 1px solid #e9ecef;
      transition: all 0.3s ease;
    }
    
    .cart-item:hover {
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      transform: translateY(-2px);
    }
    
    .item-image img {
      width: 100px;
      height: 100px;
      object-fit: cover;
      border-radius: 8px;
    }
    
    .item-info {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }
    
    .item-name {
      font-size: 1.1rem;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
    
    .item-price {
      color: #667eea;
      font-weight: 500;
      font-size: 0.9rem;
    }
    
    .item-quantity {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      background: white;
      padding: 0.5rem;
      border-radius: 8px;
      border: 1px solid #e9ecef;
    }
    
    .quantity-btn {
      width: 36px;
      height: 36px;
      border: 1px solid #d1d5db;
      background: white;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      font-weight: 600;
      font-size: 1.1rem;
    }
    
    .quantity-btn:hover:not(:disabled) {
      background: #667eea;
      color: white;
      border-color: #667eea;
    }
    
    .quantity-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    .quantity {
      min-width: 2.5rem;
      text-align: center;
      font-weight: 600;
      font-size: 1.1rem;
    }
    
    .item-subtotal {
      font-weight: 700;
      color: #667eea;
      font-size: 1.2rem;
    }
    
    .btn-remove {
      background: none;
      border: none;
      color: #dc3545;
      cursor: pointer;
      padding: 0.75rem;
      border-radius: 8px;
      transition: all 0.2s ease;
      font-size: 1.1rem;
    }
    
    .btn-remove:hover {
      background: #f8d7da;
      transform: scale(1.1);
    }
    
    .cart-summary {
      background: #f8f9fa;
      padding: 2rem;
      border-radius: 16px;
      height: fit-content;
      position: sticky;
      top: 100px;
      border: 1px solid #e9ecef;
    }
    
    .summary-title {
      font-size: 1.3rem;
      font-weight: 600;
      margin-bottom: 1.5rem;
      color: #333;
      text-align: center;
    }
    
    .summary-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.75rem 0;
      border-bottom: 1px solid #e9ecef;
    }
    
    .summary-row:last-of-type {
      border-bottom: 2px solid #667eea;
      font-weight: 700;
      font-size: 1.2rem;
      color: #667eea;
      margin-bottom: 1.5rem;
    }
    
    .summary-actions {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }
    
    .btn {
      padding: 1rem 1.5rem;
      border: none;
      border-radius: 10px;
      font-weight: 600;
      text-decoration: none;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 1rem;
    }
    
    .btn-primary {
      background: #667eea;
      color: white;
    }
    
    .btn-primary:hover:not(:disabled) {
      background: #5a67d8;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
    
    .btn-primary:disabled {
      background: #9ca3af;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
    
    .btn-outline {
      background: transparent;
      color: #667eea;
      border: 2px solid #667eea;
    }
    
    .btn-outline:hover {
      background: #667eea;
      color: white;
    }
    
    .empty-state {
      text-align: center;
      padding: 4rem 2rem;
      color: #6c757d;
    }
    
    .empty-state h3 {
      font-size: 1.5rem;
      margin-bottom: 1rem;
      color: #495057;
    }
    
    .empty-state p {
      margin-bottom: 2rem;
      font-size: 1.1rem;
    }
    
    .footer {
      background: #333;
      color: white;
      text-align: center;
      padding: 2rem;
    }
    
    .toast {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: #28a745;
      color: white;
      padding: 1rem 1.5rem;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 1000;
      transform: translateY(100px);
      opacity: 0;
      transition: all 0.3s ease;
    }
    
    .toast.show {
      transform: translateY(0);
      opacity: 1;
    }
    
    .toast.error {
      background: #dc3545;
    }
    
    .toast.warning {
      background: #ffc107;
      color: #333;
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
      .cart-container {
        grid-template-columns: 1fr;
        gap: 1rem;
      }
      
      .cart-item {
        grid-template-columns: 80px 1fr;
        gap: 1rem;
      }
      
      .item-quantity,
      .item-subtotal,
      .btn-remove {
        grid-column: 1 / -1;
        justify-self: center;
        margin-top: 0.5rem;
      }
      
      .page-header h1 {
        font-size: 2rem;
      }
      
      .nav-links {
        gap: 1rem;
      }
      
      .nav-link {
        padding: 0.5rem;
        font-size: 0.9rem;
      }
    }
  </style>
</head>
<body>
  <header class="header">
    <div class="header-container">
      <a href="index_standalone.html" class="logo">
        🛒 发卡商城
      </a>
      <nav class="nav-links">
        <a href="index_standalone.html" class="nav-link">首页</a>
        <a href="cart_optimized.html" class="nav-link active">购物车 <span id="cart-count" class="cart-badge">0</span></a>
        <a href="admin/login.html" class="nav-link">管理后台</a>
      </nav>
    </div>
  </header>

  <div class="container">
    <div class="page-header">
      <h1>我的购物车</h1>
      <p>管理您的商品，随时调整数量</p>
    </div>
    
    <div class="cart-section">
      <div id="cart-content">
        <!-- 购物车内容将在这里显示 -->
      </div>
    </div>
  </div>

  <footer class="footer">
    <p>&copy; 2025 发卡商城. 保留所有权利。</p>
  </footer>

  <script>
    // 购物车管理
    const Cart = {
      get() {
        try {
          return JSON.parse(localStorage.getItem('cart') || '[]');
        } catch {
          return [];
        }
      },

      save(cart) {
        localStorage.setItem('cart', JSON.stringify(cart));
        this.updateCount();
      },

      updateCount() {
        const cart = this.get();
        const count = cart.reduce((total, item) => total + item.quantity, 0);
        document.getElementById('cart-count').textContent = count;
      },

      updateQuantity(productId, newQuantity) {
        const cart = this.get();
        const item = cart.find(item => item.product_id === productId);

        if (item) {
          if (newQuantity <= 0) {
            this.remove(productId);
          } else {
            item.quantity = newQuantity;
            this.save(cart);
            this.showToast(`已更新数量: ${item.name}`);
            renderCart(); // 重新渲染购物车
          }
        }
      },

      remove(productId) {
        const cart = this.get();
        const item = cart.find(item => item.product_id === productId);
        const filteredCart = cart.filter(item => item.product_id !== productId);

        this.save(filteredCart);
        if (item) {
          this.showToast(`已移除: ${item.name}`, 'warning');
        }
        renderCart(); // 重新渲染购物车
      },

      clear() {
        if (confirm('确定要清空购物车吗？')) {
          localStorage.removeItem('cart');
          this.updateCount();
          this.showToast('购物车已清空', 'warning');
          renderCart(); // 重新渲染购物车
        }
      },

      getTotal() {
        const cart = this.get();
        return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
      },

      getTotalCount() {
        const cart = this.get();
        return cart.reduce((total, item) => total + item.quantity, 0);
      },

      showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => toast.classList.add('show'), 10);
        setTimeout(() => {
          toast.classList.remove('show');
          setTimeout(() => toast.remove(), 300);
        }, 3000);
      }
    };

    // 格式化价格
    function formatPrice(price) {
      return `¥${parseFloat(price).toFixed(2)}`;
    }

    // 创建购物车商品项
    function createCartItem(item) {
      return `
        <div class="cart-item" data-product-id="${item.product_id}">
          <div class="item-image">
            <img src="${item.image || `https://picsum.photos/100/100?random=${item.product_id}`}"
                 alt="${item.name}"
                 loading="lazy">
          </div>
          <div class="item-info">
            <h3 class="item-name">${item.name}</h3>
            <div class="item-price">单价: ${formatPrice(item.price)}</div>
          </div>
          <div class="item-quantity">
            <button class="quantity-btn" onclick="updateQuantity(${item.product_id}, ${item.quantity - 1})"
                    ${item.quantity <= 1 ? 'disabled' : ''}>-</button>
            <span class="quantity">${item.quantity}</span>
            <button class="quantity-btn" onclick="updateQuantity(${item.product_id}, ${item.quantity + 1})">+</button>
          </div>
          <div class="item-subtotal">${formatPrice(item.price * item.quantity)}</div>
          <button class="btn-remove" onclick="removeItem(${item.product_id})" title="移除商品">
            🗑️
          </button>
        </div>
      `;
    }

    // 创建购物车摘要
    function createCartSummary() {
      const totalCount = Cart.getTotalCount();
      const totalPrice = Cart.getTotal();

      return `
        <div class="summary-title">订单摘要</div>
        <div class="summary-row">
          <span>商品总数</span>
          <span>${totalCount} 件</span>
        </div>
        <div class="summary-row">
          <span>商品总价</span>
          <span>${formatPrice(totalPrice)}</span>
        </div>
        <div class="summary-actions">
          <button class="btn btn-outline" onclick="clearCart()">清空购物车</button>
          <button class="btn btn-primary" onclick="checkout()" ${totalCount === 0 ? 'disabled' : ''}>
            立即结算 (${totalCount})
          </button>
        </div>
      `;
    }

    // 渲染购物车
    function renderCart() {
      const cartContent = document.getElementById('cart-content');
      const cart = Cart.get();

      if (cart.length === 0) {
        cartContent.innerHTML = `
          <div class="empty-state">
            <h3>🛒 购物车是空的</h3>
            <p>还没有添加任何商品，去首页看看有什么好商品吧！</p>
            <a href="index_standalone.html" class="btn btn-primary">去购物</a>
          </div>
        `;
        return;
      }

      cartContent.innerHTML = `
        <div class="cart-container">
          <div class="cart-items">
            ${cart.map(item => createCartItem(item)).join('')}
          </div>
          <div class="cart-summary">
            ${createCartSummary()}
          </div>
        </div>
      `;
    }

    // 更新商品数量
    function updateQuantity(productId, newQuantity) {
      Cart.updateQuantity(productId, newQuantity);
    }

    // 移除商品
    function removeItem(productId) {
      Cart.remove(productId);
    }

    // 清空购物车
    function clearCart() {
      Cart.clear();
    }

    // 结算
    function checkout() {
      const cart = Cart.get();
      if (cart.length === 0) {
        Cart.showToast('购物车是空的', 'warning');
        return;
      }

      // 这里可以跳转到结算页面或显示结算模态框
      Cart.showToast('正在跳转到结算页面...', 'success');

      // 模拟跳转到结算页面
      setTimeout(() => {
        // window.location.href = 'checkout.html';
        alert('结算功能开发中，敬请期待！');
      }, 1000);
    }

    // 页面初始化
    document.addEventListener('DOMContentLoaded', () => {
      console.log('购物车页面加载完成');
      Cart.updateCount();
      renderCart();
    });
  </script>
</body>
</html>
