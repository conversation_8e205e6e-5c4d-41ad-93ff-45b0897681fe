<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>商品详情</title>
  <link rel="stylesheet" href="../static/style.css" />
</head>
<body>

  <header>
    <h1>发卡商城</h1>
    <nav>
      <a href="../index.html">首页</a>
      <a href="../cart.html">购物车</a>
    </nav>
  </header>

  <div class="container">
    <div class="product-detail" id="product-detail"></div>
    <button onclick="addToCart()">加入购物车</button>
  </div>

  <footer>
    &copy; 2025 发卡商城. 保留所有权利。
  </footer>

  <script>
    const urlParams = new URLSearchParams(window.location.search);
    const productId = parseInt(urlParams.get('id'));

    fetch(`http://localhost:5000/api/products/${productId}`)
      .then(res => res.json())
      .then(product => {
        document.getElementById('product-detail').innerHTML = `
          <div class="product-card">
            <div class="product-title">${product.name}</div>
            <div class="product-desc">${product.description || '暂无描述'}</div>
            <div class="product-price">￥${product.price}</div>
          </div>
        `;
      });

    function addToCart() {
      fetch(`http://localhost:5000/api/products/${productId}`)
        .then(res => res.json())
        .then(product => {
          const cart = JSON.parse(localStorage.getItem('cart') || '[]');
          cart.push({ product_id: product.id, amount: product.price });
          localStorage.setItem('cart', JSON.stringify(cart));
          alert('已加入购物车');
        });
    }
  </script>

</body>
</html>