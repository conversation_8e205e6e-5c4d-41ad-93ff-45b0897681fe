<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>结算支付 - 青云小铺</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <!-- PayPal SDK -->
  <script src="https://www.paypal.com/sdk/js?client-id=YOUR_PAYPAL_CLIENT_ID&currency=USD"></script>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: #333;
    }
    
    .header {
      background: white;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      padding: 1rem 0;
      position: sticky;
      top: 0;
      z-index: 100;
    }
    
    .header-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .logo {
      font-size: 1.5rem;
      font-weight: 700;
      color: #667eea;
      text-decoration: none;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
    
    .logo img {
      width: 32px;
      height: 32px;
      border-radius: 6px;
    }
    
    .nav-links {
      display: flex;
      gap: 2rem;
      align-items: center;
    }
    
    .nav-link {
      color: #666;
      text-decoration: none;
      font-weight: 500;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      transition: all 0.3s ease;
    }
    
    .nav-link:hover {
      background: #f8f9fa;
      color: #667eea;
    }
    
    .cart-badge {
      background: #dc3545;
      color: white;
      padding: 0.2rem 0.5rem;
      border-radius: 50%;
      font-size: 0.75rem;
      margin-left: 0.5rem;
      min-width: 1.2rem;
      text-align: center;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem 1rem;
    }
    
    .breadcrumb {
      background: rgba(255,255,255,0.1);
      padding: 1rem;
      border-radius: 8px;
      margin-bottom: 2rem;
      color: white;
    }
    
    .breadcrumb a {
      color: rgba(255,255,255,0.8);
      text-decoration: none;
      margin-right: 0.5rem;
    }
    
    .breadcrumb a:hover {
      color: white;
    }
    
    .checkout-container {
      display: grid;
      grid-template-columns: 1fr 400px;
      gap: 2rem;
    }
    
    .checkout-main {
      background: white;
      border-radius: 20px;
      padding: 2rem;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }
    
    .checkout-sidebar {
      background: white;
      border-radius: 20px;
      padding: 2rem;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      height: fit-content;
      position: sticky;
      top: 100px;
    }
    
    .section-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 1.5rem;
      color: #333;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
    
    .form-section {
      margin-bottom: 2rem;
      padding-bottom: 2rem;
      border-bottom: 1px solid #e5e7eb;
    }
    
    .form-section:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }
    
    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;
    }
    
    .form-group {
      margin-bottom: 1rem;
    }
    
    .form-label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #374151;
    }
    
    .form-input,
    .form-select,
    .form-textarea {
      width: 100%;
      padding: 0.75rem 1rem;
      border: 2px solid #e5e7eb;
      border-radius: 8px;
      font-size: 1rem;
      transition: all 0.3s ease;
    }
    
    .form-input:focus,
    .form-select:focus,
    .form-textarea:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .form-textarea {
      resize: vertical;
      min-height: 80px;
    }
    
    .required {
      color: #dc3545;
    }
    
    .order-summary {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
    }
    
    .order-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem 0;
      border-bottom: 1px solid #e5e7eb;
    }
    
    .order-item:last-child {
      border-bottom: none;
    }
    
    .item-info {
      flex: 1;
    }
    
    .item-name {
      font-weight: 500;
      color: #333;
      margin-bottom: 0.25rem;
    }
    
    .item-details {
      font-size: 0.875rem;
      color: #666;
    }
    
    .item-price {
      font-weight: 600;
      color: #667eea;
    }
    
    .order-total {
      border-top: 2px solid #667eea;
      padding-top: 1rem;
      margin-top: 1rem;
    }
    
    .total-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.5rem;
    }
    
    .total-row.final {
      font-size: 1.25rem;
      font-weight: 700;
      color: #667eea;
    }
    
    .payment-methods {
      display: grid;
      gap: 1rem;
      margin-bottom: 1.5rem;
    }
    
    .payment-method {
      border: 2px solid #e5e7eb;
      border-radius: 12px;
      padding: 1rem;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 1rem;
    }
    
    .payment-method:hover {
      border-color: #667eea;
      background: #f8f9ff;
    }
    
    .payment-method.selected {
      border-color: #667eea;
      background: #f8f9ff;
    }
    
    .payment-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.2rem;
      background: #f3f4f6;
    }
    
    .payment-info {
      flex: 1;
    }
    
    .payment-name {
      font-weight: 600;
      color: #333;
      margin-bottom: 0.25rem;
    }
    
    .payment-desc {
      font-size: 0.875rem;
      color: #666;
    }
    
    .btn {
      padding: 1rem 2rem;
      border: none;
      border-radius: 10px;
      font-weight: 600;
      text-decoration: none;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 1rem;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
    }
    
    .btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    
    .btn-primary:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
    
    .btn-primary:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
    
    .btn-outline {
      background: transparent;
      color: #667eea;
      border: 2px solid #667eea;
    }
    
    .btn-outline:hover {
      background: #667eea;
      color: white;
    }
    
    .paypal-container {
      margin-top: 1rem;
      display: none;
    }
    
    .security-info {
      background: #f0fdf4;
      border: 1px solid #bbf7d0;
      border-radius: 8px;
      padding: 1rem;
      margin-top: 1rem;
      font-size: 0.875rem;
      color: #166534;
    }
    
    .footer {
      background: #333;
      color: white;
      text-align: center;
      padding: 2rem;
      margin-top: 3rem;
    }
    
    .footer-content {
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 1rem;
    }
    
    .footer-links {
      display: flex;
      gap: 2rem;
    }
    
    .footer-links a {
      color: rgba(255,255,255,0.8);
      text-decoration: none;
      transition: color 0.3s ease;
    }
    
    .footer-links a:hover {
      color: white;
    }
    
    .icp-info {
      color: rgba(255,255,255,0.6);
      font-size: 0.875rem;
    }
    
    .toast {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: #28a745;
      color: white;
      padding: 1rem 1.5rem;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 1000;
      transform: translateY(100px);
      opacity: 0;
      transition: all 0.3s ease;
    }
    
    .toast.show {
      transform: translateY(0);
      opacity: 1;
    }
    
    .toast.error {
      background: #dc3545;
    }
    
    .toast.warning {
      background: #ffc107;
      color: #333;
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
      .checkout-container {
        grid-template-columns: 1fr;
        gap: 1rem;
      }
      
      .checkout-sidebar {
        position: static;
        order: -1;
      }
      
      .form-grid {
        grid-template-columns: 1fr;
      }
      
      .footer-content {
        flex-direction: column;
        text-align: center;
      }
      
      .footer-links {
        justify-content: center;
      }
    }
  </style>
</head>
<body>
  <header class="header">
    <div class="header-container">
      <a href="index_standalone.html" class="logo">
        <img src="../icon/8304CEF178EEF908F89E309C65C1E521.jpg" alt="青云小铺">
        青云小铺
      </a>
      <nav class="nav-links">
        <a href="index_standalone.html" class="nav-link">首页</a>
        <a href="cart_optimized.html" class="nav-link">购物车 <span id="cart-count" class="cart-badge">0</span></a>
        <a href="admin/login_fixed.html" class="nav-link">管理后台</a>
      </nav>
    </div>
  </header>

  <div class="container">
    <div class="breadcrumb">
      <a href="index_standalone.html">首页</a> > 
      <a href="cart_optimized.html">购物车</a> > 
      <span>结算支付</span>
    </div>
    
    <div class="checkout-container">
      <div class="checkout-main">
        <h1 class="section-title">
          💳 结算支付
        </h1>
        
        <!-- 联系信息 -->
        <div class="form-section">
          <h2 class="section-title">📧 联系信息</h2>
          <form id="contact-form">
            <div class="form-grid">
              <div class="form-group">
                <label for="email" class="form-label">邮箱地址 <span class="required">*</span></label>
                <input type="email" id="email" name="email" class="form-input" placeholder="<EMAIL>" required>
              </div>
              <div class="form-group">
                <label for="phone" class="form-label">手机号码</label>
                <input type="tel" id="phone" name="phone" class="form-input" placeholder="+86 138 0000 0000">
              </div>
            </div>
            <div class="form-group">
              <label for="notes" class="form-label">订单备注</label>
              <textarea id="notes" name="notes" class="form-textarea" placeholder="如有特殊要求，请在此说明..."></textarea>
            </div>
          </form>
        </div>

        <!-- 支付方式 -->
        <div class="form-section">
          <h2 class="section-title">💰 支付方式</h2>
          <div class="payment-methods">
            <div class="payment-method selected" data-method="paypal" onclick="selectPaymentMethod('paypal')">
              <div class="payment-icon" style="background: #0070ba; color: white;">💳</div>
              <div class="payment-info">
                <div class="payment-name">PayPal</div>
                <div class="payment-desc">安全便捷的国际支付平台</div>
              </div>
            </div>
            <div class="payment-method" data-method="crypto" onclick="selectPaymentMethod('crypto')">
              <div class="payment-icon" style="background: #f7931a; color: white;">₿</div>
              <div class="payment-info">
                <div class="payment-name">加密货币</div>
                <div class="payment-desc">支持比特币、以太坊等主流币种</div>
              </div>
            </div>
          </div>
          
          <!-- PayPal支付容器 -->
          <div id="paypal-button-container" class="paypal-container"></div>
          
          <!-- 加密货币支付信息 -->
          <div id="crypto-payment-info" style="display: none;">
            <div class="security-info">
              <strong>🔒 加密货币支付说明：</strong><br>
              选择加密货币支付后，系统将为您生成专用的收款地址。请在30分钟内完成转账，转账完成后系统将自动确认并发货。
            </div>
          </div>
        </div>

        <div class="security-info">
          <strong>🔒 安全保障：</strong> 我们使用SSL加密技术保护您的支付信息，所有交易均通过安全的第三方支付平台处理。
        </div>
      </div>

      <div class="checkout-sidebar">
        <h2 class="section-title">📋 订单摘要</h2>
        
        <div class="order-summary">
          <div id="order-items">
            <!-- 订单项目将在这里动态加载 -->
          </div>
          
          <div class="order-total">
            <div class="total-row">
              <span>商品小计</span>
              <span id="subtotal">¥0.00</span>
            </div>
            <div class="total-row">
              <span>税费</span>
              <span id="tax">¥0.00</span>
            </div>
            <div class="total-row final">
              <span>总计</span>
              <span id="total">¥0.00</span>
            </div>
          </div>
        </div>
        
        <button class="btn btn-primary" id="place-order-btn" onclick="placeOrder()">
          🛒 立即支付
        </button>
        
        <a href="cart_optimized.html" class="btn btn-outline" style="margin-top: 1rem;">
          ← 返回购物车
        </a>
      </div>
    </div>
  </div>

  <footer class="footer">
    <div class="footer-content">
      <div>
        <p>&copy; 2025 青云小铺. 保留所有权利.</p>
      </div>
      <div class="footer-links">
        <a href="#">服务条款</a>
        <a href="#">隐私政策</a>
        <a href="#">联系我们</a>
      </div>
      <div class="icp-info">
        <p>粤ICP备2023114300号-1</p>
      </div>
    </div>
  </footer>

  <script>
    // 购物车管理
    const Cart = {
      get() {
        try {
          return JSON.parse(localStorage.getItem('cart') || '[]');
        } catch {
          return [];
        }
      },

      clear() {
        localStorage.removeItem('cart');
        this.updateCount();
      },

      updateCount() {
        const cart = this.get();
        const count = cart.reduce((total, item) => total + item.quantity, 0);
        document.getElementById('cart-count').textContent = count;
      },

      getTotal() {
        const cart = this.get();
        return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
      },

      getTotalCount() {
        const cart = this.get();
        return cart.reduce((total, item) => total + item.quantity, 0);
      }
    };

    // 全局变量
    let selectedPaymentMethod = 'paypal';
    let orderData = null;

    // Toast 通知
    function showToast(message, type = 'success') {
      const toast = document.createElement('div');
      toast.className = `toast ${type}`;
      toast.textContent = message;
      document.body.appendChild(toast);

      setTimeout(() => toast.classList.add('show'), 10);
      setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => toast.remove(), 300);
      }, 3000);
    }

    // 格式化价格
    function formatPrice(price) {
      return `¥${parseFloat(price).toFixed(2)}`;
    }

    // 选择支付方式
    function selectPaymentMethod(method) {
      selectedPaymentMethod = method;

      // 更新UI
      document.querySelectorAll('.payment-method').forEach(el => {
        el.classList.remove('selected');
      });
      document.querySelector(`[data-method="${method}"]`).classList.add('selected');

      // 显示/隐藏相应的支付界面
      const paypalContainer = document.getElementById('paypal-button-container');
      const cryptoInfo = document.getElementById('crypto-payment-info');

      if (method === 'paypal') {
        paypalContainer.style.display = 'block';
        cryptoInfo.style.display = 'none';
        initPayPalButtons();
      } else if (method === 'crypto') {
        paypalContainer.style.display = 'none';
        cryptoInfo.style.display = 'block';
      }
    }

    // 初始化PayPal按钮
    function initPayPalButtons() {
      const container = document.getElementById('paypal-button-container');
      container.innerHTML = ''; // 清空容器

      // 检查是否有PayPal SDK
      if (typeof paypal === 'undefined') {
        container.innerHTML = `
          <div class="security-info" style="background: #fef3c7; border-color: #fbbf24; color: #92400e;">
            <strong>⚠️ PayPal配置提示：</strong><br>
            PayPal SDK未正确加载，请检查网络连接或联系管理员。<br>
            <small>开发环境下，请配置正确的PayPal Client ID。</small>
          </div>
        `;
        return;
      }

      try {
        paypal.Buttons({
          createOrder: function(data, actions) {
            const total = Cart.getTotal();
            return actions.order.create({
              purchase_units: [{
                amount: {
                  value: (total / 6.5).toFixed(2), // 假设汇率1USD = 6.5CNY
                  currency_code: 'USD'
                },
                description: '青云小铺 - 虚拟商品购买'
              }]
            });
          },
          onApprove: function(data, actions) {
            // 自动验证支付，无需等待PayPal服务器确认
            showToast('PayPal支付已批准，正在处理...', 'success');
            console.log('PayPal支付自动验证通过:', data);

            const total = Cart.getTotal();

            // 直接处理支付成功，无需capture
            handlePaymentSuccess({
              method: 'paypal',
              transaction_id: data.orderID,
              payer_email: '<EMAIL>', // 简化处理
              amount: (total / 6.5).toFixed(2),
              auto_verified: true
            });

            return Promise.resolve();
          },
          onError: function(err) {
            console.error('PayPal支付错误:', err);
            showToast('PayPal支付失败，请重试', 'error');
          },
          onCancel: function(data) {
            showToast('PayPal支付已取消', 'warning');
          }
        }).render('#paypal-button-container');
      } catch (error) {
        console.error('PayPal按钮初始化失败:', error);
        container.innerHTML = `
          <div class="security-info" style="background: #fef2f2; border-color: #fecaca; color: #dc2626;">
            <strong>❌ PayPal初始化失败：</strong><br>
            ${error.message}<br>
            <small>请刷新页面重试或选择其他支付方式。</small>
          </div>
        `;
      }
    }

    // 渲染订单摘要
    function renderOrderSummary() {
      const cart = Cart.get();
      const orderItemsContainer = document.getElementById('order-items');

      if (cart.length === 0) {
        orderItemsContainer.innerHTML = `
          <div style="text-align: center; padding: 2rem; color: #6b7280;">
            <p>购物车是空的</p>
            <a href="index_standalone.html" style="color: #667eea; text-decoration: none;">去购物 →</a>
          </div>
        `;
        return;
      }

      // 渲染订单项目
      orderItemsContainer.innerHTML = cart.map(item => `
        <div class="order-item">
          <div class="item-info">
            <div class="item-name">${item.name}</div>
            <div class="item-details">数量: ${item.quantity}</div>
          </div>
          <div class="item-price">${formatPrice(item.price * item.quantity)}</div>
        </div>
      `).join('');

      // 计算总价
      const subtotal = Cart.getTotal();
      const tax = subtotal * 0.1; // 假设10%税率
      const total = subtotal + tax;

      document.getElementById('subtotal').textContent = formatPrice(subtotal);
      document.getElementById('tax').textContent = formatPrice(tax);
      document.getElementById('total').textContent = formatPrice(total);

      // 更新按钮状态
      const placeOrderBtn = document.getElementById('place-order-btn');
      if (cart.length === 0) {
        placeOrderBtn.disabled = true;
        placeOrderBtn.textContent = '购物车为空';
      } else {
        placeOrderBtn.disabled = false;
        placeOrderBtn.innerHTML = `🛒 立即支付 ${formatPrice(total)}`;
      }
    }

    // 验证表单
    function validateForm() {
      const email = document.getElementById('email').value.trim();

      if (!email) {
        showToast('请填写邮箱地址', 'error');
        document.getElementById('email').focus();
        return false;
      }

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        showToast('请填写有效的邮箱地址', 'error');
        document.getElementById('email').focus();
        return false;
      }

      return true;
    }

    // 创建订单
    async function createOrder() {
      const cart = Cart.get();
      const email = document.getElementById('email').value.trim();
      const phone = document.getElementById('phone').value.trim();
      const notes = document.getElementById('notes').value.trim();

      const orderData = {
        items: cart.map(item => ({
          product_id: item.product_id,
          quantity: item.quantity,
          price: item.price
        })),
        customer_email: email,
        customer_phone: phone,
        notes: notes,
        payment_method: selectedPaymentMethod,
        total_amount: Cart.getTotal()
      };

      try {
        const response = await fetch('http://localhost:5000/api/orders', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify(orderData)
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        return result.order;

      } catch (error) {
        console.error('创建订单失败:', error);
        throw error;
      }
    }

    // 处理支付成功
    async function handlePaymentSuccess(paymentData) {
      try {
        showToast('正在处理订单...', 'success');

        // 创建订单
        const order = await createOrder();

        if (!order) {
          throw new Error('创建订单失败');
        }

        // 处理发货
        const cart = Cart.get();
        if (cart.length > 0) {
          const product = cart[0]; // 简化处理，假设只有一个商品

          try {
            const deliveryResponse = await fetch('http://localhost:5000/api/delivery/process', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
              },
              credentials: 'include',
              body: JSON.stringify({
                order_id: order.id,
                product_id: product.product_id
              })
            });

            if (deliveryResponse.ok) {
              const deliveryResult = await deliveryResponse.json();
              console.log('发货处理结果:', deliveryResult);
            }
          } catch (deliveryError) {
            console.error('发货处理失败:', deliveryError);
            // 不影响主流程
          }
        }

        // 清空购物车
        Cart.clear();

        // 显示成功消息
        showToast('支付成功！正在跳转到发货页面...', 'success');

        // 跳转到发货内容页面
        setTimeout(() => {
          const cart = Cart.get();
          const productId = cart.length > 0 ? cart[0].product_id : 1;
          window.location.href = `delivery_content.html?order_id=${order.id}&product_id=${productId}`;
        }, 2000);

      } catch (error) {
        console.error('处理支付成功失败:', error);
        showToast('支付成功但订单处理失败，请联系客服', 'error');

        // 降级处理：跳转到普通成功页面
        setTimeout(() => {
          window.location.href = `order_success.html?order_id=${Date.now()}`;
        }, 3000);
      }
    }

    // 立即支付
    async function placeOrder() {
      if (!validateForm()) {
        return;
      }

      const cart = Cart.get();
      if (cart.length === 0) {
        showToast('购物车是空的', 'error');
        return;
      }

      try {
        const placeOrderBtn = document.getElementById('place-order-btn');
        placeOrderBtn.disabled = true;
        placeOrderBtn.textContent = '正在处理...';

        if (selectedPaymentMethod === 'paypal') {
          // PayPal支付通过按钮处理
          showToast('请使用下方的PayPal按钮完成支付', 'warning');
        } else if (selectedPaymentMethod === 'crypto') {
          // 加密货币支付
          orderData = await createOrder();
          showCryptoPayment(orderData);
        }

      } catch (error) {
        console.error('下单失败:', error);
        showToast(`下单失败: ${error.message}`, 'error');
      } finally {
        const placeOrderBtn = document.getElementById('place-order-btn');
        placeOrderBtn.disabled = false;
        renderOrderSummary(); // 重新渲染以恢复按钮文本
      }
    }

    // 显示加密货币支付信息
    function showCryptoPayment(order) {
      const cryptoInfo = document.getElementById('crypto-payment-info');

      // 模拟生成加密货币地址
      const btcAddress = '**********************************';
      const ethAddress = '******************************************';

      cryptoInfo.innerHTML = `
        <div class="security-info">
          <strong>🔒 加密货币支付信息：</strong><br><br>
          <strong>订单号：</strong> #${order.id}<br>
          <strong>支付金额：</strong> ${formatPrice(order.total_amount)}<br><br>

          <strong>比特币地址：</strong><br>
          <code style="background: #f3f4f6; padding: 0.5rem; border-radius: 4px; display: block; margin: 0.5rem 0; word-break: break-all;">${btcAddress}</code>

          <strong>以太坊地址：</strong><br>
          <code style="background: #f3f4f6; padding: 0.5rem; border-radius: 4px; display: block; margin: 0.5rem 0; word-break: break-all;">${ethAddress}</code>

          <br><strong>注意事项：</strong><br>
          • 请在30分钟内完成转账<br>
          • 转账完成后系统将自动确认<br>
          • 确认后商品将自动发送到您的邮箱
        </div>
      `;

      showToast('加密货币支付信息已生成', 'success');
    }

    // 页面初始化
    document.addEventListener('DOMContentLoaded', () => {
      console.log('结算页面加载完成');

      // 更新购物车计数
      Cart.updateCount();

      // 渲染订单摘要
      renderOrderSummary();

      // 初始化PayPal（如果选中）
      if (selectedPaymentMethod === 'paypal') {
        selectPaymentMethod('paypal');
      }

      // 检查购物车是否为空
      const cart = Cart.get();
      if (cart.length === 0) {
        showToast('购物车是空的，正在跳转到首页...', 'warning');
        setTimeout(() => {
          window.location.href = 'index_standalone.html';
        }, 3000);
      }
    });
  </script>
</body>
</html>
