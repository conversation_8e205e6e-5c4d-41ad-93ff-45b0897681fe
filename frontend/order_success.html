<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>支付成功 - 青云小铺</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: #333;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 2rem 1rem;
    }
    
    .success-container {
      background: white;
      border-radius: 20px;
      padding: 3rem 2rem;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      text-align: center;
      max-width: 600px;
      width: 100%;
    }
    
    .success-icon {
      width: 80px;
      height: 80px;
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 2rem;
      font-size: 2rem;
      color: white;
      animation: bounce 0.6s ease-out;
    }
    
    @keyframes bounce {
      0% { transform: scale(0); }
      50% { transform: scale(1.1); }
      100% { transform: scale(1); }
    }
    
    .success-title {
      font-size: 2rem;
      font-weight: 700;
      color: #1f2937;
      margin-bottom: 1rem;
    }
    
    .success-message {
      font-size: 1.1rem;
      color: #6b7280;
      margin-bottom: 2rem;
      line-height: 1.6;
    }
    
    .order-info {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 2rem;
      margin-bottom: 2rem;
      text-align: left;
    }
    
    .order-info h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 1rem;
      text-align: center;
    }
    
    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.75rem 0;
      border-bottom: 1px solid #e5e7eb;
    }
    
    .info-row:last-child {
      border-bottom: none;
    }
    
    .info-label {
      font-weight: 500;
      color: #374151;
    }
    
    .info-value {
      font-weight: 600;
      color: #1f2937;
    }
    
    .order-id {
      color: #667eea;
      font-family: monospace;
    }
    
    .next-steps {
      background: #eff6ff;
      border: 1px solid #dbeafe;
      border-radius: 12px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      text-align: left;
    }
    
    .next-steps h4 {
      font-size: 1.1rem;
      font-weight: 600;
      color: #1e40af;
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
    
    .next-steps ul {
      list-style: none;
      padding: 0;
    }
    
    .next-steps li {
      padding: 0.5rem 0;
      color: #1e40af;
      display: flex;
      align-items: flex-start;
      gap: 0.5rem;
    }
    
    .next-steps li::before {
      content: '✓';
      color: #10b981;
      font-weight: bold;
      margin-top: 0.1rem;
    }
    
    .actions {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
    }
    
    .btn {
      padding: 1rem 2rem;
      border: none;
      border-radius: 10px;
      font-weight: 600;
      text-decoration: none;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 1rem;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }
    
    .btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    
    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
    
    .btn-outline {
      background: transparent;
      color: #667eea;
      border: 2px solid #667eea;
    }
    
    .btn-outline:hover {
      background: #667eea;
      color: white;
    }
    
    .contact-info {
      margin-top: 2rem;
      padding-top: 2rem;
      border-top: 1px solid #e5e7eb;
      color: #6b7280;
      font-size: 0.9rem;
    }
    
    .contact-info a {
      color: #667eea;
      text-decoration: none;
    }
    
    .contact-info a:hover {
      text-decoration: underline;
    }
    
    .countdown {
      background: #fef3c7;
      border: 1px solid #fbbf24;
      border-radius: 8px;
      padding: 1rem;
      margin-bottom: 2rem;
      color: #92400e;
      font-weight: 500;
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
      .success-container {
        padding: 2rem 1.5rem;
      }
      
      .success-title {
        font-size: 1.5rem;
      }
      
      .actions {
        flex-direction: column;
      }
      
      .btn {
        width: 100%;
        justify-content: center;
      }
      
      .info-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
      }
    }
  </style>
</head>
<body>
  <div class="success-container">
    <div class="success-icon">
      ✅
    </div>
    
    <h1 class="success-title">支付成功！</h1>
    
    <p class="success-message">
      感谢您在青云小铺的购买！您的订单已成功创建并完成支付。
      我们将尽快为您处理订单并发送商品到您的邮箱。
    </p>
    
    <div class="order-info">
      <h3>📋 订单详情</h3>
      <div class="info-row">
        <span class="info-label">订单号</span>
        <span class="info-value order-id" id="order-id">#loading...</span>
      </div>
      <div class="info-row">
        <span class="info-label">支付时间</span>
        <span class="info-value" id="payment-time">loading...</span>
      </div>
      <div class="info-row">
        <span class="info-label">支付金额</span>
        <span class="info-value" id="payment-amount">loading...</span>
      </div>
      <div class="info-row">
        <span class="info-label">支付方式</span>
        <span class="info-value" id="payment-method">loading...</span>
      </div>
      <div class="info-row">
        <span class="info-label">收货邮箱</span>
        <span class="info-value" id="customer-email">loading...</span>
      </div>
    </div>
    
    <div class="next-steps">
      <h4>📬 接下来会发生什么？</h4>
      <ul>
        <li>我们已收到您的付款，正在验证交易</li>
        <li>订单确认后，商品将自动发送到您的邮箱</li>
        <li>通常在5-15分钟内完成发货</li>
        <li>您将收到包含商品信息的邮件</li>
        <li>如有问题，请及时联系客服</li>
      </ul>
    </div>
    
    <div class="countdown" id="countdown-info">
      ⏰ 预计发货时间：<span id="countdown-timer">15:00</span>
    </div>
    
    <div class="actions">
      <a href="index_standalone.html" class="btn btn-primary">
        🏠 返回首页
      </a>
      <a href="mailto:<EMAIL>" class="btn btn-outline">
        📧 联系客服
      </a>
    </div>
    
    <div class="contact-info">
      <p>
        <strong>需要帮助？</strong><br>
        如果您在15分钟内未收到商品邮件，或有任何问题，请联系我们：<br>
        📧 邮箱：<a href="mailto:<EMAIL>"><EMAIL></a><br>
        🕒 客服时间：周一至周日 9:00-21:00
      </p>
    </div>
  </div>

  <script>
    // 获取URL参数
    function getUrlParameter(name) {
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get(name);
    }

    // 格式化价格
    function formatPrice(price) {
      return `¥${parseFloat(price).toFixed(2)}`;
    }

    // 格式化时间
    function formatTime(date) {
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    }

    // 倒计时功能
    function startCountdown() {
      let timeLeft = 15 * 60; // 15分钟
      const countdownElement = document.getElementById('countdown-timer');
      
      const timer = setInterval(() => {
        const minutes = Math.floor(timeLeft / 60);
        const seconds = timeLeft % 60;
        
        countdownElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        
        if (timeLeft <= 0) {
          clearInterval(timer);
          countdownElement.textContent = '00:00';
          document.getElementById('countdown-info').innerHTML = `
            <strong>⚠️ 如果您仍未收到商品，请联系客服</strong>
          `;
          document.getElementById('countdown-info').style.background = '#fef2f2';
          document.getElementById('countdown-info').style.borderColor = '#fecaca';
          document.getElementById('countdown-info').style.color = '#dc2626';
        }
        
        timeLeft--;
      }, 1000);
    }

    // 加载订单信息
    async function loadOrderInfo() {
      const orderId = getUrlParameter('order_id');
      
      if (!orderId) {
        // 如果没有订单ID，使用模拟数据
        const mockOrderId = `QY${Date.now()}`;
        document.getElementById('order-id').textContent = `#${mockOrderId}`;
        document.getElementById('payment-time').textContent = formatTime(new Date());
        document.getElementById('payment-amount').textContent = formatPrice(Math.random() * 1000 + 100);
        document.getElementById('payment-method').textContent = 'PayPal';
        document.getElementById('customer-email').textContent = '<EMAIL>';
        return;
      }
      
      try {
        // 这里应该调用后端API获取订单信息
        // const response = await fetch(`http://localhost:5000/api/orders/${orderId}`);
        // const order = await response.json();
        
        // 模拟订单数据
        const order = {
          id: orderId,
          payment_time: new Date().toISOString(),
          total_amount: localStorage.getItem('last_order_amount') || '299.99',
          payment_method: localStorage.getItem('last_payment_method') || 'PayPal',
          customer_email: localStorage.getItem('last_customer_email') || '<EMAIL>'
        };
        
        // 更新页面信息
        document.getElementById('order-id').textContent = `#${order.id}`;
        document.getElementById('payment-time').textContent = formatTime(new Date(order.payment_time));
        document.getElementById('payment-amount').textContent = formatPrice(order.total_amount);
        document.getElementById('payment-method').textContent = order.payment_method;
        document.getElementById('customer-email').textContent = order.customer_email;
        
      } catch (error) {
        console.error('加载订单信息失败:', error);
        
        // 使用默认信息
        document.getElementById('order-id').textContent = `#${orderId}`;
        document.getElementById('payment-time').textContent = formatTime(new Date());
        document.getElementById('payment-amount').textContent = '¥---.--';
        document.getElementById('payment-method').textContent = '未知';
        document.getElementById('customer-email').textContent = '未知';
      }
    }

    // 页面初始化
    document.addEventListener('DOMContentLoaded', () => {
      console.log('订单成功页面加载完成');
      
      // 加载订单信息
      loadOrderInfo();
      
      // 开始倒计时
      startCountdown();
      
      // 清除可能的临时数据
      setTimeout(() => {
        localStorage.removeItem('last_order_amount');
        localStorage.removeItem('last_payment_method');
        localStorage.removeItem('last_customer_email');
      }, 5000);
    });
  </script>
</body>
</html>
