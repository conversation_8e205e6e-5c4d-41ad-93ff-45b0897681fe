<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>我的购物车</title>
  <link rel="stylesheet" href="../static/style.css" />
</head>
<body>

  <header>
    <h1>我的购物车</h1>
    <nav>
      <a href="../frontend/index.html">返回商城</a>
    </nav>
  </header>

  <div class="container">
    <table id="cart-table">
      <thead>
        <tr>
          <th>商品ID</th>
          <th>价格</th>
          <th>数量</th>
          <th>小计</th>
        </tr>
      </thead>
      <tbody></tbody>
    </table>
    <div class="total">总价：￥<span id="total-price">0</span></div>
    <button onclick="checkout()">去支付</button>
  </div>

  <footer>
    &copy; 2025 发卡商城. 保留所有权利。
  </footer>

  <script>
    const cart = JSON.parse(localStorage.getItem('cart') || '[]');
    const tbody = document.querySelector('#cart-table tbody');

    if (cart.length === 0) {
      tbody.innerHTML = `<tr><td colspan="4">购物车为空</td></tr>`;
    } else {
      let totalPrice = 0;
      cart.forEach((item, index) => {
        const quantity = item.quantity || 1;
        const subtotal = item.amount * quantity;
        totalPrice += subtotal;

        tbody.innerHTML += `
          <tr>
            <td>商品ID: ${item.product_id}</td>
            <td>￥${item.amount}</td>
            <td>
              <input type="number" value="${quantity}" min="1" onchange="updateQuantity(${index}, this.value)">
            </td>
            <td>￥${subtotal.toFixed(2)}</td>
          </tr>
        `;
      });
      document.getElementById('total-price').innerText = totalPrice.toFixed(2);
    }

    function updateQuantity(index, quantity) {
      const cart = JSON.parse(localStorage.getItem('cart') || '[]');
      cart[index].quantity = parseInt(quantity);
      localStorage.setItem('cart', JSON.stringify(cart));
      location.reload();
    }

    function checkout() {
      if (cart.length === 0) {
        alert('购物车为空');
        return;
      }
      fetch('http://localhost:5000/api/order', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(cart[0])  // 示例只提交第一个订单
      })
      .then(res => res.json())
      .then(data => {
        window.location.href = 'checkout.html?order_id=' + data.id;
      });
    }
  </script>

</body>
</html>
