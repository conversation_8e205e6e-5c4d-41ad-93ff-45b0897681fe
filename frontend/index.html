<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>发卡商城 - 虚拟商品自动发货</title>
  <link rel="stylesheet" href="../static/style.css">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
  <header>
    <div class="header-container">
      <a href="#" class="logo">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
          <path d="M3 9h18M9 21V9"></path>
        </svg>
        <span>发卡商城</span>
      </a>
      <nav class="nav-links">
        <a href="#" class="nav-link active">首页</a>
        <a href="cart.html" class="nav-link">购物车</a>
      </nav>
    </div>
  </header>

  <div class="container">
    <h1 class="page-title">精选虚拟商品</h1>
    
    <div class="loading" id="loading">
      <div class="spinner"></div>
      <span>正在加载商品...</span>
    </div>
    
    <div class="product-grid" id="product-list"></div>
    
    <div class="no-product" id="no-product" style="display:none;">
      <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <circle cx="12" cy="12" r="10"></circle>
        <line x1="12" y1="8" x2="12" y2="12"></line>
        <line x1="12" y1="16" x2="12.01" y2="16"></line>
      </svg>
      <p>暂无商品，请稍后再试</p>
    </div>
  </div>

  <footer>
    <div class="footer-content">
      <p>&copy; 2025 发卡商城. 保留所有权利。</p>
    </div>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      fetch('http://localhost:5000/api/products')
        .then(res => {
          if (!res.ok) throw new Error('网络错误');
          return res.json();
        })
        .then(response => {
          const loading = document.getElementById('loading');
          const container = document.getElementById('product-list');
          const noProduct = document.getElementById('no-product');

          loading.style.display = 'none';

          // 处理分页响应格式
          const products = response.items || [];

          if (!products || products.length === 0) {
            noProduct.style.display = 'flex';
            noProduct.style.flexDirection = 'column';
            noProduct.style.alignItems = 'center';
            noProduct.style.gap = '1rem';
            noProduct.style.padding = '2rem';
            noProduct.style.color = '#6b7280';
            return;
          }

          container.innerHTML = products.map(p => `
            <div class="product-card">
              <img src="https://picsum.photos/400/300?random=${p.id}" alt="${p.name}" class="product-image">
              <div class="product-content">
                <h3 class="product-title">${p.name}</h3>
                <p class="product-desc">${p.description || '虚拟商品，自动发货'}</p>
                <div class="product-price">￥${p.price.toFixed(2)}</div>
                <div class="product-actions">
                  <a href="detail.html?id=${p.id}" class="btn btn-outline">查看详情</a>
                  <button class="btn btn-primary" onclick="addToCart(${p.id}, '${p.name.replace(/'/g, "\\'")}', ${p.price})">加入购物车</button>
                </div>
              </div>
            </div>
          `).join('');
        })
        .catch(err => {
          const loading = document.getElementById('loading');
          const noProduct = document.getElementById('no-product');
          
          loading.style.display = 'none';
          noProduct.style.display = 'flex';
          noProduct.style.flexDirection = 'column';
          noProduct.style.alignItems = 'center';
          noProduct.style.gap = '1rem';
          noProduct.style.padding = '2rem';
          noProduct.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#ef4444" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="8" x2="12" y2="12"></line>
              <line x1="12" y1="16" x2="12.01" y2="16"></line>
            </svg>
            <p>加载失败: ${err.message}</p>
            <button class="btn btn-primary" onclick="window.location.reload()">重试</button>
          `;
        });
    });

    /**
     * 加入购物车，若已存在则数量累加
     * @param {number} productId 
     * @param {string} name 
     * @param {number} price 
     */
    function addToCart(productId, name, price) {
      try {
        let cart = JSON.parse(localStorage.getItem('cart') || '[]');
        let found = cart.find(item => item.product_id === productId);
        
        if (found) {
          found.amount = (found.amount || 1) + 1;
        } else {
          cart.push({ 
            product_id: productId, 
            name: name, 
            price: price, 
            amount: 1,
            image: `https://picsum.photos/100/100?random=${productId}`
          });
        }
        
        localStorage.setItem('cart', JSON.stringify(cart));
        
        // 显示Toast通知
        const toast = document.createElement('div');
        toast.style.position = 'fixed';
        toast.style.bottom = '20px';
        toast.style.right = '20px';
        toast.style.padding = '12px 16px';
        toast.style.backgroundColor = '#10b981';
        toast.style.color = 'white';
        toast.style.borderRadius = '6px';
        toast.style.boxShadow = '0 4px 6px rgba(0,0,0,0.1)';
        toast.style.zIndex = '1000';
        toast.style.transition = 'all 0.3s ease';
        toast.style.transform = 'translateY(20px)';
        toast.style.opacity = '0';
        toast.innerHTML = `
          <div style="display: flex; align-items: center; gap: 8px;">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
            <span>已加入购物车: ${name}</span>
          </div>
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
          toast.style.transform = 'translateY(0)';
          toast.style.opacity = '1';
        }, 10);
        
        setTimeout(() => {
          toast.style.transform = 'translateY(20px)';
          toast.style.opacity = '0';
          setTimeout(() => toast.remove(), 300);
        }, 3000);
        
      } catch (err) {
        console.error('加入购物车失败:', err);
      }
    }
  </script>

</body>
</html>
