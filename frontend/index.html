<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>发卡商城 - 虚拟商品自动发货</title>
  <link rel="stylesheet" href="../static/style.css">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
  <!-- Header将通过JavaScript动态生成 -->

  <div class="container">
    <h1 class="page-title">精选虚拟商品</h1>
    
    <div class="loading" id="loading">
      <div class="spinner"></div>
      <span>正在加载商品...</span>
    </div>
    
    <div class="product-grid" id="product-list"></div>
    
    <div class="no-product" id="no-product" style="display:none;">
      <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <circle cx="12" cy="12" r="10"></circle>
        <line x1="12" y1="8" x2="12" y2="12"></line>
        <line x1="12" y1="16" x2="12.01" y2="16"></line>
      </svg>
      <p>暂无商品，请稍后再试</p>
    </div>
  </div>

  <footer>
    <div class="footer-content">
      <p>&copy; 2025 发卡商城. 保留所有权利。</p>
    </div>
  </footer>

  <!-- 引入通用组件 -->
  <script src="../static/common.js?v=20250720"></script>
  <script src="../static/header.js?v=20250720"></script>

  <script>
    // 页面初始化
    document.addEventListener('DOMContentLoaded', async () => {
      console.log('页面DOM加载完成');

      // 检查依赖是否加载
      if (typeof FakaMall === 'undefined') {
        console.error('FakaMall对象未定义，请检查common.js是否正确加载');
        document.getElementById('loading').style.display = 'none';
        document.getElementById('no-product').innerHTML = `
          <div style="text-align: center; padding: 2rem; color: #dc3545;">
            <h3>❌ 系统初始化失败</h3>
            <p>JavaScript组件加载失败，请刷新页面重试</p>
            <button onclick="window.location.reload()" style="padding: 0.5rem 1rem; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">刷新页面</button>
          </div>
        `;
        document.getElementById('no-product').style.display = 'block';
        return;
      }

      if (typeof HeaderComponent === 'undefined') {
        console.error('HeaderComponent对象未定义，请检查header.js是否正确加载');
      } else {
        // 初始化Header
        HeaderComponent.init({
          currentPage: 'home',
          showCart: true,
          showAdmin: true
        });
      }

      // 加载产品列表
      await loadProducts();
    });

    // 加载产品列表
    async function loadProducts() {
      console.log('开始加载产品列表');
      const container = document.getElementById('product-list');
      const loading = document.getElementById('loading');
      const noProduct = document.getElementById('no-product');

      try {
        // 显示加载状态
        loading.style.display = 'flex';
        noProduct.style.display = 'none';
        console.log('显示加载状态');

        // 检查API对象是否存在
        if (!FakaMall || !FakaMall.API) {
          throw new Error('FakaMall.API对象不存在');
        }

        console.log('开始调用API获取产品数据');
        // 获取产品数据
        const response = await FakaMall.API.getProducts();
        console.log('API响应:', response);

        const products = response.items || [];
        console.log('产品数量:', products.length);

        // 隐藏加载状态
        loading.style.display = 'none';

        if (products.length === 0) {
          console.log('没有产品，显示空状态');
          FakaMall.UI.showEmpty(container, '暂无商品，请稍后再试');
          return;
        }

        // 渲染产品列表
        console.log('开始渲染产品列表');
        container.innerHTML = products.map(product => createProductCard(product)).join('');
        console.log('产品列表渲染完成');

      } catch (error) {
        console.error('加载产品失败:', error);
        console.error('错误堆栈:', error.stack);
        loading.style.display = 'none';

        // 显示详细错误信息
        noProduct.innerHTML = `
          <div style="text-align: center; padding: 2rem; color: #dc3545;">
            <h3>❌ 加载失败</h3>
            <p><strong>错误信息:</strong> ${error.message}</p>
            <p><strong>可能原因:</strong></p>
            <ul style="text-align: left; display: inline-block;">
              <li>后端服务器未启动 (http://localhost:5000)</li>
              <li>网络连接问题</li>
              <li>CORS配置问题</li>
              <li>API路径错误</li>
            </ul>
            <button onclick="loadProducts()" style="padding: 0.5rem 1rem; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 0.5rem;">重试</button>
            <button onclick="window.open('http://localhost:5000/api/health', '_blank')" style="padding: 0.5rem 1rem; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 0.5rem;">测试API</button>
          </div>
        `;
        noProduct.style.display = 'block';
      }
    }

    // 创建产品卡片
    function createProductCard(product) {
      return `
        <div class="product-card">
          <img src="https://picsum.photos/400/300?random=${product.id}"
               alt="${product.name}"
               class="product-image"
               loading="lazy">
          <div class="product-content">
            <h3 class="product-title">${product.name}</h3>
            <p class="product-desc">${product.description || '虚拟商品，自动发货'}</p>
            <div class="product-meta">
              <div class="product-price">${FakaMall.DataFormatter.formatPrice(product.price)}</div>
              <div class="product-stock">${FakaMall.DataFormatter.formatStock(product.stock)}</div>
            </div>
            <div class="product-actions">
              <a href="detail.html?id=${product.id}" class="btn btn-outline">查看详情</a>
              <button class="btn btn-primary" onclick="addToCart(${product.id})">
                加入购物车
              </button>
            </div>
          </div>
        </div>
      `;
    }

    // 添加到购物车
    async function addToCart(productId) {
      try {
        // 获取产品详情
        const product = await FakaMall.API.getProduct(productId);

        // 检查库存
        if (product.stock <= 0) {
          FakaMall.UI.showToast('商品库存不足', 'error');
          return;
        }

        // 添加到购物车
        FakaMall.Cart.add(product, 1);

      } catch (error) {
        console.error('添加到购物车失败:', error);
        FakaMall.UI.showToast('添加失败，请重试', 'error');
      }
    }
  </script>

</body>
</html>
