<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>发卡商城</title>
  <link rel="stylesheet" href="../static/style.css" />
</head>
<body>

  <header>
    <h1>发卡商城</h1>
    <nav>
      <a href="#">首页</a>
      <a href="cart.html">购物车</a>
    </nav>
  </header>

  <div class="container">
    <h2>热门商品</h2>
    <div class="loading" id="loading">加载中...</div>
    <div class="product-grid" id="product-list"></div>
    <div class="no-product" id="no-product" style="display:none;color:#888;text-align:center;margin:2em 0;">暂无商品</div>
  </div>

  <footer>
    &copy; 2025 发卡商城. 保留所有权利。
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      fetch('http://localhost:5000/api/products')
        .then(res => {
          if (!res.ok) throw new Error('网络错误');
          return res.json();
        })
        .then(products => {
          document.getElementById('loading').style.display = 'none';
          const container = document.getElementById('product-list');
          const noProduct = document.getElementById('no-product');
          if (!products || products.length === 0) {
            noProduct.style.display = 'block';
            return;
          }
          products.forEach(p => {
            container.innerHTML += `
              <div class="product-card">
                <div class="product-title">${p.name}</div>
                <div class="product-desc">${p.description || '虚拟商品，自动发货'}</div>
                <div class="product-price">￥${p.price}</div>
                <div class="product-actions">
                  <a href="detail.html?id=${p.id}">查看详情</a>
                  <button onclick="addToCart(${p.id}, '${p.name}', ${p.price})">加入购物车</button>
                </div>
              </div>
            `;
          });
        })
        .catch(err => {
          document.getElementById('loading').style.display = 'none';
          document.getElementById('no-product').style.display = 'block';
          document.getElementById('no-product').innerText = '加载失败，请稍后重试';
        });
    });

    /**
     * 加入购物车，若已存在则数量累加
     * @param {number} productId 
     * @param {string} name 
     * @param {number} price 
     */
    function addToCart(productId, name, price) {
      let cart = JSON.parse(localStorage.getItem('cart') || '[]');
      let found = cart.find(item => item.product_id === productId);
      if (found) {
        found.amount = (found.amount || 1) + 1;
      } else {
        cart.push({ product_id: productId, name: name, price: price, amount: 1 });
      }
      localStorage.setItem('cart', JSON.stringify(cart));
      alert('已加入购物车');
    }
  </script>

</body>
</html>
