<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>发卡商城 - 虚拟商品自动发货</title>
  <link rel="stylesheet" href="../static/style.css">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
  <!-- Header将通过JavaScript动态生成 -->

  <div class="container">
    <h1 class="page-title">精选虚拟商品</h1>
    
    <div class="loading" id="loading">
      <div class="spinner"></div>
      <span>正在加载商品...</span>
    </div>
    
    <div class="product-grid" id="product-list"></div>
    
    <div class="no-product" id="no-product" style="display:none;">
      <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <circle cx="12" cy="12" r="10"></circle>
        <line x1="12" y1="8" x2="12" y2="12"></line>
        <line x1="12" y1="16" x2="12.01" y2="16"></line>
      </svg>
      <p>暂无商品，请稍后再试</p>
    </div>
  </div>

  <footer>
    <div class="footer-content">
      <p>&copy; 2025 发卡商城. 保留所有权利。</p>
    </div>
  </footer>

  <!-- 引入通用组件 -->
  <script src="../static/common.js"></script>
  <script src="../static/header.js"></script>

  <script>
    // 页面初始化
    document.addEventListener('DOMContentLoaded', async () => {
      // 初始化Header
      HeaderComponent.init({
        currentPage: 'home',
        showCart: true,
        showAdmin: true
      });

      // 加载产品列表
      await loadProducts();
    });

    // 加载产品列表
    async function loadProducts() {
      const container = document.getElementById('product-list');
      const loading = document.getElementById('loading');
      const noProduct = document.getElementById('no-product');

      try {
        // 显示加载状态
        loading.style.display = 'flex';
        noProduct.style.display = 'none';

        // 获取产品数据
        const response = await FakaMall.API.getProducts();
        const products = response.items || [];

        // 隐藏加载状态
        loading.style.display = 'none';

        if (products.length === 0) {
          FakaMall.UI.showEmpty(container, '暂无商品，请稍后再试');
          return;
        }

        // 渲染产品列表
        container.innerHTML = products.map(product => createProductCard(product)).join('');

      } catch (error) {
        console.error('加载产品失败:', error);
        loading.style.display = 'none';
        FakaMall.UI.showError(container, `加载失败: ${error.message}`, 'loadProducts()');
      }
    }

    // 创建产品卡片
    function createProductCard(product) {
      return `
        <div class="product-card">
          <img src="https://picsum.photos/400/300?random=${product.id}"
               alt="${product.name}"
               class="product-image"
               loading="lazy">
          <div class="product-content">
            <h3 class="product-title">${product.name}</h3>
            <p class="product-desc">${product.description || '虚拟商品，自动发货'}</p>
            <div class="product-meta">
              <div class="product-price">${FakaMall.DataFormatter.formatPrice(product.price)}</div>
              <div class="product-stock">${FakaMall.DataFormatter.formatStock(product.stock)}</div>
            </div>
            <div class="product-actions">
              <a href="detail.html?id=${product.id}" class="btn btn-outline">查看详情</a>
              <button class="btn btn-primary" onclick="addToCart(${product.id})">
                加入购物车
              </button>
            </div>
          </div>
        </div>
      `;
    }

    // 添加到购物车
    async function addToCart(productId) {
      try {
        // 获取产品详情
        const product = await FakaMall.API.getProduct(productId);

        // 检查库存
        if (product.stock <= 0) {
          FakaMall.UI.showToast('商品库存不足', 'error');
          return;
        }

        // 添加到购物车
        FakaMall.Cart.add(product, 1);

      } catch (error) {
        console.error('添加到购物车失败:', error);
        FakaMall.UI.showToast('添加失败，请重试', 'error');
      }
    }
  </script>

</body>
</html>
