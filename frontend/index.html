<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>发卡商城</title>
  <link rel="stylesheet" href="../static/style.css" />
</head>
<body>

  <header>
    <h1>发卡商城</h1>
    <nav>
      <a href="#">首页</a>
      <a href="cart.html">购物车</a>
    </nav>
  </header>

  <div class="container">
    <h2>热门商品</h2>
    <div class="loading" id="loading">加载中...</div>
    <div class="product-grid" id="product-list"></div>
  </div>

  <footer>
    &copy; 2025 发卡商城. 保留所有权利。
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      fetch('http://localhost:5000/api/products')
        .then(res => res.json())
        .then(products => {
          document.getElementById('loading').style.display = 'none';
          const container = document.getElementById('product-list');
          products.forEach(p => {
            container.innerHTML += `
              <div class="product-card">
                <div class="product-title">${p.name}</div>
                <div class="product-desc">${p.description || '虚拟商品，自动发货'}</div>
                <div class="product-price">￥${p.price}</div>
                <a href="detail.html?id=${p.id}">查看详情</a>
              </div>
            `;
          });
        });
    });

    function addToCart(productId, price) {
      const cart = JSON.parse(localStorage.getItem('cart') || '[]');
      cart.push({ product_id: productId, amount: price });
      localStorage.setItem('cart', JSON.stringify(cart));
      alert('已加入购物车');
    }
  </script>

</body>
</html>
