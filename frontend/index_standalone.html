<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>发卡商城 - 虚拟商品自动发货</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: #333;
    }
    
    .header {
      background: white;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      padding: 1rem 0;
      position: sticky;
      top: 0;
      z-index: 100;
    }
    
    .header-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .logo {
      font-size: 1.5rem;
      font-weight: 700;
      color: #667eea;
      text-decoration: none;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
    
    .nav-links {
      display: flex;
      gap: 2rem;
      align-items: center;
    }
    
    .nav-link {
      color: #666;
      text-decoration: none;
      font-weight: 500;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      transition: all 0.3s ease;
    }
    
    .nav-link:hover {
      background: #f8f9fa;
      color: #667eea;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem 1rem;
    }
    
    .hero {
      text-align: center;
      color: white;
      margin-bottom: 3rem;
    }
    
    .hero h1 {
      font-size: 3rem;
      font-weight: 700;
      margin-bottom: 1rem;
      text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }
    
    .hero p {
      font-size: 1.2rem;
      opacity: 0.9;
      margin-bottom: 2rem;
    }
    
    .products-section {
      background: white;
      border-radius: 20px;
      padding: 2rem;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }
    
    .section-title {
      font-size: 2rem;
      font-weight: 600;
      text-align: center;
      margin-bottom: 2rem;
      color: #333;
    }
    
    .loading {
      text-align: center;
      padding: 3rem;
      color: #666;
    }
    
    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #667eea;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .products-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 2rem;
      margin-top: 2rem;
    }
    
    .product-card {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      border: 1px solid #e5e7eb;
    }
    
    .product-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }
    
    .product-image {
      width: 100%;
      height: 200px;
      object-fit: cover;
    }
    
    .product-content {
      padding: 1.5rem;
    }
    
    .product-title {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      color: #333;
    }
    
    .product-desc {
      color: #666;
      margin-bottom: 1rem;
      line-height: 1.5;
    }
    
    .product-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }
    
    .product-price {
      font-size: 1.5rem;
      font-weight: 700;
      color: #667eea;
    }
    
    .product-stock {
      font-size: 0.875rem;
      color: #666;
      background: #f8f9fa;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
    }
    
    .product-actions {
      display: flex;
      gap: 0.75rem;
    }
    
    .btn {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 8px;
      font-weight: 500;
      text-decoration: none;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      flex: 1;
    }
    
    .btn-primary {
      background: #667eea;
      color: white;
    }
    
    .btn-primary:hover {
      background: #5a67d8;
      transform: translateY(-1px);
    }
    
    .btn-outline {
      background: transparent;
      color: #667eea;
      border: 2px solid #667eea;
    }
    
    .btn-outline:hover {
      background: #667eea;
      color: white;
    }
    
    .error-state {
      text-align: center;
      padding: 3rem;
      color: #dc3545;
    }
    
    .error-state h3 {
      margin-bottom: 1rem;
    }
    
    .error-state button {
      margin: 0.5rem;
    }
    
    .footer {
      background: #333;
      color: white;
      text-align: center;
      padding: 2rem;
      margin-top: 3rem;
    }
    
    .toast {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: #28a745;
      color: white;
      padding: 1rem 1.5rem;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 1000;
      transform: translateY(100px);
      opacity: 0;
      transition: all 0.3s ease;
    }
    
    .toast.show {
      transform: translateY(0);
      opacity: 1;
    }
    
    .toast.error {
      background: #dc3545;
    }
  </style>
</head>
<body>
  <header class="header">
    <div class="header-container">
      <a href="#" class="logo">
        🛒 发卡商城
      </a>
      <nav class="nav-links">
        <a href="#" class="nav-link">首页</a>
        <a href="cart.html" class="nav-link">购物车 <span id="cart-count" style="background: #dc3545; color: white; padding: 0.2rem 0.5rem; border-radius: 50%; font-size: 0.75rem; margin-left: 0.5rem;">0</span></a>
        <a href="admin/login.html" class="nav-link">管理后台</a>
      </nav>
    </div>
  </header>

  <div class="container">
    <div class="hero">
      <h1>精选虚拟商品</h1>
      <p>安全可靠的虚拟商品交易平台，自动发货，即买即用</p>
    </div>

    <div class="products-section">
      <h2 class="section-title">热门商品</h2>
      
      <div id="loading" class="loading">
        <div class="spinner"></div>
        <p>正在加载商品...</p>
      </div>
      
      <div id="products-grid" class="products-grid" style="display: none;"></div>
      
      <div id="error-state" class="error-state" style="display: none;"></div>
    </div>
  </div>

  <footer class="footer">
    <p>&copy; 2025 发卡商城. 保留所有权利。</p>
  </footer>

  <script>
    // 购物车管理
    const Cart = {
      get() {
        try {
          return JSON.parse(localStorage.getItem('cart') || '[]');
        } catch {
          return [];
        }
      },
      
      add(product, quantity = 1) {
        const cart = this.get();
        const existingItem = cart.find(item => item.product_id === product.id);
        
        if (existingItem) {
          existingItem.quantity += quantity;
        } else {
          cart.push({
            product_id: product.id,
            name: product.name,
            price: product.price,
            quantity: quantity,
            image: `https://picsum.photos/100/100?random=${product.id}`
          });
        }
        
        localStorage.setItem('cart', JSON.stringify(cart));
        this.updateCount();
        this.showToast(`已添加到购物车: ${product.name}`);
      },
      
      updateCount() {
        const cart = this.get();
        const count = cart.reduce((total, item) => total + item.quantity, 0);
        document.getElementById('cart-count').textContent = count;
      },
      
      showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;
        document.body.appendChild(toast);
        
        setTimeout(() => toast.classList.add('show'), 10);
        setTimeout(() => {
          toast.classList.remove('show');
          setTimeout(() => toast.remove(), 300);
        }, 3000);
      }
    };

    // 格式化价格
    function formatPrice(price) {
      return `¥${parseFloat(price).toFixed(2)}`;
    }

    // 格式化库存
    function formatStock(stock) {
      const num = parseInt(stock) || 0;
      if (num <= 0) return '缺货';
      if (num <= 10) return `库存紧张 (${num})`;
      return `库存充足 (${num})`;
    }

    // 创建产品卡片
    function createProductCard(product) {
      return `
        <div class="product-card">
          <img src="https://picsum.photos/400/300?random=${product.id}" 
               alt="${product.name}" 
               class="product-image"
               loading="lazy">
          <div class="product-content">
            <h3 class="product-title">${product.name}</h3>
            <p class="product-desc">${product.description || '虚拟商品，自动发货'}</p>
            <div class="product-meta">
              <div class="product-price">${formatPrice(product.price)}</div>
              <div class="product-stock">${formatStock(product.stock)}</div>
            </div>
            <div class="product-actions">
              <a href="detail.html?id=${product.id}" class="btn btn-outline">查看详情</a>
              <button class="btn btn-primary" onclick="addToCart(${product.id})">
                加入购物车
              </button>
            </div>
          </div>
        </div>
      `;
    }

    // 加载产品列表
    async function loadProducts() {
      console.log('开始加载产品列表');
      const loading = document.getElementById('loading');
      const productsGrid = document.getElementById('products-grid');
      const errorState = document.getElementById('error-state');
      
      try {
        loading.style.display = 'block';
        productsGrid.style.display = 'none';
        errorState.style.display = 'none';
        
        console.log('发送API请求...');
        const response = await fetch('http://localhost:5000/api/products', {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          },
          mode: 'cors'
        });
        
        console.log('API响应状态:', response.status);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log('API响应数据:', data);
        
        const products = data.items || [];
        console.log('产品数量:', products.length);
        
        loading.style.display = 'none';
        
        if (products.length === 0) {
          errorState.innerHTML = `
            <h3>📭 暂无商品</h3>
            <p>商店正在准备商品，请稍后再来看看</p>
          `;
          errorState.style.display = 'block';
          return;
        }
        
        productsGrid.innerHTML = products.map(product => createProductCard(product)).join('');
        productsGrid.style.display = 'grid';
        
        console.log('产品列表渲染完成');
        
      } catch (error) {
        console.error('加载产品失败:', error);
        loading.style.display = 'none';
        
        errorState.innerHTML = `
          <h3>❌ 加载失败</h3>
          <p><strong>错误:</strong> ${error.message}</p>
          <p><strong>可能原因:</strong></p>
          <ul style="text-align: left; display: inline-block; margin: 1rem 0;">
            <li>后端服务器未启动 (http://localhost:5000)</li>
            <li>网络连接问题</li>
            <li>浏览器阻止了跨域请求</li>
          </ul>
          <button class="btn btn-primary" onclick="loadProducts()">重试</button>
          <button class="btn btn-outline" onclick="window.open('http://localhost:5000/api/health', '_blank')">测试后端</button>
        `;
        errorState.style.display = 'block';
      }
    }

    // 添加到购物车
    async function addToCart(productId) {
      try {
        console.log('添加产品到购物车:', productId);
        
        // 获取产品详情
        const response = await fetch(`http://localhost:5000/api/products/${productId}`);
        if (!response.ok) {
          throw new Error('获取产品信息失败');
        }
        
        const product = await response.json();
        
        // 检查库存
        if (product.stock <= 0) {
          Cart.showToast('商品库存不足', 'error');
          return;
        }
        
        // 添加到购物车
        Cart.add(product, 1);
        
      } catch (error) {
        console.error('添加到购物车失败:', error);
        Cart.showToast('添加失败，请重试', 'error');
      }
    }

    // 页面初始化
    document.addEventListener('DOMContentLoaded', () => {
      console.log('页面加载完成');
      Cart.updateCount();
      loadProducts();
    });
  </script>
</body>
</html>
