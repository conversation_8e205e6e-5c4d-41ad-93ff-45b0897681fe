<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>商品发货 - 青云小铺</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: #333;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 2rem 1rem;
    }
    
    .delivery-container {
      background: white;
      border-radius: 20px;
      padding: 3rem 2rem;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
      text-align: center;
      max-width: 700px;
      width: 100%;
    }
    
    .delivery-icon {
      width: 80px;
      height: 80px;
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 2rem;
      font-size: 2rem;
      color: white;
      animation: bounce 0.6s ease-out;
    }
    
    @keyframes bounce {
      0% { transform: scale(0); }
      50% { transform: scale(1.1); }
      100% { transform: scale(1); }
    }
    
    .delivery-title {
      font-size: 2rem;
      font-weight: 700;
      color: #1f2937;
      margin-bottom: 1rem;
    }
    
    .delivery-message {
      font-size: 1.1rem;
      color: #6b7280;
      margin-bottom: 2rem;
      line-height: 1.6;
    }
    
    .order-info {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 2rem;
      margin-bottom: 2rem;
      text-align: left;
    }
    
    .order-info h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 1rem;
      text-align: center;
    }
    
    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.75rem 0;
      border-bottom: 1px solid #e5e7eb;
    }
    
    .info-row:last-child {
      border-bottom: none;
    }
    
    .info-label {
      font-weight: 500;
      color: #374151;
    }
    
    .info-value {
      font-weight: 600;
      color: #1f2937;
    }
    
    .order-id {
      color: #667eea;
      font-family: monospace;
    }
    
    .delivery-content-section {
      background: #eff6ff;
      border: 1px solid #dbeafe;
      border-radius: 12px;
      padding: 2rem;
      margin-bottom: 2rem;
      text-align: left;
    }
    
    .content-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1e40af;
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      justify-content: center;
    }
    
    .delivery-content {
      background: white;
      border-radius: 8px;
      padding: 1.5rem;
      font-family: 'Courier New', monospace;
      white-space: pre-wrap;
      line-height: 1.6;
      border: 1px solid #e5e7eb;
      max-height: 300px;
      overflow-y: auto;
    }
    
    .copy-section {
      margin-top: 1rem;
      text-align: center;
    }
    
    .copy-btn {
      background: #667eea;
      color: white;
      border: none;
      border-radius: 8px;
      padding: 0.75rem 1.5rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }
    
    .copy-btn:hover {
      background: #5a67d8;
      transform: translateY(-1px);
    }
    
    .copy-btn.copied {
      background: #10b981;
    }
    
    .email-notice {
      background: #f0fdf4;
      border: 1px solid #bbf7d0;
      border-radius: 12px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      color: #166534;
    }
    
    .email-notice h4 {
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }
    
    .actions {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
    }
    
    .btn {
      padding: 1rem 2rem;
      border: none;
      border-radius: 10px;
      font-weight: 600;
      text-decoration: none;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 1rem;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }
    
    .btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }
    
    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
    
    .btn-outline {
      background: transparent;
      color: #667eea;
      border: 2px solid #667eea;
    }
    
    .btn-outline:hover {
      background: #667eea;
      color: white;
    }
    
    .contact-info {
      margin-top: 2rem;
      padding-top: 2rem;
      border-top: 1px solid #e5e7eb;
      color: #6b7280;
      font-size: 0.9rem;
    }
    
    .contact-info a {
      color: #667eea;
      text-decoration: none;
    }
    
    .contact-info a:hover {
      text-decoration: underline;
    }
    
    .loading {
      text-align: center;
      padding: 3rem;
      color: #6b7280;
    }
    
    .spinner {
      width: 32px;
      height: 32px;
      border: 3px solid #f3f4f6;
      border-top: 3px solid #667eea;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .error-state {
      text-align: center;
      padding: 3rem;
      color: #dc2626;
    }
    
    .error-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
      .delivery-container {
        padding: 2rem 1.5rem;
      }
      
      .delivery-title {
        font-size: 1.5rem;
      }
      
      .actions {
        flex-direction: column;
      }
      
      .btn {
        width: 100%;
        justify-content: center;
      }
      
      .info-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
      }
    }
  </style>
</head>
<body>
  <div class="delivery-container">
    <!-- 加载状态 -->
    <div id="loading-state" class="loading">
      <div class="spinner"></div>
      <p>正在获取发货内容...</p>
    </div>
    
    <!-- 错误状态 -->
    <div id="error-state" class="error-state" style="display: none;">
      <div class="error-icon">❌</div>
      <h2>获取发货内容失败</h2>
      <p id="error-message">请联系客服获取帮助</p>
      <div style="margin-top: 2rem;">
        <a href="index_standalone.html" class="btn btn-primary">返回首页</a>
      </div>
    </div>
    
    <!-- 主要内容 -->
    <div id="main-content" style="display: none;">
      <div class="delivery-icon">
        📦
      </div>
      
      <h1 class="delivery-title">商品已发货！</h1>
      
      <p class="delivery-message">
        恭喜！您购买的虚拟商品已成功发货。
        请妥善保存以下内容，并注意查收邮件。
      </p>
      
      <div class="order-info">
        <h3>📋 订单信息</h3>
        <div class="info-row">
          <span class="info-label">商品名称</span>
          <span class="info-value" id="product-name">loading...</span>
        </div>
        <div class="info-row">
          <span class="info-label">订单号</span>
          <span class="info-value order-id" id="order-id">loading...</span>
        </div>
        <div class="info-row">
          <span class="info-label">发货时间</span>
          <span class="info-value" id="delivery-time">loading...</span>
        </div>
        <div class="info-row">
          <span class="info-label">收货邮箱</span>
          <span class="info-value" id="customer-email">loading...</span>
        </div>
      </div>
      
      <div class="delivery-content-section">
        <div class="content-title">
          📦 发货内容
        </div>
        <div class="delivery-content" id="delivery-content">
          loading...
        </div>
        <div class="copy-section">
          <button class="copy-btn" id="copy-btn" onclick="copyContent()">
            📋 复制内容
          </button>
        </div>
      </div>
      
      <div class="email-notice">
        <h4>📧 邮件通知</h4>
        <p>
          相同的发货内容已发送到您的邮箱 <strong id="email-address">loading...</strong>，
          请注意查收。如果未收到邮件，请检查垃圾邮件文件夹。
        </p>
      </div>
      
      <div class="actions">
        <a href="index_standalone.html" class="btn btn-primary">
          🏠 返回首页
        </a>
        <a href="mailto:<EMAIL>" class="btn btn-outline">
          📧 联系客服
        </a>
      </div>
      
      <div class="contact-info">
        <p>
          <strong>需要帮助？</strong><br>
          如果您在使用过程中遇到任何问题，请联系我们：<br>
          📧 邮箱：<a href="mailto:<EMAIL>"><EMAIL></a><br>
          🕒 客服时间：周一至周日 9:00-21:00
        </p>
      </div>
    </div>
  </div>

  <script>
    // 获取URL参数
    function getUrlParameter(name) {
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get(name);
    }

    // 格式化时间
    function formatTime(date) {
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    }

    // 复制内容到剪贴板
    async function copyContent() {
      const content = document.getElementById('delivery-content').textContent;
      const copyBtn = document.getElementById('copy-btn');
      
      try {
        await navigator.clipboard.writeText(content);
        
        // 更新按钮状态
        copyBtn.classList.add('copied');
        copyBtn.innerHTML = '✅ 已复制';
        
        setTimeout(() => {
          copyBtn.classList.remove('copied');
          copyBtn.innerHTML = '📋 复制内容';
        }, 2000);
        
      } catch (error) {
        console.error('复制失败:', error);
        
        // 降级方案：选择文本
        const range = document.createRange();
        range.selectNode(document.getElementById('delivery-content'));
        window.getSelection().removeAllRanges();
        window.getSelection().addRange(range);
        
        copyBtn.innerHTML = '📋 已选择文本';
        setTimeout(() => {
          copyBtn.innerHTML = '📋 复制内容';
        }, 2000);
      }
    }

    // 显示错误状态
    function showError(message) {
      document.getElementById('loading-state').style.display = 'none';
      document.getElementById('main-content').style.display = 'none';
      document.getElementById('error-state').style.display = 'block';
      document.getElementById('error-message').textContent = message;
    }

    // 显示主要内容
    function showMainContent() {
      document.getElementById('loading-state').style.display = 'none';
      document.getElementById('error-state').style.display = 'none';
      document.getElementById('main-content').style.display = 'block';
    }

    // 加载发货内容
    async function loadDeliveryContent() {
      const orderId = getUrlParameter('order_id');
      const productId = getUrlParameter('product_id');
      
      if (!orderId || !productId) {
        showError('缺少必要的参数，请从正确的链接访问');
        return;
      }
      
      try {
        // 获取订单信息
        const orderResponse = await fetch(`http://localhost:5000/api/orders/${orderId}`, {
          credentials: 'include'
        });
        
        if (!orderResponse.ok) {
          throw new Error('订单不存在或已过期');
        }
        
        const order = await orderResponse.json();
        
        // 获取商品信息和发货内容
        const productResponse = await fetch(`http://localhost:5000/api/products/${productId}`, {
          credentials: 'include'
        });
        
        if (!productResponse.ok) {
          throw new Error('商品信息获取失败');
        }
        
        const product = await productResponse.json();
        
        // 模拟发货内容（实际应该从后端获取）
        let deliveryContent = '';
        if (product.delivery_type === 'list') {
          // 从列表中随机选择一个
          const items = JSON.parse(product.delivery_content || '[]');
          if (items.length > 0) {
            const randomIndex = Math.floor(Math.random() * items.length);
            deliveryContent = items[randomIndex];
          } else {
            deliveryContent = '暂无可用的发货内容';
          }
        } else {
          deliveryContent = product.delivery_content || '暂无发货内容';
        }
        
        // 更新页面内容
        document.getElementById('product-name').textContent = product.name;
        document.getElementById('order-id').textContent = `#${orderId}`;
        document.getElementById('delivery-time').textContent = formatTime(new Date());
        document.getElementById('customer-email').textContent = order.customer_email || '<EMAIL>';
        document.getElementById('email-address').textContent = order.customer_email || '<EMAIL>';
        document.getElementById('delivery-content').textContent = deliveryContent;
        
        showMainContent();
        
        // 模拟发送邮件
        await sendDeliveryEmail(order, product, deliveryContent);
        
      } catch (error) {
        console.error('加载发货内容失败:', error);
        showError(error.message || '加载发货内容失败，请联系客服');
      }
    }

    // 发送发货邮件
    async function sendDeliveryEmail(order, product, deliveryContent) {
      try {
        const emailData = {
          to: order.customer_email,
          subject: product.email_subject || `【青云小铺】您购买的商品已发货`,
          content: deliveryContent,
          order_id: order.id,
          product_name: product.name
        };
        
        // 这里应该调用后端API发送邮件
        // const response = await fetch('http://localhost:5000/api/send-delivery-email', {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   credentials: 'include',
        //   body: JSON.stringify(emailData)
        // });
        
        console.log('发货邮件已发送:', emailData);
        
      } catch (error) {
        console.error('发送邮件失败:', error);
        // 不影响主要流程，只记录错误
      }
    }

    // 页面初始化
    document.addEventListener('DOMContentLoaded', () => {
      console.log('发货内容页面加载完成');
      
      // 加载发货内容
      loadDeliveryContent();
    });
  </script>
</body>
</html>
