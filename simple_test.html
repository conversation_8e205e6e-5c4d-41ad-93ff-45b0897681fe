<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .product-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .error {
            text-align: center;
            padding: 20px;
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛒 发卡商城 - 简单测试页面</h1>
        
        <div id="status-container">
            <div class="status info">正在初始化...</div>
        </div>
        
        <div>
            <button onclick="testAPI()">测试API连接</button>
            <button onclick="loadProducts()">加载产品</button>
            <button onclick="clearResults()">清除结果</button>
        </div>
        
        <div id="products-container">
            <div class="loading" id="loading" style="display: none;">
                <p>⏳ 加载中...</p>
            </div>
            <div id="products-list"></div>
            <div id="error-message" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 状态显示函数
        function showStatus(message, type = 'info') {
            const container = document.getElementById('status-container');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }

        // 测试API连接
        async function testAPI() {
            showStatus('开始测试API连接...', 'info');
            
            try {
                // 测试健康检查
                const healthResponse = await fetch('http://localhost:5000/api/health');
                if (healthResponse.ok) {
                    const healthData = await healthResponse.json();
                    showStatus(`✅ 健康检查成功: ${JSON.stringify(healthData)}`, 'success');
                } else {
                    throw new Error(`健康检查失败: ${healthResponse.status}`);
                }
                
                // 测试产品API
                const productsResponse = await fetch('http://localhost:5000/api/products');
                if (productsResponse.ok) {
                    const productsData = await productsResponse.json();
                    showStatus(`✅ 产品API成功: 获取到 ${productsData.total || 0} 个产品`, 'success');
                } else {
                    throw new Error(`产品API失败: ${productsResponse.status}`);
                }
                
            } catch (error) {
                showStatus(`❌ API测试失败: ${error.message}`, 'error');
                console.error('API测试错误:', error);
            }
        }

        // 加载产品列表
        async function loadProducts() {
            showStatus('开始加载产品列表...', 'info');
            
            const loading = document.getElementById('loading');
            const productsList = document.getElementById('products-list');
            const errorMessage = document.getElementById('error-message');
            
            // 显示加载状态
            loading.style.display = 'block';
            productsList.innerHTML = '';
            errorMessage.style.display = 'none';
            
            try {
                const response = await fetch('http://localhost:5000/api/products');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                const products = data.items || [];
                
                showStatus(`✅ 成功加载 ${products.length} 个产品`, 'success');
                
                // 隐藏加载状态
                loading.style.display = 'none';
                
                if (products.length === 0) {
                    productsList.innerHTML = '<div class="status info">📭 暂无产品</div>';
                    return;
                }
                
                // 渲染产品列表
                productsList.innerHTML = products.map(product => `
                    <div class="product-card">
                        <h3>${product.name}</h3>
                        <p><strong>价格:</strong> ¥${product.price}</p>
                        <p><strong>库存:</strong> ${product.stock}</p>
                        <p><strong>描述:</strong> ${product.description || '无描述'}</p>
                        <p><strong>ID:</strong> ${product.id}</p>
                    </div>
                `).join('');
                
            } catch (error) {
                showStatus(`❌ 加载产品失败: ${error.message}`, 'error');
                console.error('加载产品错误:', error);
                
                loading.style.display = 'none';
                errorMessage.innerHTML = `
                    <div class="error">
                        <h3>❌ 加载失败</h3>
                        <p><strong>错误:</strong> ${error.message}</p>
                        <p><strong>可能原因:</strong></p>
                        <ul>
                            <li>后端服务器未启动</li>
                            <li>网络连接问题</li>
                            <li>CORS配置问题</li>
                        </ul>
                        <button onclick="loadProducts()">重试</button>
                        <button onclick="window.open('http://localhost:5000/api/health', '_blank')">测试后端</button>
                    </div>
                `;
                errorMessage.style.display = 'block';
            }
        }

        // 清除结果
        function clearResults() {
            document.getElementById('status-container').innerHTML = '<div class="status info">已清除结果</div>';
            document.getElementById('products-list').innerHTML = '';
            document.getElementById('error-message').style.display = 'none';
            document.getElementById('loading').style.display = 'none';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            showStatus('🚀 页面加载完成', 'success');
            showStatus(`📍 当前页面: ${window.location.href}`, 'info');
            showStatus(`🌐 用户代理: ${navigator.userAgent}`, 'info');
            showStatus(`🔗 在线状态: ${navigator.onLine ? '在线' : '离线'}`, navigator.onLine ? 'success' : 'error');
            
            // 自动测试API
            setTimeout(() => {
                testAPI();
            }, 1000);
        });
    </script>
</body>
</html>
