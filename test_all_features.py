#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全功能测试脚本
测试所有修复后的功能
"""

import requests
import json
import time

BASE_URL = 'http://localhost:5000'

def test_all_features():
    """测试所有功能"""
    print("=== 发卡商城全功能测试 ===\n")
    
    # 创建会话
    session = requests.Session()
    
    # 1. 测试健康检查
    print("1. 测试健康检查")
    try:
        response = session.get(f"{BASE_URL}/api/health")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print(f"   ✅ 健康检查通过")
        else:
            print(f"   ❌ 健康检查失败")
    except Exception as e:
        print(f"   ❌ 健康检查异常: {e}")
    
    # 2. 测试产品API
    print("\n2. 测试产品API")
    try:
        response = session.get(f"{BASE_URL}/api/products")
        print(f"   获取产品列表状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 产品数量: {data.get('total', 0)}")
            if data.get('items'):
                print(f"   ✅ 第一个产品: {data['items'][0]['name']}")
        else:
            print(f"   ❌ 获取产品列表失败")
    except Exception as e:
        print(f"   ❌ 产品API异常: {e}")
    
    # 3. 测试管理员登录
    print("\n3. 测试管理员登录")
    try:
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        response = session.post(f"{BASE_URL}/api/auth/login", json=login_data)
        print(f"   登录状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 登录成功: {result.get('message', '成功')}")
        else:
            print(f"   ❌ 登录失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 登录异常: {e}")
    
    # 4. 测试创建产品（需要管理员权限）
    print("\n4. 测试创建产品")
    try:
        product_data = {
            "name": "测试产品",
            "price": 88.88,
            "stock": 999,
            "description": "这是一个测试产品"
        }
        response = session.post(f"{BASE_URL}/api/products", json=product_data)
        print(f"   创建产品状态码: {response.status_code}")
        if response.status_code == 201:
            result = response.json()
            print(f"   ✅ 产品创建成功: {result.get('product', {}).get('name', '未知')}")
            test_product_id = result.get('product', {}).get('id')
        else:
            print(f"   ❌ 创建产品失败: {response.text}")
            test_product_id = None
    except Exception as e:
        print(f"   ❌ 创建产品异常: {e}")
        test_product_id = None
    
    # 5. 测试更新产品
    if test_product_id:
        print("\n5. 测试更新产品")
        try:
            update_data = {
                "name": "更新后的测试产品",
                "price": 99.99,
                "stock": 888,
                "description": "这是更新后的测试产品"
            }
            response = session.put(f"{BASE_URL}/api/products/{test_product_id}", json=update_data)
            print(f"   更新产品状态码: {response.status_code}")
            if response.status_code == 200:
                print(f"   ✅ 产品更新成功")
            else:
                print(f"   ❌ 更新产品失败: {response.text}")
        except Exception as e:
            print(f"   ❌ 更新产品异常: {e}")
    
    # 6. 测试订单API
    print("\n6. 测试订单API")
    try:
        response = session.get(f"{BASE_URL}/api/orders")
        print(f"   获取订单列表状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 订单数量: {data.get('total', 0)}")
        else:
            print(f"   ❌ 获取订单列表失败: {response.text}")
    except Exception as e:
        print(f"   ❌ 订单API异常: {e}")
    
    # 7. 测试创建订单
    print("\n7. 测试创建订单")
    try:
        # 先获取一个产品ID
        products_response = session.get(f"{BASE_URL}/api/products")
        if products_response.status_code == 200:
            products = products_response.json().get('items', [])
            if products:
                product_id = products[0]['id']
                order_data = {
                    "product_id": product_id,
                    "quantity": 1,
                    "notes": "测试订单"
                }
                response = session.post(f"{BASE_URL}/api/orders", json=order_data)
                print(f"   创建订单状态码: {response.status_code}")
                if response.status_code == 201:
                    result = response.json()
                    print(f"   ✅ 订单创建成功: ID {result.get('order', {}).get('id', '未知')}")
                else:
                    print(f"   ❌ 创建订单失败: {response.text}")
            else:
                print(f"   ❌ 没有可用产品创建订单")
        else:
            print(f"   ❌ 无法获取产品列表创建订单")
    except Exception as e:
        print(f"   ❌ 创建订单异常: {e}")
    
    # 8. 测试搜索功能
    print("\n8. 测试搜索功能")
    try:
        response = session.get(f"{BASE_URL}/api/products?search=Steam")
        print(f"   搜索产品状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 搜索结果数量: {data.get('total', 0)}")
        else:
            print(f"   ❌ 搜索功能失败")
    except Exception as e:
        print(f"   ❌ 搜索功能异常: {e}")
    
    # 9. 测试分页功能
    print("\n9. 测试分页功能")
    try:
        response = session.get(f"{BASE_URL}/api/products?page=1&limit=2")
        print(f"   分页查询状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 分页功能正常，当前页: {data.get('page', 0)}")
        else:
            print(f"   ❌ 分页功能失败")
    except Exception as e:
        print(f"   ❌ 分页功能异常: {e}")
    
    # 10. 清理测试数据
    if test_product_id:
        print("\n10. 清理测试数据")
        try:
            response = session.delete(f"{BASE_URL}/api/products/{test_product_id}")
            print(f"   删除测试产品状态码: {response.status_code}")
            if response.status_code == 200:
                print(f"   ✅ 测试产品已删除")
            else:
                print(f"   ❌ 删除测试产品失败")
        except Exception as e:
            print(f"   ❌ 删除测试产品异常: {e}")
    
    print("\n=== 测试完成 ===")
    print("✅ 所有核心功能已测试")
    print("🌐 前端地址: http://localhost:8080/frontend/index.html")
    print("🔧 管理后台: http://localhost:8080/frontend/admin/login.html")
    print("📊 系统状态: http://localhost:8080/status.html")

if __name__ == "__main__":
    try:
        test_all_features()
    except requests.exceptions.ConnectionError:
        print("❌ 错误: 无法连接到服务器")
        print("请确保后端服务器正在运行: cd backend && source venv/bin/activate && python app.py")
        print("请确保前端服务器正在运行: python3 -m http.server 8080")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
