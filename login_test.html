<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 300px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 登录功能测试页面</h1>
        
        <div class="test-section">
            <h2>1. 网络连接测试</h2>
            <button onclick="testConnection()">测试后端连接</button>
            <div id="connection-results"></div>
        </div>
        
        <div class="test-section">
            <h2>2. CORS测试</h2>
            <button onclick="testCORS()">测试CORS配置</button>
            <div id="cors-results"></div>
        </div>
        
        <div class="test-section">
            <h2>3. 登录API测试</h2>
            <div>
                <input type="text" id="test-username" value="admin" placeholder="用户名">
                <input type="password" id="test-password" value="admin123" placeholder="密码">
                <button onclick="testLoginFetch()">使用Fetch测试</button>
                <button onclick="testLoginXHR()">使用XHR测试</button>
            </div>
            <div id="login-results"></div>
        </div>
        
        <div class="test-section">
            <h2>4. 浏览器信息</h2>
            <div id="browser-info"></div>
        </div>
        
        <div class="test-section">
            <h2>5. 实时日志</h2>
            <button onclick="clearLogs()">清除日志</button>
            <div id="logs"></div>
        </div>
    </div>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logsContainer = document.getElementById('logs');
            const logEntry = document.createElement('div');
            logEntry.className = `test-result ${type}`;
            logEntry.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;
            console.log(`[${type.toUpperCase()}]`, message);
        }

        // 显示结果
        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }

        // 测试网络连接
        async function testConnection() {
            log('开始测试网络连接');
            showResult('connection-results', '开始测试网络连接...', 'info');
            
            try {
                const response = await fetch('http://localhost:5000/api/health', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    const message = `✅ 连接成功! 状态: ${response.status}, 数据: ${JSON.stringify(data)}`;
                    showResult('connection-results', message, 'success');
                    log(message, 'success');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                const message = `❌ 连接失败: ${error.message}`;
                showResult('connection-results', message, 'error');
                log(message, 'error');
            }
        }

        // 测试CORS
        async function testCORS() {
            log('开始测试CORS');
            showResult('cors-results', '开始测试CORS...', 'info');
            
            try {
                const response = await fetch('http://localhost:5000/api/health', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    credentials: 'include'
                });
                
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
                    'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials')
                };
                
                const message = `✅ CORS测试成功! 响应头: ${JSON.stringify(corsHeaders, null, 2)}`;
                showResult('cors-results', `<pre>${message}</pre>`, 'success');
                log('CORS测试成功', 'success');
                
            } catch (error) {
                const message = `❌ CORS测试失败: ${error.message}`;
                showResult('cors-results', message, 'error');
                log(message, 'error');
            }
        }

        // 使用Fetch测试登录
        async function testLoginFetch() {
            const username = document.getElementById('test-username').value;
            const password = document.getElementById('test-password').value;
            
            log(`开始使用Fetch测试登录: ${username}`);
            showResult('login-results', '使用Fetch API测试登录...', 'info');
            
            try {
                const loginData = { username, password };
                
                const response = await fetch('http://localhost:5000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify(loginData)
                });
                
                log(`Fetch响应状态: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    const message = `✅ Fetch登录成功! 用户: ${data.user.username}, 管理员: ${data.user.is_admin}`;
                    showResult('login-results', message, 'success');
                    log(message, 'success');
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
                }
                
            } catch (error) {
                const message = `❌ Fetch登录失败: ${error.message}`;
                showResult('login-results', message, 'error');
                log(message, 'error');
            }
        }

        // 使用XMLHttpRequest测试登录
        function testLoginXHR() {
            const username = document.getElementById('test-username').value;
            const password = document.getElementById('test-password').value;
            
            log(`开始使用XHR测试登录: ${username}`);
            showResult('login-results', '使用XMLHttpRequest测试登录...', 'info');
            
            const xhr = new XMLHttpRequest();
            xhr.open('POST', 'http://localhost:5000/api/auth/login', true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.setRequestHeader('Accept', 'application/json');
            xhr.withCredentials = true;
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    log(`XHR状态: ${xhr.status}, 响应: ${xhr.responseText}`);
                    
                    if (xhr.status === 200) {
                        try {
                            const data = JSON.parse(xhr.responseText);
                            const message = `✅ XHR登录成功! 用户: ${data.user.username}, 管理员: ${data.user.is_admin}`;
                            showResult('login-results', message, 'success');
                            log(message, 'success');
                        } catch (e) {
                            const message = `❌ XHR响应解析失败: ${e.message}`;
                            showResult('login-results', message, 'error');
                            log(message, 'error');
                        }
                    } else {
                        const message = `❌ XHR登录失败: HTTP ${xhr.status} ${xhr.statusText}`;
                        showResult('login-results', message, 'error');
                        log(message, 'error');
                    }
                }
            };
            
            xhr.onerror = function() {
                const message = '❌ XHR网络错误';
                showResult('login-results', message, 'error');
                log(message, 'error');
            };
            
            xhr.ontimeout = function() {
                const message = '❌ XHR请求超时';
                showResult('login-results', message, 'error');
                log(message, 'error');
            };
            
            xhr.timeout = 10000;
            
            try {
                const loginData = { username, password };
                xhr.send(JSON.stringify(loginData));
                log('XHR请求已发送');
            } catch (e) {
                const message = `❌ XHR发送失败: ${e.message}`;
                showResult('login-results', message, 'error');
                log(message, 'error');
            }
        }

        // 显示浏览器信息
        function showBrowserInfo() {
            const info = document.getElementById('browser-info');
            info.innerHTML = `
                <div class="test-result info">
                    <strong>浏览器信息:</strong><br>
                    用户代理: ${navigator.userAgent}<br>
                    语言: ${navigator.language}<br>
                    平台: ${navigator.platform}<br>
                    在线状态: ${navigator.onLine ? '在线' : '离线'}<br>
                    Cookie启用: ${navigator.cookieEnabled ? '是' : '否'}<br>
                    当前URL: ${window.location.href}<br>
                    协议: ${window.location.protocol}<br>
                    主机: ${window.location.host}<br>
                    端口: ${window.location.port || '默认'}
                </div>
            `;
        }

        // 清除日志
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('测试页面加载完成');
            showBrowserInfo();
            
            // 自动测试连接
            setTimeout(() => {
                testConnection();
            }, 1000);
        });
    </script>
</body>
</html>
