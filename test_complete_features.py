#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
青云小铺完整功能测试脚本
测试所有新开发的功能和页面
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = 'http://localhost:5000'
FRONTEND_URL = 'http://localhost:8080'

def print_section(title):
    """打印章节标题"""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")

def print_test(test_name, status, details=""):
    """打印测试结果"""
    status_icon = "✅" if status else "❌"
    print(f"{status_icon} {test_name}")
    if details:
        print(f"   {details}")

def test_new_admin_pages():
    """测试新的管理后台页面"""
    print_section("🎛️ 新管理后台页面测试")
    
    pages = [
        ("数据统计页面", "/frontend/admin/analytics.html"),
        ("系统设置页面", "/frontend/admin/settings.html"),
        ("优化版商品管理", "/frontend/admin/admin_optimized.html"),
        ("优化版订单管理", "/frontend/admin/orders_optimized.html"),
        ("优化版登录页面", "/frontend/admin/login_fixed.html"),
    ]
    
    results = []
    
    for page_name, page_path in pages:
        try:
            response = requests.get(f"{FRONTEND_URL}{page_path}", timeout=5)
            success = response.status_code == 200
            details = f"状态码: {response.status_code}"
            if success:
                content_length = len(response.content)
                details += f", 大小: {content_length} bytes"
                # 检查是否包含青云小铺品牌
                if "青云小铺" in response.text:
                    details += ", 品牌更新: ✓"
                else:
                    details += ", 品牌更新: ✗"
            print_test(page_name, success, details)
            results.append(success)
        except Exception as e:
            print_test(page_name, False, f"错误: {e}")
            results.append(False)
    
    return all(results)

def test_payment_pages():
    """测试支付相关页面"""
    print_section("💳 支付功能页面测试")
    
    pages = [
        ("优化版结算页面", "/frontend/checkout_optimized.html"),
        ("订单成功页面", "/frontend/order_success.html"),
    ]
    
    results = []
    
    for page_name, page_path in pages:
        try:
            response = requests.get(f"{FRONTEND_URL}{page_path}", timeout=5)
            success = response.status_code == 200
            details = f"状态码: {response.status_code}"
            if success:
                content_length = len(response.content)
                details += f", 大小: {content_length} bytes"
                # 检查PayPal集成
                if "paypal" in response.text.lower():
                    details += ", PayPal集成: ✓"
                # 检查备案号
                if "粤ICP备2023114300号-1" in response.text:
                    details += ", 备案号: ✓"
            print_test(page_name, success, details)
            results.append(success)
        except Exception as e:
            print_test(page_name, False, f"错误: {e}")
            results.append(False)
    
    return all(results)

def test_brand_update():
    """测试品牌更新"""
    print_section("🏷️ 品牌更新测试")
    
    pages = [
        ("首页品牌更新", "/frontend/index_standalone.html"),
        ("购物车品牌更新", "/frontend/cart_optimized.html"),
        ("商品详情品牌更新", "/frontend/detail_optimized.html"),
        ("系统状态品牌更新", "/status_optimized.html"),
    ]
    
    results = []
    
    for page_name, page_path in pages:
        try:
            response = requests.get(f"{FRONTEND_URL}{page_path}", timeout=5)
            success = response.status_code == 200
            
            if success:
                content = response.text
                has_new_brand = "青云小铺" in content
                has_icp = "粤ICP备2023114300号-1" in content
                has_old_brand = "发卡商城" in content
                
                details = f"青云小铺: {'✓' if has_new_brand else '✗'}, "
                details += f"备案号: {'✓' if has_icp else '✗'}, "
                details += f"旧品牌清理: {'✗' if has_old_brand else '✓'}"
                
                success = has_new_brand and not has_old_brand
            else:
                details = f"状态码: {response.status_code}"
            
            print_test(page_name, success, details)
            results.append(success)
        except Exception as e:
            print_test(page_name, False, f"错误: {e}")
            results.append(False)
    
    return all(results)

def test_navigation_links():
    """测试导航链接"""
    print_section("🔗 导航链接测试")
    
    # 测试管理后台导航
    admin_pages = [
        "admin_optimized.html",
        "orders_optimized.html", 
        "analytics.html",
        "settings.html"
    ]
    
    results = []
    
    for page in admin_pages:
        try:
            response = requests.get(f"{FRONTEND_URL}/frontend/admin/{page}", timeout=5)
            success = response.status_code == 200
            
            if success:
                # 检查是否包含所有导航链接
                content = response.text
                has_all_links = all(link in content for link in admin_pages)
                details = f"状态码: {response.status_code}, 导航完整性: {'✓' if has_all_links else '✗'}"
            else:
                details = f"状态码: {response.status_code}"
            
            print_test(f"管理后台 - {page}", success, details)
            results.append(success)
        except Exception as e:
            print_test(f"管理后台 - {page}", False, f"错误: {e}")
            results.append(False)
    
    return all(results)

def test_javascript_functionality():
    """测试JavaScript功能"""
    print_section("⚡ JavaScript功能测试")
    
    # 这里我们测试页面是否包含必要的JavaScript代码
    js_tests = [
        ("购物车管理JS", "/frontend/cart_optimized.html", ["Cart.get", "Cart.add", "Cart.remove"]),
        ("结算页面JS", "/frontend/checkout_optimized.html", ["selectPaymentMethod", "placeOrder", "paypal"]),
        ("数据统计JS", "/frontend/admin/analytics.html", ["Chart.js", "loadStatistics", "initSalesChart"]),
        ("系统设置JS", "/frontend/admin/settings.html", ["toggleSwitch", "saveBasicSettings", "testPayPal"]),
    ]
    
    results = []
    
    for test_name, page_path, required_functions in js_tests:
        try:
            response = requests.get(f"{FRONTEND_URL}{page_path}", timeout=5)
            success = response.status_code == 200
            
            if success:
                content = response.text
                missing_functions = [func for func in required_functions if func not in content]
                
                if not missing_functions:
                    details = f"所有必需函数存在: ✓"
                    js_success = True
                else:
                    details = f"缺少函数: {', '.join(missing_functions)}"
                    js_success = False
                
                success = success and js_success
            else:
                details = f"状态码: {response.status_code}"
            
            print_test(test_name, success, details)
            results.append(success)
        except Exception as e:
            print_test(test_name, False, f"错误: {e}")
            results.append(False)
    
    return all(results)

def test_responsive_design():
    """测试响应式设计"""
    print_section("📱 响应式设计测试")
    
    pages = [
        ("数据统计响应式", "/frontend/admin/analytics.html"),
        ("系统设置响应式", "/frontend/admin/settings.html"),
        ("结算页面响应式", "/frontend/checkout_optimized.html"),
    ]
    
    results = []
    
    for page_name, page_path in pages:
        try:
            response = requests.get(f"{FRONTEND_URL}{page_path}", timeout=5)
            success = response.status_code == 200
            
            if success:
                content = response.text
                # 检查是否包含响应式CSS
                has_media_queries = "@media" in content
                has_mobile_styles = "max-width: 768px" in content or "max-width: 480px" in content
                
                details = f"媒体查询: {'✓' if has_media_queries else '✗'}, "
                details += f"移动端样式: {'✓' if has_mobile_styles else '✗'}"
                
                success = has_media_queries and has_mobile_styles
            else:
                details = f"状态码: {response.status_code}"
            
            print_test(page_name, success, details)
            results.append(success)
        except Exception as e:
            print_test(page_name, False, f"错误: {e}")
            results.append(False)
    
    return all(results)

def main():
    """主测试函数"""
    print("🛒 青云小铺 - 完整功能测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行所有测试
    test_results = []
    
    test_results.append(("新管理后台页面", test_new_admin_pages()))
    test_results.append(("支付功能页面", test_payment_pages()))
    test_results.append(("品牌更新", test_brand_update()))
    test_results.append(("导航链接", test_navigation_links()))
    test_results.append(("JavaScript功能", test_javascript_functionality()))
    test_results.append(("响应式设计", test_responsive_design()))
    
    # 汇总结果
    print_section("📊 测试结果汇总")
    
    all_passed = True
    for test_name, result in test_results:
        print_test(test_name, result)
        if not result:
            all_passed = False
    
    print(f"\n{'='*60}")
    if all_passed:
        print("🎉 所有测试通过！青云小铺功能完整且正常运行。")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
    
    print(f"\n🌐 访问地址:")
    print(f"   商城首页: {FRONTEND_URL}/frontend/index_standalone.html")
    print(f"   购物车: {FRONTEND_URL}/frontend/cart_optimized.html")
    print(f"   结算支付: {FRONTEND_URL}/frontend/checkout_optimized.html")
    print(f"   管理员登录: {FRONTEND_URL}/frontend/admin/login_fixed.html")
    print(f"   数据统计: {FRONTEND_URL}/frontend/admin/analytics.html")
    print(f"   系统设置: {FRONTEND_URL}/frontend/admin/settings.html")
    print(f"   系统状态: {FRONTEND_URL}/status_optimized.html")
    
    print(f"\n📋 新功能特性:")
    print(f"   ✅ 完整的数据统计面板（图表、销售趋势、热销商品）")
    print(f"   ✅ 系统设置管理（基本信息、支付配置、邮件设置、安全设置）")
    print(f"   ✅ PayPal支付集成（沙盒和生产模式）")
    print(f"   ✅ 加密货币支付支持（比特币、以太坊）")
    print(f"   ✅ 品牌更新为'青云小铺'")
    print(f"   ✅ 备案号添加（粤ICP备2023114300号-1）")
    print(f"   ✅ 完整的订单成功页面")
    print(f"   ✅ 响应式设计优化")
    
    print(f"\n🔐 默认管理员账户:")
    print(f"   用户名: admin")
    print(f"   密码: admin123")
    
    print(f"\n{'='*60}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {e}")
