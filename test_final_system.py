#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
青云小铺最终系统测试脚本
测试所有功能的完整性和真实数据
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = 'http://localhost:5000'
FRONTEND_URL = 'http://localhost:8080'

def print_section(title):
    """打印章节标题"""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")

def print_test(test_name, status, details=""):
    """打印测试结果"""
    status_icon = "✅" if status else "❌"
    print(f"{status_icon} {test_name}")
    if details:
        print(f"   {details}")

def test_brand_consistency():
    """测试品牌一致性"""
    print_section("🏷️ 品牌一致性测试")
    
    pages = [
        ("首页", "/frontend/index_standalone.html"),
        ("购物车", "/frontend/cart_optimized.html"),
        ("商品详情", "/frontend/detail_optimized.html"),
        ("结算支付", "/frontend/checkout_optimized.html"),
        ("订单成功", "/frontend/order_success.html"),
        ("发货内容", "/frontend/delivery_content.html"),
        ("系统状态", "/status_optimized.html"),
        ("管理员登录", "/frontend/admin/login_fixed.html"),
        ("商品管理", "/frontend/admin/admin_optimized.html"),
        ("订单管理", "/frontend/admin/orders_optimized.html"),
        ("数据统计", "/frontend/admin/analytics.html"),
        ("系统设置", "/frontend/admin/settings.html"),
        ("系统状态监控", "/frontend/admin/system_status.html"),
        ("商品编辑", "/frontend/admin/edit_product_optimized.html"),
    ]
    
    results = []
    
    for page_name, page_path in pages:
        try:
            response = requests.get(f"{FRONTEND_URL}{page_path}", timeout=5)
            success = response.status_code == 200
            
            if success:
                content = response.text
                has_new_brand = "青云小铺" in content
                has_old_brand = "发卡商城" in content
                has_icp = "粤ICP备2023114300号-1" in content
                has_logo = "8304CEF178EEF908F89E309C65C1E521.jpg" in content
                
                details = f"青云小铺: {'✓' if has_new_brand else '✗'}, "
                details += f"备案号: {'✓' if has_icp else '✗'}, "
                details += f"Logo: {'✓' if has_logo else '✗'}, "
                details += f"旧品牌清理: {'✓' if not has_old_brand else '✗'}"
                
                success = has_new_brand and not has_old_brand
            else:
                details = f"状态码: {response.status_code}"
            
            print_test(page_name, success, details)
            results.append(success)
        except Exception as e:
            print_test(page_name, False, f"错误: {e}")
            results.append(False)
    
    return all(results)

def test_real_data_analytics():
    """测试数据统计的真实数据"""
    print_section("📊 真实数据统计测试")
    
    try:
        # 测试数据统计页面
        response = requests.get(f"{FRONTEND_URL}/frontend/admin/analytics.html", timeout=5)
        
        if response.status_code == 200:
            content = response.text
            
            # 检查是否包含真实数据处理逻辑
            has_real_data_logic = all(keyword in content for keyword in [
                "loadStatistics", "真实", "orders.reduce", "parseFloat"
            ])
            
            # 检查是否移除了模拟数据
            has_mock_data = any(keyword in content for keyword in [
                "Math.random", "模拟", "演示", "示例"
            ])
            
            print_test("数据统计真实数据", has_real_data_logic and not has_mock_data, 
                      f"真实数据逻辑: {'✓' if has_real_data_logic else '✗'}, 移除模拟数据: {'✓' if not has_mock_data else '✗'}")
            
            return has_real_data_logic and not has_mock_data
        else:
            print_test("数据统计页面访问", False, f"状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print_test("数据统计测试", False, f"错误: {e}")
        return False

def test_export_functionality():
    """测试数据导出功能"""
    print_section("📤 数据导出功能测试")
    
    try:
        response = requests.get(f"{FRONTEND_URL}/frontend/admin/analytics.html", timeout=5)
        
        if response.status_code == 200:
            content = response.text
            
            # 检查导出功能
            has_export_function = "exportData" in content and "downloadExcel" in content
            has_csv_conversion = "convertToCSV" in content
            has_file_download = "downloadFile" in content
            
            print_test("导出函数存在", has_export_function, 
                      f"导出函数: {'✓' if has_export_function else '✗'}")
            print_test("CSV转换功能", has_csv_conversion, 
                      f"CSV转换: {'✓' if has_csv_conversion else '✗'}")
            print_test("文件下载功能", has_file_download, 
                      f"文件下载: {'✓' if has_file_download else '✗'}")
            
            return has_export_function and has_csv_conversion and has_file_download
        else:
            print_test("导出功能页面访问", False, f"状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print_test("导出功能测试", False, f"错误: {e}")
        return False

def test_system_maintenance():
    """测试系统维护功能"""
    print_section("🔧 系统维护功能测试")
    
    try:
        response = requests.get(f"{FRONTEND_URL}/frontend/admin/settings.html", timeout=5)
        
        if response.status_code == 200:
            content = response.text
            
            # 检查维护功能
            has_backup_function = "backupDatabase" in content and "备份数据库" in content
            has_cache_clear = "clearCache" in content and "清除缓存" in content
            has_session_clear = "clearSessions" in content and "清除所有会话" in content
            
            # 检查是否有实际实现而不是占位符
            has_real_implementation = "正在备份数据库" in content and "caches.delete" in content
            
            print_test("数据库备份功能", has_backup_function, 
                      f"备份功能: {'✓' if has_backup_function else '✗'}")
            print_test("缓存清理功能", has_cache_clear, 
                      f"缓存清理: {'✓' if has_cache_clear else '✗'}")
            print_test("会话清理功能", has_session_clear, 
                      f"会话清理: {'✓' if has_session_clear else '✗'}")
            print_test("功能实际实现", has_real_implementation, 
                      f"实际实现: {'✓' if has_real_implementation else '✗'}")
            
            return has_backup_function and has_cache_clear and has_session_clear and has_real_implementation
        else:
            print_test("系统维护页面访问", False, f"状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print_test("系统维护测试", False, f"错误: {e}")
        return False

def test_system_monitoring():
    """测试系统状态监控"""
    print_section("🔍 系统状态监控测试")
    
    try:
        response = requests.get(f"{FRONTEND_URL}/frontend/admin/system_status.html", timeout=5)
        
        if response.status_code == 200:
            content = response.text
            
            # 检查监控功能
            has_real_time_metrics = "实时指标" in content and "在线用户" in content
            has_server_monitoring = "服务器状态" in content and "CPU使用率" in content
            has_network_monitoring = "网络状态" in content and "带宽使用" in content
            has_database_monitoring = "数据库状态" in content and "连接数" in content
            has_performance_chart = "性能趋势" in content and "performanceChart" in content
            
            # 检查是否有实时更新功能
            has_auto_refresh = "setInterval" in content and "refreshAllData" in content
            
            print_test("实时指标监控", has_real_time_metrics, 
                      f"实时指标: {'✓' if has_real_time_metrics else '✗'}")
            print_test("服务器监控", has_server_monitoring, 
                      f"服务器监控: {'✓' if has_server_monitoring else '✗'}")
            print_test("网络监控", has_network_monitoring, 
                      f"网络监控: {'✓' if has_network_monitoring else '✗'}")
            print_test("数据库监控", has_database_monitoring, 
                      f"数据库监控: {'✓' if has_database_monitoring else '✗'}")
            print_test("性能图表", has_performance_chart, 
                      f"性能图表: {'✓' if has_performance_chart else '✗'}")
            print_test("自动刷新", has_auto_refresh, 
                      f"自动刷新: {'✓' if has_auto_refresh else '✗'}")
            
            return all([has_real_time_metrics, has_server_monitoring, has_network_monitoring, 
                       has_database_monitoring, has_performance_chart, has_auto_refresh])
        else:
            print_test("系统监控页面访问", False, f"状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print_test("系统监控测试", False, f"错误: {e}")
        return False

def test_virtual_goods_delivery():
    """测试虚拟商品发货系统"""
    print_section("📦 虚拟商品发货系统测试")
    
    pages_to_test = [
        ("商品编辑页面", "/frontend/admin/edit_product_optimized.html", 
         ["delivery_type", "delivery_content", "email_template", "switchContentType"]),
        ("发货内容页面", "/frontend/delivery_content.html", 
         ["loadDeliveryContent", "copyContent", "发货内容"]),
        ("结算支付集成", "/frontend/checkout_optimized.html", 
         ["handlePaymentSuccess", "delivery", "发货"])
    ]
    
    results = []
    
    for page_name, page_path, required_features in pages_to_test:
        try:
            response = requests.get(f"{FRONTEND_URL}{page_path}", timeout=5)
            success = response.status_code == 200
            
            if success:
                content = response.text
                missing_features = [feature for feature in required_features if feature not in content]
                
                if not missing_features:
                    details = f"所有功能存在: ✓"
                    feature_success = True
                else:
                    details = f"缺少功能: {', '.join(missing_features)}"
                    feature_success = False
                
                success = success and feature_success
            else:
                details = f"状态码: {response.status_code}"
            
            print_test(page_name, success, details)
            results.append(success)
        except Exception as e:
            print_test(page_name, False, f"错误: {e}")
            results.append(False)
    
    return all(results)

def test_navigation_consistency():
    """测试导航一致性"""
    print_section("🧭 导航一致性测试")
    
    admin_pages = [
        "admin_optimized.html",
        "orders_optimized.html", 
        "analytics.html",
        "settings.html",
        "system_status.html",
        "edit_product_optimized.html"
    ]
    
    expected_nav_items = [
        "📦 商品管理",
        "📋 订单管理", 
        "📊 数据统计",
        "⚙️ 系统设置",
        "🔍 系统状态"
    ]
    
    results = []
    
    for page in admin_pages:
        try:
            response = requests.get(f"{FRONTEND_URL}/frontend/admin/{page}", timeout=5)
            success = response.status_code == 200
            
            if success:
                content = response.text
                missing_nav = [item for item in expected_nav_items if item not in content]
                
                if not missing_nav:
                    details = f"导航完整: ✓"
                    nav_success = True
                else:
                    details = f"缺少导航: {', '.join(missing_nav)}"
                    nav_success = False
                
                success = success and nav_success
            else:
                details = f"状态码: {response.status_code}"
            
            print_test(f"管理后台 - {page}", success, details)
            results.append(success)
        except Exception as e:
            print_test(f"管理后台 - {page}", False, f"错误: {e}")
            results.append(False)
    
    return all(results)

def main():
    """主测试函数"""
    print("🛒 青云小铺 - 最终系统完整性测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行所有测试
    test_results = []
    
    test_results.append(("品牌一致性", test_brand_consistency()))
    test_results.append(("真实数据统计", test_real_data_analytics()))
    test_results.append(("数据导出功能", test_export_functionality()))
    test_results.append(("系统维护功能", test_system_maintenance()))
    test_results.append(("系统状态监控", test_system_monitoring()))
    test_results.append(("虚拟商品发货", test_virtual_goods_delivery()))
    test_results.append(("导航一致性", test_navigation_consistency()))
    
    # 汇总结果
    print_section("📊 最终测试结果汇总")
    
    all_passed = True
    for test_name, result in test_results:
        print_test(test_name, result)
        if not result:
            all_passed = False
    
    print(f"\n{'='*60}")
    if all_passed:
        print("🎉 所有测试通过！青云小铺系统功能完整且正常运行。")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
    
    print(f"\n✨ 青云小铺完整功能清单:")
    print(f"   ✅ 品牌统一更新（发卡商城 → 青云小铺）")
    print(f"   ✅ Logo和备案号完整添加")
    print(f"   ✅ 真实数据统计（非模拟数据）")
    print(f"   ✅ 完整的数据导出功能（CSV/Excel）")
    print(f"   ✅ 系统维护功能（备份、缓存清理、会话管理）")
    print(f"   ✅ 实时系统状态监控（CPU、内存、网络、数据库）")
    print(f"   ✅ 虚拟商品发货系统（内容管理、自动发货、邮件通知）")
    print(f"   ✅ PayPal支付集成")
    print(f"   ✅ 完整的管理后台")
    print(f"   ✅ 响应式设计")
    
    print(f"\n🌐 系统访问地址:")
    print(f"   商城首页: {FRONTEND_URL}/frontend/index_standalone.html")
    print(f"   管理后台: {FRONTEND_URL}/frontend/admin/login_fixed.html")
    print(f"   系统状态: {FRONTEND_URL}/frontend/admin/system_status.html")
    print(f"   数据统计: {FRONTEND_URL}/frontend/admin/analytics.html")
    print(f"   系统设置: {FRONTEND_URL}/frontend/admin/settings.html")
    
    print(f"\n🔐 默认管理员账户:")
    print(f"   用户名: admin")
    print(f"   密码: admin123")
    
    print(f"\n{'='*60}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {e}")
