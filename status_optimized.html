<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统状态 - 青云小铺</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            padding: 2rem 1rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
        }
        
        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .status-card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .status-card:hover {
            transform: translateY(-4px);
        }
        
        .status-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .status-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }
        
        .status-icon.success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-icon.warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-icon.error {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .status-icon.info {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .status-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .status-content {
            color: #6b7280;
            line-height: 1.6;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-label {
            font-weight: 500;
            color: #374151;
        }
        
        .status-value {
            font-weight: 600;
            color: #1f2937;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .badge-success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .badge-error {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            color: #6b7280;
        }
        
        .spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #f3f4f6;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
        }
        
        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        .btn-outline {
            background: rgba(255,255,255,0.9);
            color: #667eea;
            border: 2px solid rgba(255,255,255,0.5);
        }
        
        .btn-outline:hover {
            background: white;
            border-color: white;
        }
        
        .footer {
            text-align: center;
            color: rgba(255,255,255,0.8);
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(255,255,255,0.1);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .status-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .status-card {
                padding: 1.5rem;
            }
            
            .actions {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛒 青云小铺系统状态</h1>
            <p>实时监控系统运行状态和性能指标</p>
        </div>
        
        <div class="status-grid">
            <!-- 系统状态 -->
            <div class="status-card">
                <div class="status-header">
                    <div class="status-icon success" id="system-icon">🟢</div>
                    <div class="status-title">系统状态</div>
                </div>
                <div class="status-content" id="system-status">
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>检查中...</p>
                    </div>
                </div>
            </div>
            
            <!-- API状态 -->
            <div class="status-card">
                <div class="status-header">
                    <div class="status-icon info" id="api-icon">🔗</div>
                    <div class="status-title">API服务</div>
                </div>
                <div class="status-content" id="api-status">
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>检查中...</p>
                    </div>
                </div>
            </div>
            
            <!-- 数据库状态 -->
            <div class="status-card">
                <div class="status-header">
                    <div class="status-icon info" id="db-icon">🗄️</div>
                    <div class="status-title">数据库</div>
                </div>
                <div class="status-content" id="db-status">
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>检查中...</p>
                    </div>
                </div>
            </div>
            
            <!-- 性能指标 -->
            <div class="status-card">
                <div class="status-header">
                    <div class="status-icon info" id="perf-icon">📊</div>
                    <div class="status-title">性能指标</div>
                </div>
                <div class="status-content" id="perf-status">
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>检查中...</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="actions">
            <a href="frontend/index_standalone.html" class="btn btn-primary">访问商城</a>
            <a href="frontend/admin/login_optimized.html" class="btn btn-outline">管理后台</a>
            <button class="btn btn-outline" onclick="refreshStatus()">刷新状态</button>
        </div>
        
        <div class="footer">
            <p>最后更新: <span id="last-update">--</span></p>
            <p>&copy; 2025 青云小铺. 保留所有权利. | 粤ICP备2023114300号-1</p>
        </div>
    </div>

    <script>
        // 状态检查结果
        let statusResults = {
            system: null,
            api: null,
            database: null,
            performance: null
        };

        // 更新状态显示
        function updateStatusDisplay() {
            updateSystemStatus();
            updateAPIStatus();
            updateDatabaseStatus();
            updatePerformanceStatus();
            
            // 更新最后更新时间
            document.getElementById('last-update').textContent = new Date().toLocaleString('zh-CN');
        }

        // 更新系统状态
        function updateSystemStatus() {
            const container = document.getElementById('system-status');
            const icon = document.getElementById('system-icon');
            
            if (!statusResults.system) {
                return;
            }
            
            const { healthy, services } = statusResults.system;
            
            if (healthy) {
                icon.className = 'status-icon success';
                icon.textContent = '✅';
            } else {
                icon.className = 'status-icon error';
                icon.textContent = '❌';
            }
            
            container.innerHTML = `
                <div class="status-item">
                    <span class="status-label">整体状态</span>
                    <span class="status-badge ${healthy ? 'badge-success' : 'badge-error'}">
                        ${healthy ? '正常' : '异常'}
                    </span>
                </div>
                <div class="status-item">
                    <span class="status-label">前端服务</span>
                    <span class="status-badge ${services.frontend ? 'badge-success' : 'badge-error'}">
                        ${services.frontend ? '运行中' : '离线'}
                    </span>
                </div>
                <div class="status-item">
                    <span class="status-label">后端服务</span>
                    <span class="status-badge ${services.backend ? 'badge-success' : 'badge-error'}">
                        ${services.backend ? '运行中' : '离线'}
                    </span>
                </div>
            `;
        }

        // 更新API状态
        function updateAPIStatus() {
            const container = document.getElementById('api-status');
            const icon = document.getElementById('api-icon');
            
            if (!statusResults.api) {
                return;
            }
            
            const { healthy, endpoints, responseTime } = statusResults.api;
            
            if (healthy) {
                icon.className = 'status-icon success';
                icon.textContent = '✅';
            } else {
                icon.className = 'status-icon error';
                icon.textContent = '❌';
            }
            
            container.innerHTML = `
                <div class="status-item">
                    <span class="status-label">API状态</span>
                    <span class="status-badge ${healthy ? 'badge-success' : 'badge-error'}">
                        ${healthy ? '正常' : '异常'}
                    </span>
                </div>
                <div class="status-item">
                    <span class="status-label">响应时间</span>
                    <span class="status-value">${responseTime}ms</span>
                </div>
                <div class="status-item">
                    <span class="status-label">可用端点</span>
                    <span class="status-value">${endpoints.working}/${endpoints.total}</span>
                </div>
            `;
        }

        // 更新数据库状态
        function updateDatabaseStatus() {
            const container = document.getElementById('db-status');
            const icon = document.getElementById('db-icon');
            
            if (!statusResults.database) {
                return;
            }
            
            const { healthy, products, orders } = statusResults.database;
            
            if (healthy) {
                icon.className = 'status-icon success';
                icon.textContent = '✅';
            } else {
                icon.className = 'status-icon error';
                icon.textContent = '❌';
            }
            
            container.innerHTML = `
                <div class="status-item">
                    <span class="status-label">数据库状态</span>
                    <span class="status-badge ${healthy ? 'badge-success' : 'badge-error'}">
                        ${healthy ? '正常' : '异常'}
                    </span>
                </div>
                <div class="status-item">
                    <span class="status-label">商品数量</span>
                    <span class="status-value">${products}</span>
                </div>
                <div class="status-item">
                    <span class="status-label">订单数量</span>
                    <span class="status-value">${orders}</span>
                </div>
            `;
        }

        // 更新性能指标
        function updatePerformanceStatus() {
            const container = document.getElementById('perf-status');
            const icon = document.getElementById('perf-icon');
            
            if (!statusResults.performance) {
                return;
            }
            
            const { memory, uptime, requests } = statusResults.performance;
            
            icon.className = 'status-icon info';
            icon.textContent = '📊';
            
            container.innerHTML = `
                <div class="status-item">
                    <span class="status-label">内存使用</span>
                    <span class="status-value">${memory}</span>
                </div>
                <div class="status-item">
                    <span class="status-label">运行时间</span>
                    <span class="status-value">${uptime}</span>
                </div>
                <div class="status-item">
                    <span class="status-label">请求总数</span>
                    <span class="status-value">${requests}</span>
                </div>
            `;
        }

        // 检查系统状态
        async function checkSystemStatus() {
            try {
                // 检查前端服务 (当前页面能加载说明前端正常)
                const frontendHealthy = true;
                
                // 检查后端服务
                let backendHealthy = false;
                try {
                    const response = await fetch('http://localhost:5000/api/health', {
                        method: 'GET',
                        timeout: 5000
                    });
                    backendHealthy = response.ok;
                } catch (e) {
                    backendHealthy = false;
                }
                
                statusResults.system = {
                    healthy: frontendHealthy && backendHealthy,
                    services: {
                        frontend: frontendHealthy,
                        backend: backendHealthy
                    }
                };
                
            } catch (error) {
                console.error('系统状态检查失败:', error);
                statusResults.system = {
                    healthy: false,
                    services: {
                        frontend: false,
                        backend: false
                    }
                };
            }
        }

        // 检查API状态
        async function checkAPIStatus() {
            try {
                const startTime = Date.now();
                
                // 测试多个API端点
                const endpoints = [
                    '/api/health',
                    '/api/products',
                    '/api/orders'
                ];
                
                let workingEndpoints = 0;
                
                for (const endpoint of endpoints) {
                    try {
                        const response = await fetch(`http://localhost:5000${endpoint}`, {
                            method: 'GET',
                            timeout: 3000
                        });
                        if (response.ok) {
                            workingEndpoints++;
                        }
                    } catch (e) {
                        // 端点不可用
                    }
                }
                
                const responseTime = Date.now() - startTime;
                const healthy = workingEndpoints > 0;
                
                statusResults.api = {
                    healthy,
                    responseTime,
                    endpoints: {
                        working: workingEndpoints,
                        total: endpoints.length
                    }
                };
                
            } catch (error) {
                console.error('API状态检查失败:', error);
                statusResults.api = {
                    healthy: false,
                    responseTime: 0,
                    endpoints: {
                        working: 0,
                        total: 3
                    }
                };
            }
        }

        // 检查数据库状态
        async function checkDatabaseStatus() {
            try {
                // 获取产品数量
                let productCount = 0;
                let orderCount = 0;
                let healthy = false;
                
                try {
                    const productsResponse = await fetch('http://localhost:5000/api/products');
                    if (productsResponse.ok) {
                        const productsData = await productsResponse.json();
                        productCount = productsData.total || 0;
                        healthy = true;
                    }
                } catch (e) {
                    // 产品API不可用
                }
                
                try {
                    const ordersResponse = await fetch('http://localhost:5000/api/orders');
                    if (ordersResponse.ok) {
                        const ordersData = await ordersResponse.json();
                        orderCount = ordersData.total || 0;
                    }
                } catch (e) {
                    // 订单API不可用
                }
                
                statusResults.database = {
                    healthy,
                    products: productCount,
                    orders: orderCount
                };
                
            } catch (error) {
                console.error('数据库状态检查失败:', error);
                statusResults.database = {
                    healthy: false,
                    products: 0,
                    orders: 0
                };
            }
        }

        // 检查性能指标
        async function checkPerformanceStatus() {
            try {
                // 模拟性能数据 (实际应用中应该从后端获取)
                const memory = '45.2 MB';
                const uptime = formatUptime(Date.now() - performance.timeOrigin);
                const requests = Math.floor(Math.random() * 1000) + 500;
                
                statusResults.performance = {
                    memory,
                    uptime,
                    requests
                };
                
            } catch (error) {
                console.error('性能指标检查失败:', error);
                statusResults.performance = {
                    memory: '未知',
                    uptime: '未知',
                    requests: 0
                };
            }
        }

        // 格式化运行时间
        function formatUptime(ms) {
            const seconds = Math.floor(ms / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);
            const days = Math.floor(hours / 24);
            
            if (days > 0) {
                return `${days}天 ${hours % 24}小时`;
            } else if (hours > 0) {
                return `${hours}小时 ${minutes % 60}分钟`;
            } else if (minutes > 0) {
                return `${minutes}分钟 ${seconds % 60}秒`;
            } else {
                return `${seconds}秒`;
            }
        }

        // 刷新状态
        async function refreshStatus() {
            console.log('刷新系统状态...');
            
            // 重置所有状态为加载中
            document.querySelectorAll('.status-content').forEach(el => {
                el.innerHTML = `
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>检查中...</p>
                    </div>
                `;
            });
            
            // 并行检查所有状态
            await Promise.all([
                checkSystemStatus(),
                checkAPIStatus(),
                checkDatabaseStatus(),
                checkPerformanceStatus()
            ]);
            
            // 更新显示
            updateStatusDisplay();
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('系统状态页面加载完成');
            refreshStatus();
            
            // 每30秒自动刷新一次
            setInterval(refreshStatus, 30000);
        });
    </script>
</body>
</html>
