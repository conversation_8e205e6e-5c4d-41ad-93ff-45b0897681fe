<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 300px;
        }
    </style>
</head>
<body>
    <h1>🔧 发卡商城 API 调试页面</h1>
    
    <div class="test-section">
        <h2>基础连接测试</h2>
        <button onclick="testBasicConnection()">测试基础连接</button>
        <button onclick="testCORS()">测试CORS</button>
        <button onclick="testHealthAPI()">测试健康检查API</button>
        <div id="basic-results"></div>
    </div>
    
    <div class="test-section">
        <h2>产品API测试</h2>
        <button onclick="testProductsAPI()">测试产品列表API</button>
        <button onclick="testProductsWithFetch()">使用原生Fetch测试</button>
        <button onclick="testProductsWithXHR()">使用XMLHttpRequest测试</button>
        <div id="products-results"></div>
    </div>
    
    <div class="test-section">
        <h2>网络诊断</h2>
        <button onclick="diagnoseNetwork()">网络诊断</button>
        <button onclick="testDifferentURLs()">测试不同URL格式</button>
        <div id="network-results"></div>
    </div>
    
    <div class="test-section">
        <h2>浏览器信息</h2>
        <div id="browser-info"></div>
    </div>

    <script>
        // 显示结果的辅助函数
        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }

        // 测试基础连接
        async function testBasicConnection() {
            showResult('basic-results', '开始测试基础连接...', 'info');
            
            try {
                const response = await fetch('http://localhost:5000/api/health');
                showResult('basic-results', `✅ 连接成功! 状态码: ${response.status}`, 'success');
                
                const data = await response.json();
                showResult('basic-results', `📄 响应数据: ${JSON.stringify(data)}`, 'success');
            } catch (error) {
                showResult('basic-results', `❌ 连接失败: ${error.message}`, 'error');
                showResult('basic-results', `🔍 错误详情: ${error.stack}`, 'error');
            }
        }

        // 测试CORS
        async function testCORS() {
            showResult('basic-results', '开始测试CORS...', 'info');
            
            try {
                const response = await fetch('http://localhost:5000/api/health', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include'
                });
                
                showResult('basic-results', `✅ CORS测试成功! 状态码: ${response.status}`, 'success');
                showResult('basic-results', `📋 响应头: ${JSON.stringify([...response.headers.entries()])}`, 'info');
            } catch (error) {
                showResult('basic-results', `❌ CORS测试失败: ${error.message}`, 'error');
            }
        }

        // 测试健康检查API
        async function testHealthAPI() {
            showResult('basic-results', '测试健康检查API...', 'info');
            
            const urls = [
                'http://localhost:5000/api/health',
                'http://127.0.0.1:5000/api/health'
            ];
            
            for (const url of urls) {
                try {
                    const response = await fetch(url);
                    const data = await response.json();
                    showResult('basic-results', `✅ ${url} - 成功: ${JSON.stringify(data)}`, 'success');
                } catch (error) {
                    showResult('basic-results', `❌ ${url} - 失败: ${error.message}`, 'error');
                }
            }
        }

        // 测试产品API
        async function testProductsAPI() {
            showResult('products-results', '测试产品API...', 'info');
            
            try {
                const response = await fetch('http://localhost:5000/api/products');
                showResult('products-results', `✅ 产品API响应状态: ${response.status}`, 'success');
                
                const data = await response.json();
                showResult('products-results', `📊 产品数据: ${JSON.stringify(data, null, 2)}`, 'success');
            } catch (error) {
                showResult('products-results', `❌ 产品API失败: ${error.message}`, 'error');
            }
        }

        // 使用原生Fetch测试
        async function testProductsWithFetch() {
            showResult('products-results', '使用原生Fetch测试...', 'info');
            
            try {
                const response = await fetch('http://localhost:5000/api/products', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    mode: 'cors'
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                showResult('products-results', `✅ Fetch成功: 获取到 ${data.total || 0} 个产品`, 'success');
            } catch (error) {
                showResult('products-results', `❌ Fetch失败: ${error.message}`, 'error');
            }
        }

        // 使用XMLHttpRequest测试
        function testProductsWithXHR() {
            showResult('products-results', '使用XMLHttpRequest测试...', 'info');
            
            const xhr = new XMLHttpRequest();
            xhr.open('GET', 'http://localhost:5000/api/products', true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            const data = JSON.parse(xhr.responseText);
                            showResult('products-results', `✅ XHR成功: ${JSON.stringify(data)}`, 'success');
                        } catch (e) {
                            showResult('products-results', `❌ XHR解析失败: ${e.message}`, 'error');
                        }
                    } else {
                        showResult('products-results', `❌ XHR失败: ${xhr.status} ${xhr.statusText}`, 'error');
                    }
                }
            };
            
            xhr.onerror = function() {
                showResult('products-results', `❌ XHR网络错误`, 'error');
            };
            
            xhr.send();
        }

        // 网络诊断
        async function diagnoseNetwork() {
            showResult('network-results', '开始网络诊断...', 'info');
            
            // 检查网络连接
            if (navigator.onLine) {
                showResult('network-results', '✅ 浏览器显示在线状态', 'success');
            } else {
                showResult('network-results', '❌ 浏览器显示离线状态', 'error');
            }
            
            // 检查当前页面URL
            showResult('network-results', `📍 当前页面: ${window.location.href}`, 'info');
            
            // 检查用户代理
            showResult('network-results', `🌐 用户代理: ${navigator.userAgent}`, 'info');
            
            // 检查协议
            showResult('network-results', `🔒 协议: ${window.location.protocol}`, 'info');
        }

        // 测试不同URL格式
        async function testDifferentURLs() {
            showResult('network-results', '测试不同URL格式...', 'info');
            
            const urls = [
                'http://localhost:5000/api/products',
                'http://127.0.0.1:5000/api/products',
                'http://0.0.0.0:5000/api/products'
            ];
            
            for (const url of urls) {
                try {
                    const startTime = Date.now();
                    const response = await fetch(url);
                    const endTime = Date.now();
                    
                    showResult('network-results', `✅ ${url} - 成功 (${endTime - startTime}ms)`, 'success');
                } catch (error) {
                    showResult('network-results', `❌ ${url} - 失败: ${error.message}`, 'error');
                }
            }
        }

        // 显示浏览器信息
        function showBrowserInfo() {
            const info = document.getElementById('browser-info');
            info.innerHTML = `
                <div class="test-result info">
                    <strong>浏览器信息:</strong><br>
                    用户代理: ${navigator.userAgent}<br>
                    语言: ${navigator.language}<br>
                    平台: ${navigator.platform}<br>
                    在线状态: ${navigator.onLine ? '在线' : '离线'}<br>
                    Cookie启用: ${navigator.cookieEnabled ? '是' : '否'}<br>
                    当前URL: ${window.location.href}<br>
                    协议: ${window.location.protocol}<br>
                    主机: ${window.location.host}
                </div>
            `;
        }

        // 页面加载时显示浏览器信息
        document.addEventListener('DOMContentLoaded', () => {
            showBrowserInfo();
            showResult('basic-results', '🚀 调试页面已加载，可以开始测试', 'info');
        });
    </script>
</body>
</html>
