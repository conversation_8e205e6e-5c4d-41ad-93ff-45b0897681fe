#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化后功能测试脚本
测试所有优化后的功能和页面
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = 'http://localhost:5000'
FRONTEND_URL = 'http://localhost:8080'

def print_section(title):
    """打印章节标题"""
    print(f"\n{'='*60}")
    print(f"  {title}")
    print(f"{'='*60}")

def print_test(test_name, status, details=""):
    """打印测试结果"""
    status_icon = "✅" if status else "❌"
    print(f"{status_icon} {test_name}")
    if details:
        print(f"   {details}")

def test_backend_apis():
    """测试后端API"""
    print_section("🔧 后端API测试")
    
    session = requests.Session()
    results = []
    
    # 1. 健康检查
    try:
        response = session.get(f"{BASE_URL}/api/health", timeout=5)
        success = response.status_code == 200
        details = f"状态码: {response.status_code}"
        if success:
            data = response.json()
            details += f", 版本: {data.get('version', 'unknown')}"
        print_test("健康检查API", success, details)
        results.append(success)
    except Exception as e:
        print_test("健康检查API", False, f"错误: {e}")
        results.append(False)
    
    # 2. 产品列表API
    try:
        response = session.get(f"{BASE_URL}/api/products", timeout=5)
        success = response.status_code == 200
        details = f"状态码: {response.status_code}"
        if success:
            data = response.json()
            details += f", 产品数量: {data.get('total', 0)}"
        print_test("产品列表API", success, details)
        results.append(success)
    except Exception as e:
        print_test("产品列表API", False, f"错误: {e}")
        results.append(False)
    
    # 3. 订单列表API
    try:
        response = session.get(f"{BASE_URL}/api/orders", timeout=5)
        success = response.status_code == 200
        details = f"状态码: {response.status_code}"
        if success:
            data = response.json()
            details += f", 订单数量: {data.get('total', 0)}"
        print_test("订单列表API", success, details)
        results.append(success)
    except Exception as e:
        print_test("订单列表API", False, f"错误: {e}")
        results.append(False)
    
    # 4. 管理员登录API
    try:
        login_data = {"username": "admin", "password": "admin123"}
        response = session.post(f"{BASE_URL}/api/auth/login", 
                              json=login_data, timeout=5)
        success = response.status_code == 200
        details = f"状态码: {response.status_code}"
        if success:
            data = response.json()
            details += f", 用户: {data.get('user', {}).get('username', 'unknown')}"
        print_test("管理员登录API", success, details)
        results.append(success)
    except Exception as e:
        print_test("管理员登录API", False, f"错误: {e}")
        results.append(False)
    
    return all(results)

def test_frontend_pages():
    """测试前端页面"""
    print_section("🌐 前端页面测试")
    
    pages = [
        ("优化版首页", "/frontend/index_standalone.html"),
        ("优化版购物车", "/frontend/cart_optimized.html"),
        ("优化版商品详情", "/frontend/detail_optimized.html?id=1"),
        ("优化版管理员登录", "/frontend/admin/login_optimized.html"),
        ("优化版管理后台", "/frontend/admin/admin_optimized.html"),
        ("优化版订单管理", "/frontend/admin/orders_optimized.html"),
        ("优化版系统状态", "/status_optimized.html"),
    ]
    
    results = []
    
    for page_name, page_path in pages:
        try:
            response = requests.get(f"{FRONTEND_URL}{page_path}", timeout=5)
            success = response.status_code == 200
            details = f"状态码: {response.status_code}"
            if success:
                content_length = len(response.content)
                details += f", 大小: {content_length} bytes"
            print_test(page_name, success, details)
            results.append(success)
        except Exception as e:
            print_test(page_name, False, f"错误: {e}")
            results.append(False)
    
    return all(results)

def test_crud_operations():
    """测试CRUD操作"""
    print_section("📝 CRUD操作测试")
    
    session = requests.Session()
    
    # 先登录
    try:
        login_data = {"username": "admin", "password": "admin123"}
        login_response = session.post(f"{BASE_URL}/api/auth/login", json=login_data)
        if login_response.status_code != 200:
            print_test("登录准备", False, "无法登录管理员账户")
            return False
        print_test("登录准备", True, "管理员登录成功")
    except Exception as e:
        print_test("登录准备", False, f"登录失败: {e}")
        return False
    
    test_product_id = None
    results = []
    
    # 1. 创建产品
    try:
        product_data = {
            "name": f"测试产品_{int(time.time())}",
            "price": 99.99,
            "stock": 100,
            "description": "这是一个自动化测试创建的产品"
        }
        response = session.post(f"{BASE_URL}/api/products", json=product_data)
        success = response.status_code == 201
        details = f"状态码: {response.status_code}"
        if success:
            data = response.json()
            test_product_id = data.get('product', {}).get('id')
            details += f", 产品ID: {test_product_id}"
        print_test("创建产品", success, details)
        results.append(success)
    except Exception as e:
        print_test("创建产品", False, f"错误: {e}")
        results.append(False)
    
    # 2. 读取产品
    if test_product_id:
        try:
            response = session.get(f"{BASE_URL}/api/products/{test_product_id}")
            success = response.status_code == 200
            details = f"状态码: {response.status_code}"
            if success:
                data = response.json()
                details += f", 产品名: {data.get('name', 'unknown')}"
            print_test("读取产品", success, details)
            results.append(success)
        except Exception as e:
            print_test("读取产品", False, f"错误: {e}")
            results.append(False)
    
    # 3. 更新产品
    if test_product_id:
        try:
            update_data = {
                "name": f"更新的测试产品_{int(time.time())}",
                "price": 199.99,
                "stock": 50,
                "description": "这是更新后的产品描述"
            }
            response = session.put(f"{BASE_URL}/api/products/{test_product_id}", 
                                 json=update_data)
            success = response.status_code == 200
            details = f"状态码: {response.status_code}"
            print_test("更新产品", success, details)
            results.append(success)
        except Exception as e:
            print_test("更新产品", False, f"错误: {e}")
            results.append(False)
    
    # 4. 删除产品
    if test_product_id:
        try:
            response = session.delete(f"{BASE_URL}/api/products/{test_product_id}")
            success = response.status_code == 200
            details = f"状态码: {response.status_code}"
            print_test("删除产品", success, details)
            results.append(success)
        except Exception as e:
            print_test("删除产品", False, f"错误: {e}")
            results.append(False)
    
    return all(results)

def test_performance():
    """测试性能"""
    print_section("⚡ 性能测试")
    
    results = []
    
    # 1. API响应时间
    try:
        start_time = time.time()
        response = requests.get(f"{BASE_URL}/api/products", timeout=10)
        end_time = time.time()
        
        response_time = (end_time - start_time) * 1000  # 转换为毫秒
        success = response.status_code == 200 and response_time < 1000  # 1秒内
        details = f"响应时间: {response_time:.2f}ms"
        print_test("API响应时间", success, details)
        results.append(success)
    except Exception as e:
        print_test("API响应时间", False, f"错误: {e}")
        results.append(False)
    
    # 2. 页面加载时间
    try:
        start_time = time.time()
        response = requests.get(f"{FRONTEND_URL}/frontend/index_standalone.html", timeout=10)
        end_time = time.time()
        
        load_time = (end_time - start_time) * 1000  # 转换为毫秒
        success = response.status_code == 200 and load_time < 2000  # 2秒内
        details = f"加载时间: {load_time:.2f}ms"
        print_test("页面加载时间", success, details)
        results.append(success)
    except Exception as e:
        print_test("页面加载时间", False, f"错误: {e}")
        results.append(False)
    
    return all(results)

def main():
    """主测试函数"""
    print("🛒 发卡商城 - 优化后功能测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行所有测试
    test_results = []
    
    test_results.append(("后端API", test_backend_apis()))
    test_results.append(("前端页面", test_frontend_pages()))
    test_results.append(("CRUD操作", test_crud_operations()))
    test_results.append(("性能测试", test_performance()))
    
    # 汇总结果
    print_section("📊 测试结果汇总")
    
    all_passed = True
    for test_name, result in test_results:
        print_test(test_name, result)
        if not result:
            all_passed = False
    
    print(f"\n{'='*60}")
    if all_passed:
        print("🎉 所有测试通过！系统运行正常。")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
    
    print(f"\n🌐 访问地址:")
    print(f"   商城首页: {FRONTEND_URL}/frontend/index_standalone.html")
    print(f"   购物车页面: {FRONTEND_URL}/frontend/cart_optimized.html")
    print(f"   管理员登录: {FRONTEND_URL}/frontend/admin/login_optimized.html")
    print(f"   系统状态: {FRONTEND_URL}/status_optimized.html")
    print(f"   API健康检查: {BASE_URL}/api/health")
    
    print(f"\n📋 默认管理员账户:")
    print(f"   用户名: admin")
    print(f"   密码: admin123")
    
    print(f"\n{'='*60}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {e}")
